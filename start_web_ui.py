#!/usr/bin/env python3
"""
启动Freqtrade Web UI
提供网页版交易界面
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['freqtrade', 'uvicorn', 'fastapi']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    return missing_packages

def install_web_dependencies():
    """安装Web UI依赖"""
    print("📦 安装Web UI依赖包...")
    
    packages = [
        'fastapi',
        'uvicorn[standard]',
        'python-multipart',
        'jinja2',
        'aiofiles'
    ]
    
    for package in packages:
        print(f"安装 {package}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")

def download_freqtrade_ui():
    """下载FreqUI（Web界面）"""
    print("🌐 下载FreqUI Web界面...")
    
    freqtrade_path = Path("freqtrade")
    if not freqtrade_path.exists():
        print("❌ Freqtrade目录不存在")
        return False
    
    try:
        # 使用freqtrade命令下载UI
        cmd = [sys.executable, "-m", "freqtrade", "install-ui"]
        result = subprocess.run(cmd, cwd=freqtrade_path, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ FreqUI下载成功")
            return True
        else:
            print(f"❌ FreqUI下载失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 下载FreqUI时出错: {e}")
        return False

def start_freqtrade_webserver():
    """启动Freqtrade Web服务器"""
    print("🚀 启动Freqtrade Web服务器...")
    
    freqtrade_path = Path("freqtrade")
    config_path = Path("web_config.json")
    
    if not config_path.exists():
        print("❌ 配置文件 web_config.json 不存在")
        return False
    
    try:
        # 启动webserver
        cmd = [
            sys.executable, "-m", "freqtrade", 
            "webserver",
            "--config", str(config_path.absolute())
        ]
        
        print("启动命令:", " ".join(cmd))
        print("=" * 50)
        print("🌐 Web界面将在以下地址启动:")
        print("📱 本地访问: http://localhost:8080")
        print("🔐 用户名: freqtrader")
        print("🔑 密码: freqtrade123")
        print("=" * 50)
        print("⚠️  注意: 当前为模拟交易模式（dry_run: true）")
        print("💡 要停止服务器，请按 Ctrl+C")
        print("=" * 50)
        
        # 等待3秒后自动打开浏览器
        print("3秒后自动打开浏览器...")
        time.sleep(3)
        webbrowser.open("http://localhost:8080")
        
        # 启动服务器
        subprocess.run(cmd, cwd=freqtrade_path)
        
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return False

def create_simple_strategy():
    """创建一个简单的交易策略"""
    strategy_dir = Path("freqtrade/user_data/strategies")
    strategy_dir.mkdir(parents=True, exist_ok=True)
    
    strategy_file = strategy_dir / "SimpleGridStrategy.py"
    
    if strategy_file.exists():
        print("✅ 策略文件已存在")
        return True
    
    strategy_content = '''
from freqtrade.strategy import IStrategy
from pandas import DataFrame
import talib.abstract as ta

class SimpleGridStrategy(IStrategy):
    """
    简单网格策略 - 适合200美金预算
    """
    
    # 策略参数
    INTERFACE_VERSION = 3
    minimal_roi = {
        "60": 0.01,
        "30": 0.02,
        "0": 0.04
    }
    
    stoploss = -0.10
    timeframe = '5m'
    
    # 可选订单类型
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """添加技术指标"""
        
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # 移动平均线
        dataframe['sma_20'] = ta.SMA(dataframe, timeperiod=20)
        dataframe['sma_50'] = ta.SMA(dataframe, timeperiod=50)
        
        # 布林带
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2)
        dataframe['bb_lower'] = bollinger['lowerband']
        dataframe['bb_upper'] = bollinger['upperband']
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """入场信号"""
        
        dataframe.loc[
            (
                (dataframe['rsi'] < 30) &  # RSI超卖
                (dataframe['close'] < dataframe['bb_lower']) &  # 价格低于布林带下轨
                (dataframe['volume'] > 0)  # 有交易量
            ),
            'enter_long'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """出场信号"""
        
        dataframe.loc[
            (
                (dataframe['rsi'] > 70) &  # RSI超买
                (dataframe['close'] > dataframe['bb_upper'])  # 价格高于布林带上轨
            ),
            'exit_long'] = 1
        
        return dataframe
'''
    
    try:
        with open(strategy_file, 'w', encoding='utf-8') as f:
            f.write(strategy_content)
        print("✅ 简单网格策略创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建策略失败: {e}")
        return False

def main():
    """主函数"""
    print("🌐 Freqtrade Web UI 启动器")
    print("=" * 50)
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print("安装缺失的依赖...")
        install_web_dependencies()
    
    # 创建策略
    create_simple_strategy()
    
    # 下载UI（如果需要）
    # download_freqtrade_ui()
    
    # 启动Web服务器
    start_freqtrade_webserver()

if __name__ == "__main__":
    main()
'''
