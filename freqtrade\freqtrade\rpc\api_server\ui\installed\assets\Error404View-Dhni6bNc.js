import{t as n,M as s,c as a,a as l,e,x as t,b as i,h as p}from"./index-Cwqm8wBn.js";const u={};function d(f,o){const r=s("RouterLink");return l(),a("div",null,[o[3]||(o[3]=e("h1",{class:"mb-5"},"404 Error.",-1)),o[4]||(o[4]=e("p",{class:"h4"},"Ahhhhhhhh! The page you are looking for does not exist.",-1)),e("p",null,[o[1]||(o[1]=t(" Don't worry, you can head back to ")),i(r,{to:"/"},{default:p(()=>o[0]||(o[0]=[e("span",null,"the main page",-1)])),_:1,__:[0]}),o[2]||(o[2]=t(". "))])])}const x=n(u,[["render",d]]);export{x as default};
//# sourceMappingURL=Error404View-Dhni6bNc.js.map
