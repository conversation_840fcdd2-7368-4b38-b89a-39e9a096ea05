import{a as F,b as H,_ as K}from"./TradesLogChart-DuJ-8U1F.js";import{c as Q}from"./TradeList.vue_vue_type_script_setup_true_lang--83SPrIm.js";import{b as X,c as Y}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";import{d as j,u as z,j as u,l as g,a as m,f as e,h as a,b as s,e as x,k as h,c as N,W as Z,x as S,z as T,i as ee,b3 as te,G as oe,D as se,J as ae,r as re,L as D,b4 as $,o as ne,N as P,M as U}from"./index-Cwqm8wBn.js";import{s as ie,a as le}from"./index-DhBpwJns.js";import{_ as de}from"./DraggableContainer.vue_vue_type_script_setup_true_lang-Q62jSh7o.js";import{_ as ue}from"./PeriodBreakdown.vue_vue_type_script_setup_true_lang-BFChaFcs.js";import"./installCanvasRenderer-SA1tPojE.js";import"./chartZoom-DB0tK3Do.js";import"./index-CONYmxgd.js";import"./index-D6LFPO4a.js";import"./index-BL9ilpkx.js";import"./index-ULt6J10p.js";import"./index-xjUaB_r9.js";import"./index-Bpiivi0c.js";import"./install-_krYdrE6.js";const ce={class:"flex flex-row justify-between items-center"},pe={key:2},me={key:0},fe=["title"],_e={class:"text-sm"},ye={key:0},he={class:"text-profit"},be={class:"text-loss"},ge=j({__name:"BotComparisonList",setup(A){const o=z(),b=u({get:()=>Object.values(o.botStores).every(d=>d.isSelected),set:d=>{for(const r in o.botStores)o.botStores[r].isSelected=d}}),R=u(()=>{const d=[],r={botId:void 0,botName:"Summary",profitClosed:0,profitClosedRatio:void 0,profitOpen:0,profitOpenRatio:void 0,stakeCurrency:"USDT",wins:0,losses:0};return Object.entries(o.allProfit).forEach(([n,i])=>{var t,c,p,w,C,V,v,B;const f=o.allOpenTrades[n].reduce((L,l)=>L+l.stake_amount,0),_=o.allOpenTrades[n].reduce((L,l)=>L+(l.total_profit_ratio??l.profit_ratio)*l.stake_amount,0)/f,y=o.allOpenTrades[n].reduce((L,l)=>L+(l.total_profit_abs??l.profit_abs??0),0);d.push({botId:n,botName:o.availableBots[n].botName||o.availableBots[n].botId,trades:`${o.allOpenTradeCount[n]} / ${((t=o.allBotState[n])==null?void 0:t.max_open_trades)||"N/A"}`,profitClosed:i.profit_closed_coin,profitClosedRatio:i.profit_closed_ratio||0,stakeCurrency:((c=o.allBotState[n])==null?void 0:c.stake_currency)||"",profitOpenRatio:_,profitOpen:y,wins:i.winning_trades,losses:i.losing_trades,balance:((p=o.allBalance[n])==null?void 0:p.total_bot)??((w=o.allBalance[n])==null?void 0:w.total),stakeCurrencyDecimals:((C=o.allBotState[n])==null?void 0:C.stake_currency_decimals)||3,isDryRun:(V=o.allBotState[n])==null?void 0:V.dry_run,isOnline:(v=o.botStores[n])==null?void 0:v.isBotOnline}),i.profit_closed_coin!==void 0&&o.botStores[n].isSelected&&(r.profitClosed+=i.profit_closed_coin,r.profitOpen+=y,r.wins+=i.winning_trades,r.losses+=i.losing_trades,r.stakeCurrency=((B=o.allBotState[n])==null?void 0:B.stake_currency)||r.stakeCurrency)}),d.push(r),d});return(d,r)=>{const n=Z,i=te,f=le,_=X,y=ie;return m(),g(y,{size:"small",value:e(R)},{default:a(()=>[s(f,{field:"botName",header:"Bot"},{body:a(({data:t,field:c})=>[x("div",ce,[x("div",null,[t.botId&&e(o).botCount>1?(m(),g(n,{key:0,modelValue:e(o).botStores[t.botId??""].isSelected,"onUpdate:modelValue":p=>e(o).botStores[t.botId??""].isSelected=p,title:"Show this bot in Dashboard"},{default:a(()=>[S(T(t[c]),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])):h("",!0),!t.botId&&e(o).botCount>1?(m(),g(n,{key:1,modelValue:e(b),"onUpdate:modelValue":r[0]||(r[0]=p=>ee(b)?b.value=p:null),title:"Toggle all bots",class:"font-bold"},{default:a(()=>[S(T(t[c]),1)]),_:2},1032,["modelValue"])):h("",!0),e(o).botCount<=1?(m(),N("span",pe,T(t[c]),1)):h("",!0)]),t.isOnline&&t.isDryRun?(m(),g(i,{key:0,class:"items-center bg-green-800 text-slate-200",severity:"success"},{default:a(()=>r[1]||(r[1]=[S("Dry")])),_:1,__:[1]})):h("",!0),t.isOnline&&!t.isDryRun?(m(),g(i,{key:1,class:"items-center",severity:"warning"},{default:a(()=>r[2]||(r[2]=[S("Live")])),_:1,__:[2]})):h("",!0),t.isOnline===!1?(m(),g(i,{key:2,class:"items-center",severity:"secondary"},{default:a(()=>r[3]||(r[3]=[S("Offline")])),_:1,__:[3]})):h("",!0)])]),_:1}),s(f,{field:"trades",header:"Trades"}),s(f,{header:"Open Profit"},{body:a(({data:t})=>[t.profitOpen&&t.botId!="Summary"?(m(),g(_,{key:0,"profit-ratio":t.profitOpenRatio,"profit-abs":t.profitOpen,"profit-desc":`Total Profit (Open and realized) ${("formatPercent"in d?d.formatPercent:e(oe))(t.profitOpenRatio??0)}`,"stake-currency":t.stakeCurrency},null,8,["profit-ratio","profit-abs","profit-desc","stake-currency"])):h("",!0)]),_:1}),s(f,{header:"Closed Profit"},{body:a(({data:t})=>[t.profitClosed&&t.botId!="Summary"?(m(),g(_,{key:0,"profit-ratio":t.profitClosedRatio,"profit-abs":t.profitClosed,"stake-currency":t.stakeCurrency},null,8,["profit-ratio","profit-abs","stake-currency"])):h("",!0)]),_:1}),s(f,{field:"balance",header:"Balance"},{body:a(({data:t})=>[t.balance?(m(),N("div",me,[x("span",{title:t.stakeCurrency},T(("formatPrice"in d?d.formatPrice:e(se))(t.balance??0,t.stakeCurrencyDecimals)),9,fe),x("span",_e,T(` ${t.stakeCurrency}${t.isDryRun?" (dry)":""}`),1)])):h("",!0)]),_:1}),s(f,{field:"winVsLoss",header:"W/L"},{body:a(({data:t})=>[t.losses!==void 0?(m(),N("div",ye,[x("span",he,T(t.wins),1),r[4]||(r[4]=S(" / ")),x("span",be,T(t.losses),1)])):h("",!0)]),_:1})]),_:1},8,["value"])}}}),we={class:"flex justify-content-center"},Ce={class:"flex justify-content-center"},Ue=j({__name:"DashboardView",setup(A){const o=z(),b=ae(),R=re("");function d(v){R.value=v}const r=u(()=>["","sm","md","lg","xl"].includes(R.value)),n=u(()=>b.layoutLocked||!r.value),i=u(()=>r.value?b.dashboardLayout:[...b.getDashboardLayoutSm]);function f(v){r.value&&(console.log("newlayout",v),console.log("saving dashboard"),b.dashboardLayout=v)}const _=u(()=>D(i.value,$.dailyChart)),y=u(()=>D(i.value,$.botComparison)),t=u(()=>D(i.value,$.allOpenTrades)),c=u(()=>D(i.value,$.allClosedTrades)),p=u(()=>D(i.value,$.cumChartChart)),w=u(()=>D(i.value,$.profitDistributionChart)),C=u(()=>D(i.value,$.tradesLogChart)),V=u(()=>({sm:b.getDashboardLayoutSm}));return ne(async()=>{o.allGetDaily({timescale:30}),o.activeBot.getOpenTrades(),o.activeBot.getProfit()}),(v,B)=>{const L=ue,l=de,O=U("GridItem"),E=ge,G=Y,I=Q,M=F,W=H,J=K,q=U("GridLayout");return m(),g(q,{class:"h-full w-full",style:{padding:"1px"},"row-height":50,layout:e(i),"vertical-compact":!1,margin:[2,2],"responsive-layouts":e(V),"is-resizable":!e(n),"is-draggable":!e(n),responsive:!0,"prevent-collision":!0,cols:{lg:12,md:12,sm:12,xs:4,xxs:2},"col-num":12,onLayoutUpdated:f,"onUpdate:breakpoint":d},{default:a(({gridItemProps:k})=>[s(O,P(k,{i:e(_).i,x:e(_).x,y:e(_).y,w:e(_).w,h:e(_).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,{header:`Profit over time ${e(o).botCount>1?"combined":""}`},{default:a(()=>[s(L,{"multi-bot-view":""})]),_:1},8,["header"])]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(y).i,x:e(y).x,y:e(y).y,w:e(y).w,h:e(y).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,{header:"Bot comparison"},{default:a(()=>[s(E)]),_:1})]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(t).i,x:e(t).x,y:e(t).y,w:e(t).w,h:e(t).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,null,{header:a(()=>[x("div",we,[B[0]||(B[0]=S(" Open Trades ")),s(G,{class:"ms-2",hint:"Open trades of all selected bots. Click on a trade to go to the trade page for that trade/bot."})])]),default:a(()=>[s(I,{"active-trades":"",trades:e(o).allOpenTradesSelectedBots,"multi-bot-view":""},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(p).i,x:e(p).x,y:e(p).y,w:e(p).w,h:e(p).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,{header:"Cumulative Profit"},{default:a(()=>[s(M,{trades:e(o).allTradesSelectedBots,"open-trades":e(o).allOpenTradesSelectedBots,"show-title":!1},null,8,["trades","open-trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(c).i,x:e(c).x,y:e(c).y,w:e(c).w,h:e(c).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,null,{header:a(()=>[x("div",Ce,[B[1]||(B[1]=S(" Closed Trades ")),s(G,{class:"ms-2",hint:"Closed trades for all selected bots. Click on a trade to go to the trade page for that trade/bot."})])]),default:a(()=>[s(I,{"active-trades":!1,"show-filter":"",trades:e(o).allClosedTradesSelectedBots,"multi-bot-view":""},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(w).i,x:e(w).x,y:e(w).y,w:e(w).w,h:e(w).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,{header:"Profit Distribution"},{default:a(()=>[s(W,{trades:e(o).allTradesSelectedBots,"show-title":!1},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"]),s(O,P(k,{i:e(C).i,x:e(C).x,y:e(C).y,w:e(C).w,h:e(C).h,"min-w":3,"min-h":4,"drag-allow-from":".drag-header"}),{default:a(()=>[s(l,{header:"Trades Log"},{default:a(()=>[s(J,{trades:e(o).allTradesSelectedBots,"show-title":!1},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])]),_:1},8,["layout","responsive-layouts","is-resizable","is-draggable"])}}});export{Ue as default};
//# sourceMappingURL=DashboardView-DcRsdFg4.js.map
