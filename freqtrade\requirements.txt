numpy==2.3.2
pandas==2.3.1
bottleneck==1.5.0
numexpr==2.11.0
# Indicator libraries
# ft-pandas-ta==0.3.15  # 暂时注释掉，Python 3.13不兼容
# ta-lib==0.5.5  # 将手动安装
# technical==1.5.2  # 暂时注释掉

ccxt==4.4.96
cryptography==45.0.5
aiohttp==3.12.14
SQLAlchemy==2.0.41
python-telegram-bot==22.3
# can't be hard-pinned due to telegram-bot pinning httpx with ~
httpx>=0.24.1
humanize==4.12.3
cachetools==6.1.0
requests==2.32.4
urllib3==2.5.0
certifi==2025.7.14
jsonschema==4.25.0
tabulate==0.9.0
pycoingecko==3.2.0
jinja2==3.1.6
joblib==1.5.1
rich==14.1.0
pyarrow==21.0.0; platform_machine != 'armv7l'

# Load ticker files 30% faster
python-rapidjson==1.21
# Properly format api responses
orjson==3.11.1

# Notify systemd
sdnotify==0.3.2

# API Server
fastapi==0.116.1
pydantic==2.11.7
uvicorn==0.35.0
pyjwt==2.10.1
aiofiles==24.1.0
psutil==7.0.0

# Building config files interactively
questionary==2.1.0
prompt-toolkit==3.0.51
# Extensions to datetime library
python-dateutil==2.9.0.post0
pytz==2025.2

#Futures
schedule==1.2.2

#WS Messages
websockets==15.0.1
janus==2.0.0

ast-comments==1.2.3
packaging==25.0
