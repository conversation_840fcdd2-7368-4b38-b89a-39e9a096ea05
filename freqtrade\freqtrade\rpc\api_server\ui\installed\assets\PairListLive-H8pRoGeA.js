import{_ as N}from"./check-circle-C_wO5mMc.js";import{s as D}from"./index-BL9ilpkx.js";import{d as O,r as _,u as U,o as W,c as i,a as s,e as l,b as r,k as g,f as o,F as b,m as k,z as h,v as F,l as I,g as T,n as B,h as p,B as j,w as q,s as E,i as R,x,t as G}from"./index-Cwqm8wBn.js";import{_ as H}from"./plus-box-outline-CDxaZbJP.js";const J={key:0,class:"list wide"},K=["title"],Q={key:0,class:"list"},X={key:1},Y={class:"flex flex-row justify-center mb-1"},Z={class:"flex flex-cols items-center gap-1 pe-1"},tt={class:"space-y-1"},et={class:"space-x-2"},st={key:2,class:"list"},lt=["title","onClick"],it={class:"check"},ot={key:3},at=O({__name:"PairListLive",setup(nt){const u=_(""),w=_(!1),v=_(),n=_([]),e=U();function y(){e.activeBot.whitelist.length===0&&e.activeBot.getWhitelist(),e.activeBot.blacklist.length===0&&e.activeBot.getBlacklist()}function P(){var a;u.value&&(w.value=!1,(a=v.value)==null||a.hide(),e.activeBot.addBlacklist({blacklist:[u.value]}),u.value="")}function C(a){var t;(t=v.value)==null||t.show(a)}function S(a){const t=n.value.indexOf(a);t>-1?n.value.splice(t,1):n.value.push(a)}function V(){if(n.value.length===0){console.log("nothing to delete");return}const a=e.activeBot.blacklist.filter((t,m)=>n.value.indexOf(m)>-1);console.log("Deleting pairs: ",a),e.activeBot.deleteBlacklist(a),n.value=[]}return W(()=>{y()}),(a,t)=>{const m=F,L=H,f=T,A=j,$=E,z=D,M=N;return s(),i("div",null,[l("div",null,[t[1]||(t[1]=l("h3",{class:"text-xl"},"Whitelist Methods",-1)),o(e).activeBot.pairlistMethods.length?(s(),i("ul",J,[(s(!0),i(b,null,k(o(e).activeBot.pairlistMethods,(c,d)=>(s(),i("li",{key:d,class:"pair bg-white text-black align-middle border border-secondary"},h(c),1))),128))])):g("",!0)]),l("h3",{class:"text-lg font-bold",title:`${o(e).activeBot.whitelist.length} pairs`}," Whitelist ",8,K),o(e).activeBot.whitelist.length?(s(),i("ul",Q,[(s(!0),i(b,null,k(o(e).activeBot.whitelist,(c,d)=>(s(),i("li",{key:d,class:"pair bg-white text-black align-middle border border-secondary"},h(c),1))),128))])):(s(),i("p",X,"List Unavailable. Please Login and make sure server is running.")),r(m),l("div",null,[l("div",Y,[t[2]||(t[2]=l("label",{class:"text-lg font-bold mb-2 w-full",title:"Blacklist - Select (followed by a click on '-') to remove pairs"}," Blacklist ",-1)),l("div",Z,[r(f,{ref:"blacklist-add-btn",severity:"secondary",class:B(o(e).activeBot.botApiVersion>=1.12?"col-6":""),size:"small",onClick:C},{icon:p(()=>[r(L)]),_:1},8,["class"]),o(e).activeBot.botApiVersion>=1.12?(s(),I(f,{key:0,size:"small",severity:"secondary",title:"Select pairs to delete pairs from your blacklist.",disabled:o(n).length===0,onClick:V},{icon:p(()=>[r(A)]),_:1},8,["disabled"])):g("",!0)])]),r(z,{ref_key:"blacklistAddPopover",ref:v,class:"p-1"},{default:p(()=>[l("form",{ref:"form",onSubmit:q(P,["prevent"])},[l("div",tt,[t[5]||(t[5]=l("h4",{class:"font-bold mb-2"},"Add Pair to Blacklist",-1)),l("div",et,[t[3]||(t[3]=l("label",{for:"pair-input"},"Pair",-1)),r($,{id:"pair-input",modelValue:o(u),"onUpdate:modelValue":t[0]||(t[0]=c=>R(u)?u.value=c:null),required:"",autofocus:""},null,8,["modelValue"])]),r(f,{id:"blacklist-submit",class:"float-end mb-2",size:"small",severity:"primary",type:"submit"},{default:p(()=>t[4]||(t[4]=[x(" Add ")])),_:1,__:[4]})])],544)]),_:1},512)]),o(e).activeBot.blacklist.length?(s(),i("ul",st,[(s(!0),i(b,null,k(o(e).activeBot.blacklist,(c,d)=>(s(),i("li",{key:d,class:B(["pair bg-black text-white text-ellipsis overflow-hidden",o(n).indexOf(d)>-1?"active":""]),title:c,onClick:ct=>S(d)},[l("span",it,[r(M)]),x(h(c),1)],10,lt))),128))])):(s(),i("p",ot,"BlackList Unavailable. Please Login and make sure server is running."))])}}}),pt=G(at,[["__scopeId","data-v-d8a70cd1"]]);export{pt as default};
//# sourceMappingURL=PairListLive-H8pRoGeA.js.map
