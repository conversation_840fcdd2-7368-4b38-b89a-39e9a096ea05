import{cb as Se,cc as pa,cd as ga,ce as nn,cf as gt,cg as rn,d as qe,ch as Fa,r as Z,a_ as et,o as Qe,bb as oa,ci as Qa,U as na,j as W,c as H,a as M,b as ta,l as Te,aS as Je,m as Ve,h as ke,a2 as ie,aR as je,cj as la,f as o,N as We,i as mt,Y as Za,k as q,ad as bt,ck as un,n as De,bM as on,cl as nt,bI as sn,cm as sl,e as me,w as at,cn as ct,F as _e,bh as sa,z as Ze,c8 as dn,co as cn,cp as wa,cq as vn,x as _a,cr as qt,cs as Et,ct as pn,cu as mn,cv as fn,V as vt,X as pt,ba as yn,cw as ft,cx as hn,cy as gn,cz as bn,K as wn,cA as il,b8 as Rt,cB as dl}from"./index-Cwqm8wBn.js";function va(e,a,n){const t=Se(e,n==null?void 0:n.in);if(isNaN(a))return pa(e,NaN);if(!a)return t;const l=t.getDate(),s=pa(e,t.getTime());s.setMonth(t.getMonth()+a+1,0);const i=s.getDate();return l>=i?s:(t.setFullYear(s.getFullYear(),s.getMonth(),l),t)}function $l(e,a,n){const{years:t=0,months:l=0,weeks:s=0,days:i=0,hours:f=0,minutes:d=0,seconds:T=0}=a,p=Se(e,n==null?void 0:n.in),v=l||t?va(p,l+t*12):p,m=i||s?ga(v,i+s*7):v,k=d+f*60,Y=(T+k*60)*1e3;return pa(e,+m+Y)}function _n(e,a,n){return pa(e,+Se(e)+a)}function kn(e,a,n){return _n(e,a*nn)}function Dn(e,a,n){return va(e,a*3,n)}function Qt(e,a,n){return va(e,a*12,n)}function cl(e,a){const n=+Se(e)-+Se(a);return n<0?-1:n>0?1:n}function vl(e,a){const n=Se(e,a==null?void 0:a.in);return Math.trunc(n.getMonth()/3)+1}function Mn(e,a,n){const[t,l]=gt(n==null?void 0:n.in,e,a);return t.getFullYear()-l.getFullYear()}function $n(e,a,n){const[t,l]=gt(n==null?void 0:n.in,e,a),s=cl(t,l),i=Math.abs(Mn(t,l));t.setFullYear(1584),l.setFullYear(1584);const f=cl(t,l)===-s,d=s*(i-+f);return d===0?0:d}function Al(e,a){const[n,t]=gt(e,a.start,a.end);return{start:n,end:t}}function Tl(e,a){const{start:n,end:t}=Al(a==null?void 0:a.in,e);let l=+n>+t;const s=l?+n:+t,i=l?t:n;i.setHours(0,0,0,0);let f=1;const d=[];for(;+i<=s;)d.push(pa(n,i)),i.setDate(i.getDate()+f),i.setHours(0,0,0,0);return l?d.reverse():d}function Va(e,a){const n=Se(e,a==null?void 0:a.in),t=n.getMonth(),l=t-t%3;return n.setMonth(l,1),n.setHours(0,0,0,0),n}function An(e,a){const{start:n,end:t}=Al(a==null?void 0:a.in,e);let l=+n>+t;const s=l?+Va(n):+Va(t);let i=Va(l?t:n),f=1;const d=[];for(;+i<=s;)d.push(pa(n,i)),i=Dn(i,f);return l?d.reverse():d}function Tn(e,a){const n=Se(e,a==null?void 0:a.in);return n.setDate(1),n.setHours(0,0,0,0),n}function Pl(e,a){const n=Se(e,a==null?void 0:a.in),t=n.getFullYear();return n.setFullYear(t+1,0,0),n.setHours(23,59,59,999),n}function Sl(e,a){var f,d,T,p;const n=rn(),t=(a==null?void 0:a.weekStartsOn)??((d=(f=a==null?void 0:a.locale)==null?void 0:f.options)==null?void 0:d.weekStartsOn)??n.weekStartsOn??((p=(T=n.locale)==null?void 0:T.options)==null?void 0:p.weekStartsOn)??0,l=Se(e,a==null?void 0:a.in),s=l.getDay(),i=(s<t?-7:0)+6-(s-t);return l.setDate(l.getDate()+i),l.setHours(23,59,59,999),l}function pl(e,a){const n=Se(e,a==null?void 0:a.in),t=n.getMonth(),l=t-t%3+3;return n.setMonth(l,0),n.setHours(23,59,59,999),n}function Pn(e,a){return Se(e,a==null?void 0:a.in).getDay()}function Sn(e,a){const n=Se(e,a==null?void 0:a.in),t=n.getFullYear(),l=n.getMonth(),s=pa(n,0);return s.setFullYear(t,l+1,0),s.setHours(0,0,0,0),s.getDate()}function $a(e,a){return Se(e,a==null?void 0:a.in).getHours()}function Ra(e,a){return Se(e,a==null?void 0:a.in).getMinutes()}function $e(e,a){return Se(e,a==null?void 0:a.in).getMonth()}function Ha(e){return Se(e).getSeconds()}function he(e,a){return Se(e,a==null?void 0:a.in).getFullYear()}function Na(e,a){return+Se(e)>+Se(a)}function ja(e,a){return+Se(e)<+Se(a)}function Ua(e,a){return+Se(e)==+Se(a)}function ml(e,a,n){const[t,l]=gt(n==null?void 0:n.in,e,a);return+Va(t)==+Va(l)}function xl(e,a,n){return ga(e,-a,n)}function Rl(e,a,n){const t=Se(e,n==null?void 0:n.in),l=t.getFullYear(),s=t.getDate(),i=pa(e,0);i.setFullYear(l,a,15),i.setHours(0,0,0,0);const f=Sn(i);return t.setMonth(a,Math.min(s,f)),t}function Oe(e,a,n){let t=Se(e,n==null?void 0:n.in);return isNaN(+t)?pa(e,NaN):(a.year!=null&&t.setFullYear(a.year),a.month!=null&&(t=Rl(t,a.month)),a.date!=null&&t.setDate(a.date),a.hours!=null&&t.setHours(a.hours),a.minutes!=null&&t.setMinutes(a.minutes),a.seconds!=null&&t.setSeconds(a.seconds),a.milliseconds!=null&&t.setMilliseconds(a.milliseconds),t)}function xn(e,a,n){const t=Se(e,n==null?void 0:n.in);return t.setHours(a),t}function Ol(e,a,n){const t=Se(e,n==null?void 0:n.in);return t.setMilliseconds(a),t}function Rn(e,a,n){const t=Se(e,n==null?void 0:n.in);return t.setMinutes(a),t}function Cl(e,a,n){const t=Se(e,n==null?void 0:n.in);return t.setSeconds(a),t}function ba(e,a,n){const t=Se(e,n==null?void 0:n.in);return isNaN(+t)?pa(e,NaN):(t.setFullYear(a),t)}function Wa(e,a,n){return va(e,-a,n)}function On(e,a,n){const{years:t=0,months:l=0,weeks:s=0,days:i=0,hours:f=0,minutes:d=0,seconds:T=0}=a,p=Wa(e,l+t*12,n),v=xl(p,i+s*7,n),m=d+f*60,R=(T+m*60)*1e3;return pa(e,+v-R)}function Yl(e,a,n){return Qt(e,-a,n)}function Ga(){const e=vn();return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img",...e},[me("path",{d:"M29.333 8c0-2.208-1.792-4-4-4h-18.667c-2.208 0-4 1.792-4 4v18.667c0 2.208 1.792 4 4 4h18.667c2.208 0 4-1.792 4-4v-18.667zM26.667 8v18.667c0 0.736-0.597 1.333-1.333 1.333 0 0-18.667 0-18.667 0-0.736 0-1.333-0.597-1.333-1.333 0 0 0-18.667 0-18.667 0-0.736 0.597-1.333 1.333-1.333 0 0 18.667 0 18.667 0 0.736 0 1.333 0.597 1.333 1.333z"}),me("path",{d:"M20 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),me("path",{d:"M9.333 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),me("path",{d:"M4 14.667h24c0.736 0 1.333-0.597 1.333-1.333s-0.597-1.333-1.333-1.333h-24c-0.736 0-1.333 0.597-1.333 1.333s0.597 1.333 1.333 1.333z"})])}Ga.compatConfig={MODE:3};function Vl(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M23.057 7.057l-16 16c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l16-16c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0z"}),me("path",{d:"M7.057 8.943l16 16c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885l-16-16c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}Vl.compatConfig={MODE:3};function Zt(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M20.943 23.057l-7.057-7.057c0 0 7.057-7.057 7.057-7.057 0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-8 8c-0.521 0.521-0.521 1.365 0 1.885l8 8c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}Zt.compatConfig={MODE:3};function Gt(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M12.943 24.943l8-8c0.521-0.521 0.521-1.365 0-1.885l-8-8c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885l7.057 7.057c0 0-7.057 7.057-7.057 7.057-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0z"})])}Gt.compatConfig={MODE:3};function Xt(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M16 1.333c-8.095 0-14.667 6.572-14.667 14.667s6.572 14.667 14.667 14.667c8.095 0 14.667-6.572 14.667-14.667s-6.572-14.667-14.667-14.667zM16 4c6.623 0 12 5.377 12 12s-5.377 12-12 12c-6.623 0-12-5.377-12-12s5.377-12 12-12z"}),me("path",{d:"M14.667 8v8c0 0.505 0.285 0.967 0.737 1.193l5.333 2.667c0.658 0.329 1.46 0.062 1.789-0.596s0.062-1.46-0.596-1.789l-4.596-2.298c0 0 0-7.176 0-7.176 0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"})])}Xt.compatConfig={MODE:3};function Jt(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M24.943 19.057l-8-8c-0.521-0.521-1.365-0.521-1.885 0l-8 8c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l7.057-7.057c0 0 7.057 7.057 7.057 7.057 0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}Jt.compatConfig={MODE:3};function el(){return M(),H("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[me("path",{d:"M7.057 12.943l8 8c0.521 0.521 1.365 0.521 1.885 0l8-8c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-7.057 7.057c0 0-7.057-7.057-7.057-7.057-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}el.compatConfig={MODE:3};const ra=(e,a)=>a?new Date(e.toLocaleString("en-US",{timeZone:a})):new Date(e),al=(e,a,n)=>Ht(e,a,n)||j(),Cn=(e,a,n)=>{const t=a.dateInTz?ra(new Date(e),a.dateInTz):j(e);return n?Ke(t,!0):t},Ht=(e,a,n)=>{if(!e)return null;const t=n?Ke(j(e),!0):j(e);return a?a.exactMatch?Cn(e,a,n):ra(t,a.timezone):t},Yn=e=>{const a=new Date(e.getFullYear(),0,1).getTimezoneOffset();return e.getTimezoneOffset()<a},Vn=(e,a)=>{if(!e)return 0;const n=new Date,t=new Date(n.toLocaleString("en-US",{timeZone:"UTC"})),l=new Date(n.toLocaleString("en-US",{timeZone:e})),s=(Yn(a??l)?l:a??l).getTimezoneOffset()/60;return(+t-+l)/(1e3*60*60)-s};var ia=(e=>(e.month="month",e.year="year",e))(ia||{}),da=(e=>(e.top="top",e.bottom="bottom",e))(da||{}),Ba=(e=>(e.header="header",e.calendar="calendar",e.timePicker="timePicker",e))(Ba||{}),Xe=(e=>(e.month="month",e.year="year",e.calendar="calendar",e.time="time",e.minutes="minutes",e.hours="hours",e.seconds="seconds",e))(Xe||{});const Bn=["timestamp","date","iso"];var ea=(e=>(e.up="up",e.down="down",e.left="left",e.right="right",e))(ea||{}),Ye=(e=>(e.arrowUp="ArrowUp",e.arrowDown="ArrowDown",e.arrowLeft="ArrowLeft",e.arrowRight="ArrowRight",e.enter="Enter",e.space=" ",e.esc="Escape",e.tab="Tab",e.home="Home",e.end="End",e.pageUp="PageUp",e.pageDown="PageDown",e))(Ye||{}),Ea=(e=>(e.MONTH_AND_YEAR="MM-yyyy",e.YEAR="yyyy",e.DATE="dd-MM-yyyy",e))(Ea||{});function fl(e){return a=>{const n=new Intl.DateTimeFormat(e,{weekday:"short",timeZone:"UTC"}).format(new Date(`2017-01-0${a}T00:00:00+00:00`));return e==="ar"?n.slice(2,5):n.slice(0,2)}}function In(e){return a=>wa(ra(new Date(`2017-01-0${a}T00:00:00+00:00`),"UTC"),"EEEEEE",{locale:e})}const Nn=(e,a,n)=>{const t=[1,2,3,4,5,6,7];let l;if(e!==null)try{l=t.map(In(e))}catch{l=t.map(fl(a))}else l=t.map(fl(a));const s=l.slice(0,n),i=l.slice(n+1,l.length);return[l[n]].concat(...i).concat(...s)},tl=(e,a,n)=>{const t=[];for(let l=+e[0];l<=+e[1];l++)t.push({value:+l,text:Fl(l,a)});return n?t.reverse():t},Bl=(e,a,n)=>{const t=[1,2,3,4,5,6,7,8,9,10,11,12].map(s=>{const i=s<10?`0${s}`:s;return new Date(`2017-${i}-01T00:00:00+00:00`)});if(e!==null)try{const s=n==="long"?"LLLL":"LLL";return t.map((i,f)=>{const d=wa(ra(i,"UTC"),s,{locale:e});return{text:d.charAt(0).toUpperCase()+d.substring(1),value:f}})}catch{}const l=new Intl.DateTimeFormat(a,{month:n,timeZone:"UTC"});return t.map((s,i)=>{const f=l.format(s);return{text:f.charAt(0).toUpperCase()+f.substring(1),value:i}})},Fn=e=>[12,1,2,3,4,5,6,7,8,9,10,11,12,1,2,3,4,5,6,7,8,9,10,11][e],ze=e=>{const a=o(e);return a!=null&&a.$el?a==null?void 0:a.$el:a},Ln=e=>({type:"dot",...e??{}}),Il=e=>Array.isArray(e)?!!e[0]&&!!e[1]:!1,ll={prop:e=>`"${e}" prop must be enabled!`,dateArr:e=>`You need to use array as "model-value" binding in order to support "${e}"`},He=e=>e,yl=e=>e===0?e:!e||isNaN(+e)?null:+e,hl=e=>e===null,Nl=e=>{if(e)return[...e.querySelectorAll("input, button, select, textarea, a[href]")][0]},zn=e=>{const a=[],n=t=>t.filter(l=>l);for(let t=0;t<e.length;t+=3){const l=[e[t],e[t+1],e[t+2]];a.push(n(l))}return a},tt=(e,a,n)=>{const t=n!=null,l=a!=null;if(!t&&!l)return!1;const s=+n,i=+a;return t&&l?+e>s||+e<i:t?+e>s:l?+e<i:!1},Ka=(e,a)=>zn(e).map(n=>n.map(t=>{const{active:l,disabled:s,isBetween:i,highlighted:f}=a(t);return{...t,active:l,disabled:s,className:{dp__overlay_cell_active:l,dp__overlay_cell:!l,dp__overlay_cell_disabled:s,dp__overlay_cell_pad:!0,dp__overlay_cell_active_disabled:s&&l,dp__cell_in_between:i,"dp--highlighted":f}}})),Sa=(e,a,n=!1)=>{e&&a.allowStopPropagation&&(n&&e.stopImmediatePropagation(),e.stopPropagation())},Un=()=>["a[href]","area[href]","input:not([disabled]):not([type='hidden'])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","[tabindex]:not([tabindex='-1'])","[data-datepicker-instance]"].join(", ");function En(e,a){let n=[...document.querySelectorAll(Un())];n=n.filter(l=>!e.contains(l)||l.hasAttribute("data-datepicker-instance"));const t=n.indexOf(e);if(t>=0&&(a?t-1>=0:t+1<=n.length))return n[t+(a?-1:1)]}const jt=(e,a)=>e==null?void 0:e.querySelector(`[data-dp-element="${a}"]`),Fl=(e,a)=>new Intl.NumberFormat(a,{useGrouping:!1,style:"decimal"}).format(e),nl=(e,a)=>wa(e,a??Ea.DATE),Ot=e=>Array.isArray(e),yt=(e,a,n)=>a.get(nl(e,n)),Hn=(e,a)=>e?a?a instanceof Map?!!yt(e,a):a(j(e)):!1:!0,aa=(e,a,n=!1,t)=>{if(e.key===Ye.enter||e.key===Ye.space)return n&&e.preventDefault(),a();if(t)return t(e)},jn=()=>"ontouchstart"in window||navigator.maxTouchPoints>0,Ll=(e,a)=>e?Ea.MONTH_AND_YEAR:a?Ea.YEAR:Ea.DATE,zl=e=>e<10?`0${e}`:e,gl=(e,a,n,t,l,s)=>{const i=Et(e,a.slice(0,e.length),new Date,{locale:s});return ct(i)&&bn(i)?t||l?i:Oe(i,{hours:+n.hours,minutes:+(n==null?void 0:n.minutes),seconds:+(n==null?void 0:n.seconds),milliseconds:0}):null},Wn=(e,a,n,t,l,s)=>{const i=Array.isArray(n)?n[0]:n;if(typeof a=="string")return gl(e,a,i,t,l,s);if(Array.isArray(a)){let f=null;for(const d of a)if(f=gl(e,d,i,t,l,s),f)break;return f}return typeof a=="function"?a(e):null},j=e=>e?new Date(e):new Date,Kn=(e,a,n)=>{if(a){const l=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),i=e.getHours().toString().padStart(2,"0"),f=e.getMinutes().toString().padStart(2,"0"),d=n?e.getSeconds().toString().padStart(2,"0"):"00";return`${e.getFullYear()}-${l}-${s}T${i}:${f}:${d}.000Z`}const t=Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds());return new Date(t).toISOString()},Ke=(e,a)=>{const n=j(JSON.parse(JSON.stringify(e))),t=Oe(n,{hours:0,minutes:0,seconds:0,milliseconds:0});return a?Tn(t):t},xa=(e,a,n,t)=>{let l=e?j(e):j();return(a||a===0)&&(l=xn(l,+a)),(n||n===0)&&(l=Rn(l,+n)),(t||t===0)&&(l=Cl(l,+t)),Ol(l,0)},Ie=(e,a)=>!e||!a?!1:ja(Ke(e),Ke(a)),Ae=(e,a)=>!e||!a?!1:Ua(Ke(e),Ke(a)),Le=(e,a)=>!e||!a?!1:Na(Ke(e),Ke(a)),lt=(e,a,n)=>e!=null&&e[0]&&e!=null&&e[1]?Le(n,e[0])&&Ie(n,e[1]):e!=null&&e[0]&&a?Le(n,e[0])&&Ie(n,a)||Ie(n,e[0])&&Le(n,a):!1,ca=e=>{const a=Oe(new Date(e),{date:1});return Ke(a)},Ct=(e,a,n)=>a&&(n||n===0)?Object.fromEntries(["hours","minutes","seconds"].map(t=>t===a?[t,n]:[t,isNaN(+e[t])?void 0:+e[t]])):{hours:isNaN(+e.hours)?void 0:+e.hours,minutes:isNaN(+e.minutes)?void 0:+e.minutes,seconds:isNaN(+e.seconds)?void 0:+e.seconds},Ia=e=>({hours:$a(e),minutes:Ra(e),seconds:Ha(e)}),Ul=(e,a)=>{if(a){const n=he(j(a));if(n>e)return 12;if(n===e)return $e(j(a))}},El=(e,a)=>{if(a){const n=he(j(a));return n<e?-1:n===e?$e(j(a)):void 0}},qa=e=>{if(e)return he(j(e))},Hl=(e,a)=>{const n=Le(e,a)?a:e,t=Le(a,e)?a:e;return Tl({start:n,end:t})},qn=e=>{const a=va(e,1);return{month:$e(a),year:he(a)}},Da=(e,a)=>{const n=qt(e,{weekStartsOn:+a}),t=Sl(e,{weekStartsOn:+a});return[n,t]},jl=(e,a)=>{const n={hours:$a(j()),minutes:Ra(j()),seconds:a?Ha(j()):0};return Object.assign(n,e)},Pa=(e,a,n)=>[Oe(j(e),{date:1}),Oe(j(),{month:a,year:n,date:1})],Ma=(e,a,n)=>{let t=e?j(e):j();return(a||a===0)&&(t=Rl(t,a)),n&&(t=ba(t,n)),t},Wl=(e,a,n,t,l)=>{if(!t||l&&!a||!l&&!n)return!1;const s=l?va(e,1):Wa(e,1),i=[$e(s),he(s)];return l?!Zn(...i,a):!Qn(...i,n)},Qn=(e,a,n)=>Ie(...Pa(n,e,a))||Ae(...Pa(n,e,a)),Zn=(e,a,n)=>Le(...Pa(n,e,a))||Ae(...Pa(n,e,a)),Kl=(e,a,n,t,l,s,i)=>{if(typeof a=="function"&&!i)return a(e);const f=n?{locale:n}:void 0;return Array.isArray(e)?`${wa(e[0],s,f)}${l&&!e[1]?"":t}${e[1]?wa(e[1],s,f):""}`:wa(e,s,f)},La=e=>{if(e)return null;throw new Error(ll.prop("partial-range"))},it=(e,a)=>{if(a)return e();throw new Error(ll.prop("range"))},Wt=e=>Array.isArray(e)?ct(e[0])&&(e[1]?ct(e[1]):!0):e?ct(e):!1,Gn=(e,a)=>Oe(a??j(),{hours:+e.hours||0,minutes:+e.minutes||0,seconds:+e.seconds||0}),Yt=(e,a,n,t)=>{if(!e)return!0;if(t){const l=n==="max"?ja(e,a):Na(e,a),s={seconds:0,milliseconds:0};return l||Ua(Oe(e,s),Oe(a,s))}return n==="max"?e.getTime()<=a.getTime():e.getTime()>=a.getTime()},Vt=(e,a,n)=>e?Gn(e,a):j(n??a),bl=(e,a,n,t,l)=>{if(Array.isArray(t)){const i=Vt(e,t[0],a),f=Vt(e,t[1],a);return Yt(t[0],i,n,!!a)&&Yt(t[1],f,n,!!a)&&l}const s=Vt(e,t,a);return Yt(t,s,n,!!a)&&l},Bt=e=>Oe(j(),Ia(e)),Xn=(e,a,n)=>{if(e instanceof Map){const t=`${zl(n+1)}-${a}`;return e.size?e.has(t):!1}return typeof e=="function"?e(Ke(Oe(j(),{month:n,year:a}),!0)):!1},Jn=(e,a,n)=>{if(e instanceof Map){const t=`${zl(n+1)}-${a}`;return e.size?e.has(t):!0}return!0},ql=(e,a,n)=>typeof e=="function"?e({month:a,year:n}):!!e.months.find(t=>t.month===a&&t.year===n),rl=(e,a)=>typeof e=="function"?e(a):e.years.includes(a),Kt=e=>`dp-${wa(e,"yyyy-MM-dd")}`,wl=(e,a)=>{const n=xl(Ke(a),e),t=ga(Ke(a),e);return{before:n,after:t}},Xa=nt({menuFocused:!1,shiftKeyInMenu:!1}),Ql=()=>{const e=n=>{Xa.menuFocused=n},a=n=>{Xa.shiftKeyInMenu!==n&&(Xa.shiftKeyInMenu=n)};return{control:W(()=>({shiftKeyInMenu:Xa.shiftKeyInMenu,menuFocused:Xa.menuFocused})),setMenuFocused:e,setShiftKey:a}},Ce=nt({monthYear:[],calendar:[],time:[],actionRow:[],selectionGrid:[],timePicker:{0:[],1:[]},monthPicker:[]}),It=Z(null),dt=Z(!1),Nt=Z(!1),Ft=Z(!1),Lt=Z(!1),Ge=Z(0),Fe=Z(0),Oa=()=>{const e=W(()=>dt.value?[...Ce.selectionGrid,Ce.actionRow].filter(v=>v.length):Nt.value?[...Ce.timePicker[0],...Ce.timePicker[1],Lt.value?[]:[It.value],Ce.actionRow].filter(v=>v.length):Ft.value?[...Ce.monthPicker,Ce.actionRow]:[Ce.monthYear,...Ce.calendar,Ce.time,Ce.actionRow].filter(v=>v.length)),a=v=>{Ge.value=v?Ge.value+1:Ge.value-1;let m=null;e.value[Fe.value]&&(m=e.value[Fe.value][Ge.value]),!m&&e.value[Fe.value+(v?1:-1)]?(Fe.value=Fe.value+(v?1:-1),Ge.value=v?0:e.value[Fe.value].length-1):m||(Ge.value=v?Ge.value-1:Ge.value+1)},n=v=>{Fe.value===0&&!v||Fe.value===e.value.length&&v||(Fe.value=v?Fe.value+1:Fe.value-1,e.value[Fe.value]?e.value[Fe.value]&&!e.value[Fe.value][Ge.value]&&Ge.value!==0&&(Ge.value=e.value[Fe.value].length-1):Fe.value=v?Fe.value-1:Fe.value+1)},t=v=>{let m=null;e.value[Fe.value]&&(m=e.value[Fe.value][Ge.value]),m?m.focus({preventScroll:!dt.value}):Ge.value=v?Ge.value-1:Ge.value+1},l=()=>{a(!0),t(!0)},s=()=>{a(!1),t(!1)},i=()=>{n(!1),t(!0)},f=()=>{n(!0),t(!0)},d=(v,m)=>{Ce[m]=v},T=(v,m)=>{Ce[m]=v},p=()=>{Ge.value=0,Fe.value=0};return{buildMatrix:d,buildMultiLevelMatrix:T,setTimePickerBackRef:v=>{It.value=v},setSelectionGrid:v=>{dt.value=v,p(),v||(Ce.selectionGrid=[])},setTimePicker:(v,m=!1)=>{Nt.value=v,Lt.value=m,p(),v||(Ce.timePicker[0]=[],Ce.timePicker[1]=[])},setTimePickerElements:(v,m=0)=>{Ce.timePicker[m]=v},arrowRight:l,arrowLeft:s,arrowUp:i,arrowDown:f,clearArrowNav:()=>{Ce.monthYear=[],Ce.calendar=[],Ce.time=[],Ce.actionRow=[],Ce.selectionGrid=[],Ce.timePicker[0]=[],Ce.timePicker[1]=[],dt.value=!1,Nt.value=!1,Lt.value=!1,Ft.value=!1,p(),It.value=null},setMonthPicker:v=>{Ft.value=v,p()},refSets:Ce}},_l=e=>({menuAppearTop:"dp-menu-appear-top",menuAppearBottom:"dp-menu-appear-bottom",open:"dp-slide-down",close:"dp-slide-up",next:"calendar-next",previous:"calendar-prev",vNext:"dp-slide-up",vPrevious:"dp-slide-down",...e??{}}),er=e=>({toggleOverlay:"Toggle overlay",menu:"Datepicker menu",input:"Datepicker input",openTimePicker:"Open time picker",closeTimePicker:"Close time Picker",incrementValue:a=>`Increment ${a}`,decrementValue:a=>`Decrement ${a}`,openTpOverlay:a=>`Open ${a} overlay`,amPmButton:"Switch AM/PM mode",openYearsOverlay:"Open years overlay",openMonthsOverlay:"Open months overlay",nextMonth:"Next month",prevMonth:"Previous month",nextYear:"Next year",prevYear:"Previous year",day:void 0,weekDay:void 0,clearInput:"Clear value",calendarIcon:"Calendar icon",timePicker:"Time picker",monthPicker:a=>`Month picker${a?" overlay":""}`,yearPicker:a=>`Year picker${a?" overlay":""}`,timeOverlay:a=>`${a} overlay`,...e??{}}),kl=e=>e?typeof e=="boolean"?e?2:0:+e>=2?+e:2:0,ar=e=>{const a=typeof e=="object"&&e,n={static:!0,solo:!1};if(!e)return{...n,count:kl(!1)};const t=a?e:{},l=a?t.count??!0:e,s=kl(l);return Object.assign(n,t,{count:s})},tr=(e,a,n)=>e||(typeof n=="string"?n:a),lr=e=>typeof e=="boolean"?e?_l({}):!1:_l(e),nr=e=>{const a={enterSubmit:!0,tabSubmit:!0,openMenu:"open",selectOnFocus:!1,rangeSeparator:" - ",escClose:!0};return typeof e=="object"?{...a,...e??{},enabled:!0}:{...a,enabled:e}},rr=e=>({months:[],years:[],times:{hours:[],minutes:[],seconds:[]},...e??{}}),ur=e=>({showSelect:!0,showCancel:!0,showNow:!1,showPreview:!0,...e??{}}),or=e=>{const a={input:!1};return typeof e=="object"?{...a,...e??{},enabled:!0}:{enabled:e,...a}},sr=e=>({allowStopPropagation:!0,closeOnScroll:!1,modeHeight:255,allowPreventDefault:!1,closeOnClearValue:!0,closeOnAutoApply:!0,noSwipe:!1,keepActionRow:!1,onClickOutside:void 0,tabOutClosesMenu:!0,arrowLeft:void 0,keepViewOnOffsetClick:!1,timeArrowHoldThreshold:0,shadowDom:!1,mobileBreakpoint:600,setDateOnMenuClose:!1,...e??{}}),ir=e=>{const a={dates:Array.isArray(e)?e.map(n=>j(n)):[],years:[],months:[],quarters:[],weeks:[],weekdays:[],options:{highlightDisabled:!1}};return typeof e=="function"?e:{...a,...e??{}}},dr=e=>typeof e=="object"?{type:(e==null?void 0:e.type)??"local",hideOnOffsetDates:(e==null?void 0:e.hideOnOffsetDates)??!1}:{type:e,hideOnOffsetDates:!1},cr=e=>{const a={noDisabledRange:!1,showLastInRange:!0,minMaxRawRange:!1,partialRange:!0,disableTimeRangeValidation:!1,maxRange:void 0,minRange:void 0,autoRange:void 0,fixedStart:!1,fixedEnd:!1};return typeof e=="object"?{enabled:!0,...a,...e}:{enabled:e,...a}},vr=e=>e?typeof e=="string"?{timezone:e,exactMatch:!1,dateInTz:void 0,emitTimezone:void 0,convertModel:!0}:{timezone:e.timezone,exactMatch:e.exactMatch??!1,dateInTz:e.dateInTz??void 0,emitTimezone:e.emitTimezone??void 0,convertModel:e.convertModel??!0}:{timezone:void 0,exactMatch:!1,emitTimezone:void 0},zt=(e,a,n,t)=>new Map(e.map(l=>{const s=al(l,a,t);return[nl(s,n),s]})),pr=(e,a)=>e.length?new Map(e.map(n=>{const t=al(n.date,a);return[nl(t,Ea.DATE),n]})):null,mr=e=>{var a;const n=Ll(e.isMonthPicker,e.isYearPicker);return{minDate:Ht(e.minDate,e.timezone,e.isSpecific),maxDate:Ht(e.maxDate,e.timezone,e.isSpecific),disabledDates:Ot(e.disabledDates)?zt(e.disabledDates,e.timezone,n,e.isSpecific):e.disabledDates,allowedDates:Ot(e.allowedDates)?zt(e.allowedDates,e.timezone,n,e.isSpecific):null,highlight:typeof e.highlight=="object"&&Ot((a=e.highlight)==null?void 0:a.dates)?zt(e.highlight.dates,e.timezone,n):e.highlight,markers:pr(e.markers,e.timezone)}},fr=e=>typeof e=="boolean"?{enabled:e,dragSelect:!0,limit:null}:{enabled:!!e,limit:e.limit?+e.limit:null,dragSelect:e.dragSelect??!0},yr=e=>({...Object.fromEntries(Object.keys(e).map(a=>{const n=a,t=e[n],l=typeof e[n]=="string"?{[t]:!0}:Object.fromEntries(t.map(s=>[s,!0]));return[a,l]}))}),Be=e=>{const a=()=>{const S=e.enableSeconds?":ss":"",O=e.enableMinutes?":mm":"";return e.is24?`HH${O}${S}`:`hh${O}${S} aa`},n=()=>{var S;return e.format?e.format:e.monthPicker?"MM/yyyy":e.timePicker?a():e.weekPicker?`${((S=z.value)==null?void 0:S.type)==="iso"?"II":"ww"}-RR`:e.yearPicker?"yyyy":e.quarterPicker?"QQQ/yyyy":e.enableTimePicker?`MM/dd/yyyy, ${a()}`:"MM/dd/yyyy"},t=S=>jl(S,e.enableSeconds),l=()=>U.value.enabled?e.startTime&&Array.isArray(e.startTime)?[t(e.startTime[0]),t(e.startTime[1])]:null:e.startTime&&!Array.isArray(e.startTime)?t(e.startTime):null,s=W(()=>ar(e.multiCalendars)),i=W(()=>l()),f=W(()=>er(e.ariaLabels)),d=W(()=>rr(e.filters)),T=W(()=>lr(e.transitions)),p=W(()=>ur(e.actionRow)),v=W(()=>tr(e.previewFormat,e.format,n())),m=W(()=>nr(e.textInput)),k=W(()=>or(e.inline)),R=W(()=>sr(e.config)),Y=W(()=>ir(e.highlight)),z=W(()=>dr(e.weekNumbers)),P=W(()=>vr(e.timezone)),N=W(()=>fr(e.multiDates)),x=W(()=>mr({minDate:e.minDate,maxDate:e.maxDate,disabledDates:e.disabledDates,allowedDates:e.allowedDates,highlight:Y.value,markers:e.markers,timezone:P.value,isSpecific:e.monthPicker||e.yearPicker||e.quarterPicker,isMonthPicker:e.monthPicker,isYearPicker:e.yearPicker})),U=W(()=>cr(e.range)),Q=W(()=>yr(e.ui));return{defaultedTransitions:T,defaultedMultiCalendars:s,defaultedStartTime:i,defaultedAriaLabels:f,defaultedFilters:d,defaultedActionRow:p,defaultedPreviewFormat:v,defaultedTextInput:m,defaultedInline:k,defaultedConfig:R,defaultedHighlight:Y,defaultedWeekNumbers:z,defaultedRange:U,propDates:x,defaultedTz:P,defaultedMultiDates:N,defaultedUI:Q,getDefaultPattern:n,getDefaultStartTime:l,handleEventPropagation:S=>{R.value.allowStopPropagation&&S.stopPropagation(),R.value.allowPreventDefault&&S.preventDefault()}}},hr=(e,a,n)=>{const t=Z(),{defaultedTextInput:l,defaultedRange:s,defaultedTz:i,defaultedMultiDates:f,getDefaultPattern:d}=Be(a),T=Z(""),p=et(a,"format"),v=et(a,"formatLocale");na(t,()=>{typeof a.onInternalModelChange=="function"&&e("internal-model-change",t.value,E(!0))},{deep:!0}),na(s,(r,w)=>{r.enabled!==w.enabled&&(t.value=null)}),na(p,()=>{J()});const m=r=>i.value.timezone&&i.value.convertModel?ra(r,i.value.timezone):r,k=r=>{if(i.value.timezone&&i.value.convertModel){const w=Vn(i.value.timezone,r);return kn(r,w)}return r},R=(r,w,ue=!1)=>Kl(r,a.format,a.formatLocale,l.value.rangeSeparator,a.modelAuto,w??d(),ue),Y=r=>r?a.modelType?ee(r):{hours:$a(r),minutes:Ra(r),seconds:a.enableSeconds?Ha(r):0}:null,z=r=>a.modelType?ee(r):{month:$e(r),year:he(r)},P=r=>Array.isArray(r)?f.value.enabled?r.map(w=>N(w,ba(j(),w))):it(()=>[ba(j(),r[0]),r[1]?ba(j(),r[1]):La(s.value.partialRange)],s.value.enabled):ba(j(),+r),N=(r,w)=>(typeof r=="string"||typeof r=="number")&&a.modelType?V(r):w,x=r=>Array.isArray(r)?[N(r[0],xa(null,+r[0].hours,+r[0].minutes,r[0].seconds)),N(r[1],xa(null,+r[1].hours,+r[1].minutes,r[1].seconds))]:N(r,xa(null,r.hours,r.minutes,r.seconds)),U=r=>{const w=Oe(j(),{date:1});return Array.isArray(r)?f.value.enabled?r.map(ue=>N(ue,Ma(w,+ue.month,+ue.year))):it(()=>[N(r[0],Ma(w,+r[0].month,+r[0].year)),N(r[1],r[1]?Ma(w,+r[1].month,+r[1].year):La(s.value.partialRange))],s.value.enabled):N(r,Ma(w,+r.month,+r.year))},Q=r=>{if(Array.isArray(r))return r.map(w=>V(w));throw new Error(ll.dateArr("multi-dates"))},S=r=>{if(Array.isArray(r)&&s.value.enabled){const w=r[0],ue=r[1];return[j(Array.isArray(w)?w[0]:null),Array.isArray(ue)&&ue.length?j(ue[0]):null]}return j(r[0])},O=r=>a.modelAuto?Array.isArray(r)?[V(r[0]),V(r[1])]:a.autoApply?[V(r)]:[V(r),null]:Array.isArray(r)?it(()=>r[1]?[V(r[0]),r[1]?V(r[1]):La(s.value.partialRange)]:[V(r[0])],s.value.enabled):V(r),ne=()=>{Array.isArray(t.value)&&s.value.enabled&&t.value.length===1&&t.value.push(La(s.value.partialRange))},L=()=>{const r=t.value;return[ee(r[0]),r[1]?ee(r[1]):La(s.value.partialRange)]},C=()=>Array.isArray(t.value)?t.value[1]?L():ee(He(t.value[0])):[],oe=()=>(t.value||[]).map(r=>ee(r)),ce=(r=!1)=>(r||ne(),a.modelAuto?C():f.value.enabled?oe():Array.isArray(t.value)?it(()=>L(),s.value.enabled):ee(He(t.value))),ye=r=>!r||Array.isArray(r)&&!r.length?null:a.timePicker?x(He(r)):a.monthPicker?U(He(r)):a.yearPicker?P(He(r)):f.value.enabled?Q(He(r)):a.weekPicker?S(He(r)):O(He(r)),y=r=>{const w=ye(r);Wt(He(w))?(t.value=He(w),J()):(t.value=null,T.value="")},D=()=>{const r=w=>wa(w,l.value.format);return`${r(t.value[0])} ${l.value.rangeSeparator} ${t.value[1]?r(t.value[1]):""}`},h=()=>n.value&&t.value?Array.isArray(t.value)?D():wa(t.value,l.value.format):R(t.value),F=()=>t.value?f.value.enabled?t.value.map(r=>R(r)).join("; "):l.value.enabled&&typeof l.value.format=="string"?h():R(t.value):"",J=()=>{!a.format||typeof a.format=="string"||l.value.enabled&&typeof l.value.format=="string"?T.value=F():T.value=a.format(t.value)},V=r=>{if(a.utc){const w=new Date(r);return a.utc==="preserve"?new Date(w.getTime()+w.getTimezoneOffset()*6e4):w}return a.modelType?Bn.includes(a.modelType)?m(new Date(r)):a.modelType==="format"&&(typeof a.format=="string"||!a.format)?m(Et(r,d(),new Date,{locale:v.value})):m(Et(r,a.modelType,new Date,{locale:v.value})):m(new Date(r))},ee=r=>r?a.utc?Kn(r,a.utc==="preserve",a.enableSeconds):a.modelType?a.modelType==="timestamp"?+k(r):a.modelType==="iso"?k(r).toISOString():a.modelType==="format"&&(typeof a.format=="string"||!a.format)?R(k(r)):R(k(r),a.modelType,!0):k(r):"",$=(r,w=!1,ue=!1)=>{if(ue)return r;if(e("update:model-value",r),i.value.emitTimezone&&w){const xe=Array.isArray(r)?r.map(A=>ra(He(A),i.value.emitTimezone)):ra(He(r),i.value.emitTimezone);e("update:model-timezone-value",xe)}},de=r=>Array.isArray(t.value)?f.value.enabled?t.value.map(w=>r(w)):[r(t.value[0]),t.value[1]?r(t.value[1]):La(s.value.partialRange)]:r(He(t.value)),c=()=>{if(Array.isArray(t.value)){const r=Da(t.value[0],a.weekStart),w=t.value[1]?Da(t.value[1],a.weekStart):[];return[r.map(ue=>j(ue)),w.map(ue=>j(ue))]}return Da(t.value,a.weekStart).map(r=>j(r))},_=(r,w)=>$(He(de(r)),!1,w),B=r=>{const w=c();return r?w:e("update:model-value",c())},E=(r=!1)=>(r||J(),a.monthPicker?_(z,r):a.timePicker?_(Y,r):a.yearPicker?_(he,r):a.weekPicker?B(r):$(ce(r),!0,r));return{inputValue:T,internalModelValue:t,checkBeforeEmit:()=>t.value?s.value.enabled?s.value.partialRange?t.value.length>=1:t.value.length===2:!!t.value:!1,parseExternalModelValue:y,formatInputValue:J,emitModelValue:E}},gr=(e,a)=>{const{defaultedFilters:n,propDates:t}=Be(e),{validateMonthYearInRange:l}=Ca(e),s=(p,v)=>{let m=p;return n.value.months.includes($e(m))?(m=v?va(p,1):Wa(p,1),s(m,v)):m},i=(p,v)=>{let m=p;return n.value.years.includes(he(m))?(m=v?Qt(p,1):Yl(p,1),i(m,v)):m},f=(p,v=!1)=>{const m=Oe(j(),{month:e.month,year:e.year});let k=p?va(m,1):Wa(m,1);e.disableYearSelect&&(k=ba(k,e.year));let R=$e(k),Y=he(k);n.value.months.includes(R)&&(k=s(k,p),R=$e(k),Y=he(k)),n.value.years.includes(Y)&&(k=i(k,p),Y=he(k)),l(R,Y,p,e.preventMinMaxNavigation)&&d(R,Y,v)},d=(p,v,m)=>{a("update-month-year",{month:p,year:v,fromNav:m})},T=W(()=>p=>Wl(Oe(j(),{month:e.month,year:e.year}),t.value.maxDate,t.value.minDate,e.preventMinMaxNavigation,p));return{handleMonthYearChange:f,isDisabled:T,updateMonthYear:d}},wt={multiCalendars:{type:[Boolean,Number,String,Object],default:void 0},modelValue:{type:[String,Date,Array,Object,Number],default:null},modelType:{type:String,default:null},position:{type:String,default:"center"},dark:{type:Boolean,default:!1},format:{type:[String,Function],default:()=>null},autoPosition:{type:[Boolean,String],default:!0},altPosition:{type:Function,default:null},transitions:{type:[Boolean,Object],default:!0},formatLocale:{type:Object,default:null},utc:{type:[Boolean,String],default:!1},ariaLabels:{type:Object,default:()=>({})},offset:{type:[Number,String],default:10},hideNavigation:{type:Array,default:()=>[]},timezone:{type:[String,Object],default:null},vertical:{type:Boolean,default:!1},disableMonthYearSelect:{type:Boolean,default:!1},disableYearSelect:{type:Boolean,default:!1},dayClass:{type:Function,default:null},yearRange:{type:Array,default:()=>[1900,2100]},enableTimePicker:{type:Boolean,default:!0},autoApply:{type:Boolean,default:!1},disabledDates:{type:[Array,Function],default:()=>[]},monthNameFormat:{type:String,default:"short"},startDate:{type:[Date,String],default:null},startTime:{type:[Object,Array],default:null},hideOffsetDates:{type:Boolean,default:!1},noToday:{type:Boolean,default:!1},disabledWeekDays:{type:Array,default:()=>[]},allowedDates:{type:Array,default:null},nowButtonLabel:{type:String,default:"Now"},markers:{type:Array,default:()=>[]},escClose:{type:Boolean,default:!0},spaceConfirm:{type:Boolean,default:!0},monthChangeOnArrows:{type:Boolean,default:!0},presetDates:{type:Array,default:()=>[]},flow:{type:Array,default:()=>[]},partialFlow:{type:Boolean,default:!1},preventMinMaxNavigation:{type:Boolean,default:!1},reverseYears:{type:Boolean,default:!1},weekPicker:{type:Boolean,default:!1},filters:{type:Object,default:()=>({})},arrowNavigation:{type:Boolean,default:!1},highlight:{type:[Function,Object],default:null},teleport:{type:[Boolean,String,Object],default:null},teleportCenter:{type:Boolean,default:!1},locale:{type:String,default:"en-Us"},weekNumName:{type:String,default:"W"},weekStart:{type:[Number,String],default:1},weekNumbers:{type:[String,Function,Object],default:null},monthChangeOnScroll:{type:[Boolean,String],default:!0},dayNames:{type:[Function,Array],default:null},monthPicker:{type:Boolean,default:!1},customProps:{type:Object,default:null},yearPicker:{type:Boolean,default:!1},modelAuto:{type:Boolean,default:!1},selectText:{type:String,default:"Select"},cancelText:{type:String,default:"Cancel"},previewFormat:{type:[String,Function],default:()=>""},multiDates:{type:[Object,Boolean],default:!1},ignoreTimeValidation:{type:Boolean,default:!1},minDate:{type:[Date,String],default:null},maxDate:{type:[Date,String],default:null},minTime:{type:Object,default:null},maxTime:{type:Object,default:null},name:{type:String,default:null},placeholder:{type:String,default:""},hideInputIcon:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},alwaysClearable:{type:Boolean,default:!1},state:{type:Boolean,default:null},required:{type:Boolean,default:!1},autocomplete:{type:String,default:"off"},timePicker:{type:Boolean,default:!1},enableSeconds:{type:Boolean,default:!1},is24:{type:Boolean,default:!0},noHoursOverlay:{type:Boolean,default:!1},noMinutesOverlay:{type:Boolean,default:!1},noSecondsOverlay:{type:Boolean,default:!1},hoursGridIncrement:{type:[String,Number],default:1},minutesGridIncrement:{type:[String,Number],default:5},secondsGridIncrement:{type:[String,Number],default:5},hoursIncrement:{type:[Number,String],default:1},minutesIncrement:{type:[Number,String],default:1},secondsIncrement:{type:[Number,String],default:1},range:{type:[Boolean,Object],default:!1},uid:{type:String,default:null},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},inline:{type:[Boolean,Object],default:!1},textInput:{type:[Boolean,Object],default:!1},sixWeeks:{type:[Boolean,String],default:!1},actionRow:{type:Object,default:()=>({})},focusStartDate:{type:Boolean,default:!1},disabledTimes:{type:[Function,Array],default:void 0},timePickerInline:{type:Boolean,default:!1},calendar:{type:Function,default:null},config:{type:Object,default:void 0},quarterPicker:{type:Boolean,default:!1},yearFirst:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},onInternalModelChange:{type:[Function,Object],default:null},enableMinutes:{type:Boolean,default:!0},ui:{type:Object,default:()=>({})}},ma={...wt,shadow:{type:Boolean,default:!1},flowStep:{type:Number,default:0},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},menuWrapRef:{type:Object,default:null},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1},isMobile:{type:Boolean,default:void 0}},br=["title"],wr=["disabled"],_r=qe({compatConfig:{MODE:3},__name:"ActionRow",props:{menuMount:{type:Boolean,default:!1},calendarWidth:{type:Number,default:0},...ma},emits:["close-picker","select-date","select-now","invalid-select"],setup(e,{emit:a}){const n=a,t=e,{defaultedActionRow:l,defaultedPreviewFormat:s,defaultedMultiCalendars:i,defaultedTextInput:f,defaultedInline:d,defaultedRange:T,defaultedMultiDates:p}=Be(t),{isTimeValid:v,isMonthValid:m}=Ca(t),{buildMatrix:k}=Oa(),R=Z(null),Y=Z(null),z=Z(!1),P=Z({}),N=Z(null),x=Z(null);Qe(()=>{t.arrowNavigation&&k([ze(R),ze(Y)],"actionRow"),U(),window.addEventListener("resize",U)}),Qa(()=>{window.removeEventListener("resize",U)});const U=()=>{z.value=!1,setTimeout(()=>{var y,D;const h=(y=N.value)==null?void 0:y.getBoundingClientRect(),F=(D=x.value)==null?void 0:D.getBoundingClientRect();h&&F&&(P.value.maxWidth=`${F.width-h.width-20}px`),z.value=!0},0)},Q=W(()=>T.value.enabled&&!T.value.partialRange&&t.internalModelValue?t.internalModelValue.length===2:!0),S=W(()=>!v.value(t.internalModelValue)||!m.value(t.internalModelValue)||!Q.value),O=()=>{const y=s.value;return t.timePicker||t.monthPicker,y(He(t.internalModelValue))},ne=()=>{const y=t.internalModelValue;return i.value.count>0?`${L(y[0])} - ${L(y[1])}`:[L(y[0]),L(y[1])]},L=y=>Kl(y,s.value,t.formatLocale,f.value.rangeSeparator,t.modelAuto,s.value),C=W(()=>!t.internalModelValue||!t.menuMount?"":typeof s.value=="string"?Array.isArray(t.internalModelValue)?t.internalModelValue.length===2&&t.internalModelValue[1]?ne():p.value.enabled?t.internalModelValue.map(y=>`${L(y)}`):t.modelAuto?`${L(t.internalModelValue[0])}`:`${L(t.internalModelValue[0])} -`:L(t.internalModelValue):O()),oe=()=>p.value.enabled?"; ":" - ",ce=W(()=>Array.isArray(C.value)?C.value.join(oe()):C.value),ye=()=>{v.value(t.internalModelValue)&&m.value(t.internalModelValue)&&Q.value?n("select-date"):n("invalid-select")};return(y,D)=>(M(),H("div",{ref_key:"actionRowRef",ref:x,class:"dp__action_row"},[y.$slots["action-row"]?ie(y.$slots,"action-row",je(We({key:0},{internalModelValue:y.internalModelValue,disabled:S.value,selectDate:()=>y.$emit("select-date"),closePicker:()=>y.$emit("close-picker")}))):(M(),H(_e,{key:1},[o(l).showPreview?(M(),H("div",{key:0,class:"dp__selection_preview",title:ce.value,style:sa(P.value)},[y.$slots["action-preview"]&&z.value?ie(y.$slots,"action-preview",{key:0,value:y.internalModelValue}):q("",!0),!y.$slots["action-preview"]&&z.value?(M(),H(_e,{key:1},[_a(Ze(ce.value),1)],64)):q("",!0)],12,br)):q("",!0),me("div",{ref_key:"actionBtnContainer",ref:N,class:"dp__action_buttons","data-dp-element":"action-row"},[y.$slots["action-buttons"]?ie(y.$slots,"action-buttons",{key:0,value:y.internalModelValue}):q("",!0),y.$slots["action-buttons"]?q("",!0):(M(),H(_e,{key:1},[!o(d).enabled&&o(l).showCancel?(M(),H("button",{key:0,ref_key:"cancelButtonRef",ref:R,type:"button",class:"dp__action_button dp__action_cancel",onClick:D[0]||(D[0]=h=>y.$emit("close-picker")),onKeydown:D[1]||(D[1]=h=>o(aa)(h,()=>y.$emit("close-picker")))},Ze(y.cancelText),545)):q("",!0),o(l).showNow?(M(),H("button",{key:1,type:"button",class:"dp__action_button dp__action_cancel",onClick:D[2]||(D[2]=h=>y.$emit("select-now")),onKeydown:D[3]||(D[3]=h=>o(aa)(h,()=>y.$emit("select-now")))},Ze(y.nowButtonLabel),33)):q("",!0),o(l).showSelect?(M(),H("button",{key:2,ref_key:"selectButtonRef",ref:Y,type:"button",class:"dp__action_button dp__action_select",disabled:S.value,"data-test-id":"select-button",onKeydown:D[4]||(D[4]=h=>o(aa)(h,()=>ye())),onClick:ye},Ze(y.selectText),41,wr)):q("",!0)],64))],512)],64))],512))}}),kr=["role","aria-label","tabindex"],Dr={class:"dp__selection_grid_header"},Mr=["aria-selected","aria-disabled","data-test-id","onClick","onKeydown","onMouseover"],$r=["aria-label"],rt=qe({__name:"SelectionOverlay",props:{items:{},type:{},isLast:{type:Boolean},arrowNavigation:{type:Boolean},skipButtonRef:{type:Boolean},headerRefs:{},hideNavigation:{},escClose:{type:Boolean},useRelative:{type:Boolean},height:{},textInput:{type:[Boolean,Object]},config:{},noOverlayFocus:{type:Boolean},focusValue:{},menuWrapRef:{},ariaLabels:{},overlayLabel:{}},emits:["selected","toggle","reset-flow","hover-value"],setup(e,{expose:a,emit:n}){const{setSelectionGrid:t,buildMultiLevelMatrix:l,setMonthPicker:s}=Oa(),i=n,f=e,{defaultedAriaLabels:d,defaultedTextInput:T,defaultedConfig:p,handleEventPropagation:v}=Be(f),{hideNavigationButtons:m}=Dt(),k=Z(!1),R=Z(null),Y=Z(null),z=Z([]),P=Z(),N=Z(null),x=Z(0),U=Z(null);fn(()=>{R.value=null}),Qe(()=>{oa().then(()=>ce()),f.noOverlayFocus||S(),Q(!0)}),Qa(()=>Q(!1));const Q=c=>{var _;f.arrowNavigation&&((_=f.headerRefs)!=null&&_.length?s(c):t(c))},S=()=>{var c;const _=ze(Y);_&&(T.value.enabled||(R.value?(c=R.value)==null||c.focus({preventScroll:!0}):_.focus({preventScroll:!0})),k.value=_.clientHeight<_.scrollHeight)},O=W(()=>({dp__overlay:!0,"dp--overlay-absolute":!f.useRelative,"dp--overlay-relative":f.useRelative})),ne=W(()=>f.useRelative?{height:`${f.height}px`,width:"var(--dp-menu-min-width)"}:void 0),L=W(()=>({dp__overlay_col:!0})),C=W(()=>({dp__btn:!0,dp__button:!0,dp__overlay_action:!0,dp__over_action_scroll:k.value,dp__button_bottom:f.isLast})),oe=W(()=>{var c,_;return{dp__overlay_container:!0,dp__container_flex:((c=f.items)==null?void 0:c.length)<=6,dp__container_block:((_=f.items)==null?void 0:_.length)>6}});na(()=>f.items,()=>ce(!1),{deep:!0});const ce=(c=!0)=>{oa().then(()=>{const _=ze(R),B=ze(Y),E=ze(N),r=ze(U),w=E?E.getBoundingClientRect().height:0;B&&(B.getBoundingClientRect().height?x.value=B.getBoundingClientRect().height-w:x.value=p.value.modeHeight-w),_&&r&&c&&(r.scrollTop=_.offsetTop-r.offsetTop-(x.value/2-_.getBoundingClientRect().height)-w)})},ye=c=>{c.disabled||i("selected",c.value)},y=()=>{i("toggle"),i("reset-flow")},D=c=>{f.escClose&&(y(),v(c))},h=(c,_,B,E)=>{c&&((_.active||_.value===f.focusValue)&&(R.value=c),f.arrowNavigation&&(Array.isArray(z.value[B])?z.value[B][E]=c:z.value[B]=[c],F()))},F=()=>{var c,_;const B=(c=f.headerRefs)!=null&&c.length?[f.headerRefs].concat(z.value):z.value.concat([f.skipButtonRef?[]:[N.value]]);l(He(B),(_=f.headerRefs)!=null&&_.length?"monthPicker":"selectionGrid")},J=c=>{f.arrowNavigation||Sa(c,p.value,!0)},V=c=>{P.value=c,i("hover-value",c)},ee=()=>{if(y(),!f.isLast){const c=jt(f.menuWrapRef??null,"action-row");if(c){const _=Nl(c);_==null||_.focus()}}},$=c=>{switch(c.key){case Ye.esc:return D(c);case Ye.arrowLeft:return J(c);case Ye.arrowRight:return J(c);case Ye.arrowUp:return J(c);case Ye.arrowDown:return J(c);default:return}},de=c=>{if(c.key===Ye.enter)return y();if(c.key===Ye.tab)return ee()};return a({focusGrid:S}),(c,_)=>{var B;return M(),H("div",{ref_key:"gridWrapRef",ref:Y,class:De(O.value),style:sa(ne.value),role:c.useRelative?void 0:"dialog","aria-label":c.overlayLabel,tabindex:c.useRelative?void 0:"0",onKeydown:$,onClick:_[0]||(_[0]=at(()=>{},["prevent"]))},[me("div",{ref_key:"containerRef",ref:U,class:De(oe.value),style:sa({"--dp-overlay-height":`${x.value}px`}),role:"grid"},[me("div",Dr,[ie(c.$slots,"header")]),c.$slots.overlay?ie(c.$slots,"overlay",{key:0}):(M(!0),H(_e,{key:1},Ve(c.items,(E,r)=>(M(),H("div",{key:r,class:De(["dp__overlay_row",{dp__flex_row:c.items.length>=3}]),role:"row"},[(M(!0),H(_e,null,Ve(E,(w,ue)=>(M(),H("div",{key:w.value,ref_for:!0,ref:xe=>h(xe,w,r,ue),role:"gridcell",class:De(L.value),"aria-selected":w.active||void 0,"aria-disabled":w.disabled||void 0,tabindex:"0","data-test-id":w.text,onClick:at(xe=>ye(w),["prevent"]),onKeydown:xe=>o(aa)(xe,()=>ye(w),!0),onMouseover:xe=>V(w.value)},[me("div",{class:De(w.className)},[c.$slots.item?ie(c.$slots,"item",{key:0,item:w}):q("",!0),c.$slots.item?q("",!0):(M(),H(_e,{key:1},[_a(Ze(w.text),1)],64))],2)],42,Mr))),128))],2))),128))],6),c.$slots["button-icon"]?vt((M(),H("button",{key:0,ref_key:"toggleButton",ref:N,type:"button","aria-label":(B=o(d))==null?void 0:B.toggleOverlay,class:De(C.value),tabindex:"0",onClick:y,onKeydown:de},[ie(c.$slots,"button-icon")],42,$r)),[[pt,!o(m)(c.hideNavigation,c.type)]]):q("",!0)],46,kr)}}}),Ar=["data-dp-mobile"],_t=qe({__name:"InstanceWrap",props:{multiCalendars:{},stretch:{type:Boolean},collapse:{type:Boolean},isMobile:{type:Boolean}},setup(e){const a=e,n=W(()=>a.multiCalendars>0?[...Array(a.multiCalendars).keys()]:[0]),t=W(()=>({dp__instance_calendar:a.multiCalendars>0}));return(l,s)=>(M(),H("div",{class:De({dp__menu_inner:!l.stretch,"dp--menu--inner-stretched":l.stretch,dp__flex_display:l.multiCalendars>0,"dp--flex-display-collapsed":l.collapse}),"data-dp-mobile":l.isMobile},[(M(!0),H(_e,null,Ve(n.value,(i,f)=>(M(),H("div",{key:i,class:De(t.value)},[ie(l.$slots,"default",{instance:i,index:f})],2))),128))],10,Ar))}}),Tr=["data-dp-element","aria-label","aria-disabled"],Ja=qe({compatConfig:{MODE:3},__name:"ArrowBtn",props:{ariaLabel:{},elName:{},disabled:{type:Boolean}},emits:["activate","set-ref"],setup(e,{emit:a}){const n=a,t=Z(null);return Qe(()=>n("set-ref",t)),(l,s)=>(M(),H("button",{ref_key:"elRef",ref:t,type:"button","data-dp-element":l.elName,class:"dp__btn dp--arrow-btn-nav",tabindex:"0","aria-label":l.ariaLabel,"aria-disabled":l.disabled||void 0,onClick:s[0]||(s[0]=i=>l.$emit("activate")),onKeydown:s[1]||(s[1]=i=>o(aa)(i,()=>l.$emit("activate"),!0))},[me("span",{class:De(["dp__inner_nav",{dp__inner_nav_disabled:l.disabled}])},[ie(l.$slots,"default")],2)],40,Tr))}}),Pr=["aria-label","data-test-id"],Zl=qe({__name:"YearModePicker",props:{...ma,showYearPicker:{type:Boolean,default:!1},items:{type:Array,default:()=>[]},instance:{type:Number,default:0},year:{type:Number,default:0},isDisabled:{type:Function,default:()=>!1}},emits:["toggle-year-picker","year-select","handle-year"],setup(e,{emit:a}){const n=a,t=e,{showRightIcon:l,showLeftIcon:s}=Dt(),{defaultedConfig:i,defaultedMultiCalendars:f,defaultedAriaLabels:d,defaultedTransitions:T,defaultedUI:p}=Be(t),{showTransition:v,transitionName:m}=ut(T),k=Z(!1),R=(P=!1,N)=>{k.value=!k.value,n("toggle-year-picker",{flow:P,show:N})},Y=P=>{k.value=!1,n("year-select",P)},z=(P=!1)=>{n("handle-year",P)};return(P,N)=>{var x,U,Q,S,O;return M(),H(_e,null,[me("div",{class:De(["dp--year-mode-picker",{"dp--hidden-el":k.value}])},[o(s)(o(f),e.instance)?(M(),Te(Ja,{key:0,ref:"mpPrevIconRef","aria-label":(x=o(d))==null?void 0:x.prevYear,disabled:e.isDisabled(!1),class:De((U=o(p))==null?void 0:U.navBtnPrev),onActivate:N[0]||(N[0]=ne=>z(!1))},{default:ke(()=>[P.$slots["arrow-left"]?ie(P.$slots,"arrow-left",{key:0}):q("",!0),P.$slots["arrow-left"]?q("",!0):(M(),Te(o(Zt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):q("",!0),me("button",{ref:"mpYearButtonRef",class:"dp__btn dp--year-select",type:"button","aria-label":`${e.year}-${(Q=o(d))==null?void 0:Q.openYearsOverlay}`,"data-test-id":`year-mode-btn-${e.instance}`,onClick:N[1]||(N[1]=()=>R(!1)),onKeydown:N[2]||(N[2]=yn(()=>R(!1),["enter"]))},[P.$slots.year?ie(P.$slots,"year",{key:0,year:e.year}):q("",!0),P.$slots.year?q("",!0):(M(),H(_e,{key:1},[_a(Ze(e.year),1)],64))],40,Pr),o(l)(o(f),e.instance)?(M(),Te(Ja,{key:1,ref:"mpNextIconRef","aria-label":(S=o(d))==null?void 0:S.nextYear,disabled:e.isDisabled(!0),class:De((O=o(p))==null?void 0:O.navBtnNext),onActivate:N[3]||(N[3]=ne=>z(!0))},{default:ke(()=>[P.$slots["arrow-right"]?ie(P.$slots,"arrow-right",{key:0}):q("",!0),P.$slots["arrow-right"]?q("",!0):(M(),Te(o(Gt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):q("",!0)],2),ta(Za,{name:o(m)(e.showYearPicker),css:o(v)},{default:ke(()=>{var ne,L;return[e.showYearPicker?(M(),Te(rt,{key:0,items:e.items,"text-input":P.textInput,"esc-close":P.escClose,config:P.config,"is-last":P.autoApply&&!o(i).keepActionRow,"hide-navigation":P.hideNavigation,"aria-labels":P.ariaLabels,"overlay-label":(L=(ne=o(d))==null?void 0:ne.yearPicker)==null?void 0:L.call(ne,!0),type:"year",onToggle:R,onSelected:N[4]||(N[4]=C=>Y(C))},Je({"button-icon":ke(()=>[P.$slots["calendar-icon"]?ie(P.$slots,"calendar-icon",{key:0}):q("",!0),P.$slots["calendar-icon"]?q("",!0):(M(),Te(o(Ga),{key:1}))]),_:2},[P.$slots["year-overlay-value"]?{name:"item",fn:ke(({item:C})=>[ie(P.$slots,"year-overlay-value",{text:C.text,value:C.value})]),key:"0"}:void 0]),1032,["items","text-input","esc-close","config","is-last","hide-navigation","aria-labels","overlay-label"])):q("",!0)]}),_:3},8,["name","css"])],64)}}}),ul=(e,a,n)=>{if(a.value&&Array.isArray(a.value))if(a.value.some(t=>Ae(e,t))){const t=a.value.filter(l=>!Ae(l,e));a.value=t.length?t:null}else(n&&+n>a.value.length||!n)&&a.value.push(e);else a.value=[e]},ol=(e,a,n)=>{let t=e.value?e.value.slice():[];return t.length===2&&t[1]!==null&&(t=[]),t.length?(Ie(a,t[0])?t.unshift(a):t[1]=a,n("range-end",a)):(t=[a],n("range-start",a)),t},kt=(e,a,n,t)=>{e&&(e[0]&&e[1]&&n&&a("auto-apply"),e[0]&&!e[1]&&t&&n&&a("auto-apply"))},Gl=e=>{Array.isArray(e.value)&&e.value.length<=2&&e.range?e.modelValue.value=e.value.map(a=>ra(j(a),e.timezone)):Array.isArray(e.value)||(e.modelValue.value=ra(j(e.value),e.timezone))},Xl=(e,a,n,t)=>Array.isArray(a.value)&&(a.value.length===2||a.value.length===1&&t.value.partialRange)?t.value.fixedStart&&(Le(e,a.value[0])||Ae(e,a.value[0]))?[a.value[0],e]:t.value.fixedEnd&&(Ie(e,a.value[1])||Ae(e,a.value[1]))?[e,a.value[1]]:(n("invalid-fixed-range",e),a.value):[],Jl=({multiCalendars:e,range:a,highlight:n,propDates:t,calendars:l,modelValue:s,props:i,filters:f,year:d,month:T,emit:p})=>{const v=W(()=>tl(i.yearRange,i.locale,i.reverseYears)),m=Z([!1]),k=W(()=>(C,oe)=>{const ce=Oe(ca(new Date),{month:T.value(C),year:d.value(C)}),ye=oe?Pl(ce):ft(ce);return Wl(ye,t.value.maxDate,t.value.minDate,i.preventMinMaxNavigation,oe)}),R=()=>Array.isArray(s.value)&&e.value.solo&&s.value[1],Y=()=>{for(let C=0;C<e.value.count;C++)if(C===0)l.value[C]=l.value[0];else if(C===e.value.count-1&&R())l.value[C]={month:$e(s.value[1]),year:he(s.value[1])};else{const oe=Oe(j(),l.value[C-1]);l.value[C]={month:$e(oe),year:he(Qt(oe,1))}}},z=C=>{if(!C)return Y();const oe=Oe(j(),l.value[C]);return l.value[0].year=he(Yl(oe,e.value.count-1)),Y()},P=(C,oe)=>{const ce=$n(oe,C);return a.value.showLastInRange&&ce>1?oe:C},N=C=>i.focusStartDate||e.value.solo?C[0]:C[1]?P(C[0],C[1]):C[0],x=()=>{if(s.value){const C=Array.isArray(s.value)?N(s.value):s.value;l.value[0]={month:$e(C),year:he(C)}}},U=()=>{x(),e.value.count&&Y()};na(s,(C,oe)=>{i.isTextInputDate&&JSON.stringify(C??{})!==JSON.stringify(oe??{})&&U()}),Qe(()=>{U()});const Q=(C,oe)=>{l.value[oe].year=C,p("update-month-year",{instance:oe,year:C,month:l.value[oe].month}),e.value.count&&!e.value.solo&&z(oe)},S=W(()=>C=>Ka(v.value,oe=>{var ce;const ye=d.value(C)===oe.value,y=tt(oe.value,qa(t.value.minDate),qa(t.value.maxDate))||((ce=f.value.years)==null?void 0:ce.includes(d.value(C))),D=rl(n.value,oe.value);return{active:ye,disabled:y,highlighted:D}})),O=(C,oe)=>{Q(C,oe),L(oe)},ne=(C,oe=!1)=>{if(!k.value(C,oe)){const ce=oe?d.value(C)+1:d.value(C)-1;Q(ce,C)}},L=(C,oe=!1,ce)=>{oe||p("reset-flow"),ce!==void 0?m.value[C]=ce:m.value[C]=!m.value[C],m.value[C]?p("overlay-toggle",{open:!0,overlay:Xe.year}):(p("overlay-closed"),p("overlay-toggle",{open:!1,overlay:Xe.year}))};return{isDisabled:k,groupedYears:S,showYearPicker:m,selectYear:Q,toggleYearPicker:L,handleYearSelect:O,handleYear:ne}},Sr=(e,a)=>{const{defaultedMultiCalendars:n,defaultedAriaLabels:t,defaultedTransitions:l,defaultedConfig:s,defaultedRange:i,defaultedHighlight:f,propDates:d,defaultedTz:T,defaultedFilters:p,defaultedMultiDates:v}=Be(e),m=()=>{e.isTextInputDate&&U(he(j(e.startDate)),0)},{modelValue:k,year:R,month:Y,calendars:z}=ot(e,a,m),P=W(()=>Bl(e.formatLocale,e.locale,e.monthNameFormat)),N=Z(null),{checkMinMaxRange:x}=Ca(e),{selectYear:U,groupedYears:Q,showYearPicker:S,toggleYearPicker:O,handleYearSelect:ne,handleYear:L,isDisabled:C}=Jl({modelValue:k,multiCalendars:n,range:i,highlight:f,calendars:z,year:R,propDates:d,month:Y,filters:p,props:e,emit:a});Qe(()=>{e.startDate&&(k.value&&e.focusStartDate||!k.value)&&U(he(j(e.startDate)),0)});const oe=_=>_?{month:$e(_),year:he(_)}:{month:null,year:null},ce=()=>k.value?Array.isArray(k.value)?k.value.map(_=>oe(_)):oe(k.value):oe(),ye=(_,B)=>{const E=z.value[_],r=ce();return Array.isArray(r)?r.some(w=>w.year===(E==null?void 0:E.year)&&w.month===B):(E==null?void 0:E.year)===r.year&&B===r.month},y=(_,B,E)=>{var r,w;const ue=ce();return Array.isArray(ue)?R.value(B)===((r=ue[E])==null?void 0:r.year)&&_===((w=ue[E])==null?void 0:w.month):!1},D=(_,B)=>{if(i.value.enabled){const E=ce();if(Array.isArray(k.value)&&Array.isArray(E)){const r=y(_,B,0)||y(_,B,1),w=Ma(ca(j()),_,R.value(B));return lt(k.value,N.value,w)&&!r}return!1}return!1},h=W(()=>_=>Ka(P.value,B=>{var E;const r=ye(_,B.value),w=tt(B.value,Ul(R.value(_),d.value.minDate),El(R.value(_),d.value.maxDate))||Xn(d.value.disabledDates,R.value(_),B.value)||((E=p.value.months)==null?void 0:E.includes(B.value))||!Jn(d.value.allowedDates,R.value(_),B.value),ue=D(B.value,_),xe=ql(f.value,B.value,R.value(_));return{active:r,disabled:w,isBetween:ue,highlighted:xe}})),F=(_,B)=>Ma(ca(j()),_,R.value(B)),J=(_,B)=>{const E=k.value?k.value:ca(new Date);k.value=Ma(E,_,R.value(B)),a("auto-apply"),a("update-flow-step")},V=(_,B)=>{const E=F(_,B);i.value.fixedEnd||i.value.fixedStart?k.value=Xl(E,k,a,i):k.value?x(E,k.value)&&(k.value=ol(k,F(_,B),a)):k.value=[F(_,B)],oa().then(()=>{kt(k.value,a,e.autoApply,e.modelAuto)})},ee=(_,B)=>{ul(F(_,B),k,v.value.limit),a("auto-apply",!0)},$=(_,B)=>(z.value[B].month=_,c(B,z.value[B].year,_),v.value.enabled?ee(_,B):i.value.enabled?V(_,B):J(_,B)),de=(_,B)=>{U(_,B),c(B,_,null)},c=(_,B,E)=>{let r=E;if(!r&&r!==0){const w=ce();r=Array.isArray(w)?w[_].month:w.month}a("update-month-year",{instance:_,year:B,month:r})};return{groupedMonths:h,groupedYears:Q,year:R,isDisabled:C,defaultedMultiCalendars:n,defaultedAriaLabels:t,defaultedTransitions:l,defaultedConfig:s,showYearPicker:S,modelValue:k,presetDate:(_,B)=>{Gl({value:_,modelValue:k,range:i.value.enabled,timezone:B?void 0:T.value.timezone}),a("auto-apply")},setHoverDate:(_,B)=>{N.value=F(_,B)},selectMonth:$,selectYear:de,toggleYearPicker:O,handleYearSelect:ne,handleYear:L,getModelMonthYear:ce}},xr=qe({compatConfig:{MODE:3},__name:"MonthPicker",props:{...ma},emits:["update:internal-model-value","overlay-closed","reset-flow","range-start","range-end","auto-apply","update-month-year","update-flow-step","mount","invalid-fixed-range","overlay-toggle"],setup(e,{expose:a,emit:n}){const t=n,l=Fa(),s=ua(l,"yearMode"),i=e;Qe(()=>{i.shadow||t("mount",null)});const{groupedMonths:f,groupedYears:d,year:T,isDisabled:p,defaultedMultiCalendars:v,defaultedConfig:m,showYearPicker:k,modelValue:R,presetDate:Y,setHoverDate:z,selectMonth:P,selectYear:N,toggleYearPicker:x,handleYearSelect:U,handleYear:Q,getModelMonthYear:S}=Sr(i,t);return a({getSidebarProps:()=>({modelValue:R,year:T,getModelMonthYear:S,selectMonth:P,selectYear:N,handleYear:Q}),presetDate:Y,toggleYearPicker:O=>x(0,O)}),(O,ne)=>(M(),Te(_t,{"multi-calendars":o(v).count,collapse:O.collapse,stretch:"","is-mobile":O.isMobile},{default:ke(({instance:L})=>[O.$slots["top-extra"]?ie(O.$slots,"top-extra",{key:0,value:O.internalModelValue}):q("",!0),O.$slots["month-year"]?ie(O.$slots,"month-year",je(We({key:1},{year:o(T),months:o(f)(L),years:o(d)(L),selectMonth:o(P),selectYear:o(N),instance:L}))):(M(),Te(rt,{key:2,items:o(f)(L),"arrow-navigation":O.arrowNavigation,"is-last":O.autoApply&&!o(m).keepActionRow,"esc-close":O.escClose,height:o(m).modeHeight,config:O.config,"no-overlay-focus":!!(O.noOverlayFocus||O.textInput),"use-relative":"",type:"month",onSelected:C=>o(P)(C,L),onHoverValue:C=>o(z)(C,L)},Je({header:ke(()=>[ta(Zl,We(O.$props,{items:o(d)(L),instance:L,"show-year-picker":o(k)[L],year:o(T)(L),"is-disabled":C=>o(p)(L,C),onHandleYear:C=>o(Q)(L,C),onYearSelect:C=>o(U)(C,L),onToggleYearPicker:C=>o(x)(L,C==null?void 0:C.flow,C==null?void 0:C.show)}),Je({_:2},[Ve(o(s),(C,oe)=>({name:C,fn:ke(ce=>[ie(O.$slots,C,je(la(ce)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),_:2},[O.$slots["month-overlay-value"]?{name:"item",fn:ke(({item:C})=>[ie(O.$slots,"month-overlay-value",{text:C.text,value:C.value})]),key:"0"}:void 0]),1032,["items","arrow-navigation","is-last","esc-close","height","config","no-overlay-focus","onSelected","onHoverValue"]))]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),Rr=(e,a)=>{const n=()=>{e.isTextInputDate&&(p.value=he(j(e.startDate)))},{modelValue:t}=ot(e,a,n),l=Z(null),{defaultedHighlight:s,defaultedMultiDates:i,defaultedFilters:f,defaultedRange:d,propDates:T}=Be(e),p=Z();Qe(()=>{e.startDate&&(t.value&&e.focusStartDate||!t.value)&&(p.value=he(j(e.startDate)))});const v=P=>Array.isArray(t.value)?t.value.some(N=>he(N)===P):t.value?he(t.value)===P:!1,m=P=>d.value.enabled&&Array.isArray(t.value)?lt(t.value,l.value,z(P)):!1,k=P=>T.value.allowedDates instanceof Map?T.value.allowedDates.size?T.value.allowedDates.has(`${P}`):!1:!0,R=P=>T.value.disabledDates instanceof Map?T.value.disabledDates.size?T.value.disabledDates.has(`${P}`):!1:!0,Y=W(()=>Ka(tl(e.yearRange,e.locale,e.reverseYears),P=>{const N=v(P.value),x=tt(P.value,qa(T.value.minDate),qa(T.value.maxDate))||f.value.years.includes(P.value)||!k(P.value)||R(P.value),U=m(P.value)&&!N,Q=rl(s.value,P.value);return{active:N,disabled:x,isBetween:U,highlighted:Q}})),z=P=>ba(ca(ft(new Date)),P);return{groupedYears:Y,modelValue:t,focusYear:p,setHoverValue:P=>{l.value=ba(ca(new Date),P)},selectYear:P=>{var N;if(a("update-month-year",{instance:0,year:P}),i.value.enabled)return t.value?Array.isArray(t.value)&&(((N=t.value)==null?void 0:N.map(x=>he(x))).includes(P)?t.value=t.value.filter(x=>he(x)!==P):t.value.push(ba(Ke(j()),P))):t.value=[ba(Ke(ft(j())),P)],a("auto-apply",!0);d.value.enabled?(t.value=ol(t,z(P),a),oa().then(()=>{kt(t.value,a,e.autoApply,e.modelAuto)})):(t.value=z(P),a("auto-apply"))}}},Or=qe({compatConfig:{MODE:3},__name:"YearPicker",props:{...ma},emits:["update:internal-model-value","reset-flow","range-start","range-end","auto-apply","update-month-year"],setup(e,{expose:a,emit:n}){const t=n,l=e,{groupedYears:s,modelValue:i,focusYear:f,selectYear:d,setHoverValue:T}=Rr(l,t),{defaultedConfig:p}=Be(l);return a({getSidebarProps:()=>({modelValue:i,selectYear:d})}),(v,m)=>(M(),H("div",null,[v.$slots["top-extra"]?ie(v.$slots,"top-extra",{key:0,value:v.internalModelValue}):q("",!0),v.$slots["month-year"]?ie(v.$slots,"month-year",je(We({key:1},{years:o(s),selectYear:o(d)}))):(M(),Te(rt,{key:2,items:o(s),"is-last":v.autoApply&&!o(p).keepActionRow,height:o(p).modeHeight,config:v.config,"no-overlay-focus":!!(v.noOverlayFocus||v.textInput),"focus-value":o(f),type:"year","use-relative":"",onSelected:o(d),onHoverValue:o(T)},Je({_:2},[v.$slots["year-overlay-value"]?{name:"item",fn:ke(({item:k})=>[ie(v.$slots,"year-overlay-value",{text:k.text,value:k.value})]),key:"0"}:void 0]),1032,["items","is-last","height","config","no-overlay-focus","focus-value","onSelected","onHoverValue"]))]))}}),Cr={key:0,class:"dp__time_input"},Yr=["data-compact","data-collapsed"],Vr=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],Br=["aria-label","disabled","data-test-id","onKeydown","onClick"],Ir=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],Nr={key:0},Fr=["aria-label","data-compact"],Lr=qe({compatConfig:{MODE:3},__name:"TimeInput",props:{hours:{type:Number,default:0},minutes:{type:Number,default:0},seconds:{type:Number,default:0},closeTimePickerBtn:{type:Object,default:null},order:{type:Number,default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...ma},emits:["set-hours","set-minutes","update:hours","update:minutes","update:seconds","reset-flow","mounted","overlay-closed","overlay-opened","am-pm-change"],setup(e,{expose:a,emit:n}){const t=n,l=e,{setTimePickerElements:s,setTimePickerBackRef:i}=Oa(),{defaultedAriaLabels:f,defaultedTransitions:d,defaultedFilters:T,defaultedConfig:p,defaultedRange:v,defaultedMultiCalendars:m}=Be(l),{transitionName:k,showTransition:R}=ut(d),Y=nt({hours:!1,minutes:!1,seconds:!1}),z=Z("AM"),P=Z(null),N=Z([]),x=Z(),U=Z(!1);Qe(()=>{t("mounted")});const Q=u=>Oe(new Date,{hours:u.hours,minutes:u.minutes,seconds:l.enableSeconds?u.seconds:0,milliseconds:0}),S=W(()=>u=>V(u,l[u])||ne(u,l[u])),O=W(()=>({hours:l.hours,minutes:l.minutes,seconds:l.seconds})),ne=(u,X)=>v.value.enabled&&!v.value.disableTimeRangeValidation?!l.validateTime(u,X):!1,L=(u,X)=>{if(v.value.enabled&&!v.value.disableTimeRangeValidation){const te=X?+l[`${u}Increment`]:-+l[`${u}Increment`],le=l[u]+te;return!l.validateTime(u,le)}return!1},C=W(()=>u=>!_(+l[u]+ +l[`${u}Increment`],u)||L(u,!0)),oe=W(()=>u=>!_(+l[u]-+l[`${u}Increment`],u)||L(u,!1)),ce=(u,X)=>$l(Oe(j(),u),X),ye=(u,X)=>On(Oe(j(),u),X),y=W(()=>({dp__time_col:!0,dp__time_col_block:!l.timePickerInline,dp__time_col_reg_block:!l.enableSeconds&&l.is24&&!l.timePickerInline,dp__time_col_reg_inline:!l.enableSeconds&&l.is24&&l.timePickerInline,dp__time_col_reg_with_button:!l.enableSeconds&&!l.is24,dp__time_col_sec:l.enableSeconds&&l.is24,dp__time_col_sec_with_button:l.enableSeconds&&!l.is24})),D=W(()=>l.timePickerInline&&v.value.enabled&&!m.value.count),h=W(()=>{const u=[{type:"hours"}];return l.enableMinutes&&u.push({type:"",separator:!0},{type:"minutes"}),l.enableSeconds&&u.push({type:"",separator:!0},{type:"seconds"}),u}),F=W(()=>h.value.filter(u=>!u.separator)),J=W(()=>u=>{if(u==="hours"){const X=xe(+l.hours);return{text:X<10?`0${X}`:`${X}`,value:X}}return{text:l[u]<10?`0${l[u]}`:`${l[u]}`,value:l[u]}}),V=(u,X)=>{var te;if(!l.disabledTimesConfig)return!1;const le=l.disabledTimesConfig(l.order,u==="hours"?X:void 0);return le[u]?!!((te=le[u])!=null&&te.includes(X)):!0},ee=(u,X)=>X!=="hours"||z.value==="AM"?u:u+12,$=u=>{const X=l.is24?24:12,te=u==="hours"?X:60,le=+l[`${u}GridIncrement`],Me=u==="hours"&&!l.is24?le:0,be=[];for(let Re=Me;Re<te;Re+=le)be.push({value:l.is24?Re:ee(Re,u),text:Re<10?`0${Re}`:`${Re}`});return u==="hours"&&!l.is24&&be.unshift({value:z.value==="PM"?12:0,text:"12"}),Ka(be,Re=>({active:!1,disabled:T.value.times[u].includes(Re.value)||!_(Re.value,u)||V(u,Re.value)||ne(u,Re.value)}))},de=u=>u>=0?u:59,c=u=>u>=0?u:23,_=(u,X)=>{const te=l.minTime?Q(Ct(l.minTime)):null,le=l.maxTime?Q(Ct(l.maxTime)):null,Me=Q(Ct(O.value,X,X==="minutes"||X==="seconds"?de(u):c(u)));return te&&le?(ja(Me,le)||Ua(Me,le))&&(Na(Me,te)||Ua(Me,te)):te?Na(Me,te)||Ua(Me,te):le?ja(Me,le)||Ua(Me,le):!0},B=u=>l[`no${u[0].toUpperCase()+u.slice(1)}Overlay`],E=u=>{B(u)||(Y[u]=!Y[u],Y[u]?(U.value=!0,t("overlay-opened",u)):(U.value=!1,t("overlay-closed",u)))},r=u=>u==="hours"?$a:u==="minutes"?Ra:Ha,w=()=>{x.value&&clearTimeout(x.value)},ue=(u,X=!0,te)=>{const le=X?ce:ye,Me=X?+l[`${u}Increment`]:-+l[`${u}Increment`];_(+l[u]+Me,u)&&t(`update:${u}`,r(u)(le({[u]:+l[u]},{[u]:+l[`${u}Increment`]}))),!(te!=null&&te.keyboard)&&p.value.timeArrowHoldThreshold&&(x.value=setTimeout(()=>{ue(u,X)},p.value.timeArrowHoldThreshold))},xe=u=>l.is24?u:(u>=12?z.value="PM":z.value="AM",Fn(u)),A=()=>{z.value==="PM"?(z.value="AM",t("update:hours",l.hours-12)):(z.value="PM",t("update:hours",l.hours+12)),t("am-pm-change",z.value)},ge=u=>{Y[u]=!0},K=(u,X,te)=>{if(u&&l.arrowNavigation){Array.isArray(N.value[X])?N.value[X][te]=u:N.value[X]=[u];const le=N.value.reduce((Me,be)=>be.map((Re,Ne)=>[...Me[Ne]||[],be[Ne]]),[]);i(l.closeTimePickerBtn),P.value&&(le[1]=le[1].concat(P.value)),s(le,l.order)}},ae=(u,X)=>(E(u),t(`update:${u}`,X));return a({openChildCmp:ge}),(u,X)=>{var te;return u.disabled?q("",!0):(M(),H("div",Cr,[(M(!0),H(_e,null,Ve(h.value,(le,Me)=>{var be,Re,Ne;return M(),H("div",{key:Me,class:De(y.value),"data-compact":D.value&&!u.enableSeconds,"data-collapsed":D.value&&u.enableSeconds},[le.separator?(M(),H(_e,{key:0},[U.value?q("",!0):(M(),H(_e,{key:0},[_a(":")],64))],64)):(M(),H(_e,{key:1},[me("button",{ref_for:!0,ref:g=>K(g,Me,0),type:"button",class:De({dp__btn:!0,dp__inc_dec_button:!u.timePickerInline,dp__inc_dec_button_inline:u.timePickerInline,dp__tp_inline_btn_top:u.timePickerInline,dp__inc_dec_button_disabled:C.value(le.type),"dp--hidden-el":U.value}),"data-test-id":`${le.type}-time-inc-btn-${l.order}`,"aria-label":(be=o(f))==null?void 0:be.incrementValue(le.type),tabindex:"0",onKeydown:g=>o(aa)(g,()=>ue(le.type,!0,{keyboard:!0}),!0),onClick:g=>o(p).timeArrowHoldThreshold?void 0:ue(le.type,!0),onMousedown:g=>o(p).timeArrowHoldThreshold?ue(le.type,!0):void 0,onMouseup:w},[l.timePickerInline?(M(),H(_e,{key:1},[u.$slots["tp-inline-arrow-up"]?ie(u.$slots,"tp-inline-arrow-up",{key:0}):(M(),H(_e,{key:1},[X[2]||(X[2]=me("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),X[3]||(X[3]=me("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(M(),H(_e,{key:0},[u.$slots["arrow-up"]?ie(u.$slots,"arrow-up",{key:0}):q("",!0),u.$slots["arrow-up"]?q("",!0):(M(),Te(o(Jt),{key:1}))],64))],42,Vr),me("button",{ref_for:!0,ref:g=>K(g,Me,1),type:"button","aria-label":`${J.value(le.type).text}-${(Re=o(f))==null?void 0:Re.openTpOverlay(le.type)}`,class:De({dp__time_display:!0,dp__time_display_block:!u.timePickerInline,dp__time_display_inline:u.timePickerInline,"dp--time-invalid":S.value(le.type),"dp--time-overlay-btn":!S.value(le.type),"dp--hidden-el":U.value}),disabled:B(le.type),tabindex:"0","data-test-id":`${le.type}-toggle-overlay-btn-${l.order}`,onKeydown:g=>o(aa)(g,()=>E(le.type),!0),onClick:g=>E(le.type)},[u.$slots[le.type]?ie(u.$slots,le.type,{key:0,text:J.value(le.type).text,value:J.value(le.type).value}):q("",!0),u.$slots[le.type]?q("",!0):(M(),H(_e,{key:1},[_a(Ze(J.value(le.type).text),1)],64))],42,Br),me("button",{ref_for:!0,ref:g=>K(g,Me,2),type:"button",class:De({dp__btn:!0,dp__inc_dec_button:!u.timePickerInline,dp__inc_dec_button_inline:u.timePickerInline,dp__tp_inline_btn_bottom:u.timePickerInline,dp__inc_dec_button_disabled:oe.value(le.type),"dp--hidden-el":U.value}),"data-test-id":`${le.type}-time-dec-btn-${l.order}`,"aria-label":(Ne=o(f))==null?void 0:Ne.decrementValue(le.type),tabindex:"0",onKeydown:g=>o(aa)(g,()=>ue(le.type,!1,{keyboard:!0}),!0),onClick:g=>o(p).timeArrowHoldThreshold?void 0:ue(le.type,!1),onMousedown:g=>o(p).timeArrowHoldThreshold?ue(le.type,!1):void 0,onMouseup:w},[l.timePickerInline?(M(),H(_e,{key:1},[u.$slots["tp-inline-arrow-down"]?ie(u.$slots,"tp-inline-arrow-down",{key:0}):(M(),H(_e,{key:1},[X[4]||(X[4]=me("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),X[5]||(X[5]=me("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(M(),H(_e,{key:0},[u.$slots["arrow-down"]?ie(u.$slots,"arrow-down",{key:0}):q("",!0),u.$slots["arrow-down"]?q("",!0):(M(),Te(o(el),{key:1}))],64))],42,Ir)],64))],10,Yr)}),128)),u.is24?q("",!0):(M(),H("div",Nr,[u.$slots["am-pm-button"]?ie(u.$slots,"am-pm-button",{key:0,toggle:A,value:z.value}):q("",!0),u.$slots["am-pm-button"]?q("",!0):(M(),H("button",{key:1,ref_key:"amPmButton",ref:P,type:"button",class:"dp__pm_am_button",role:"button","aria-label":(te=o(f))==null?void 0:te.amPmButton,tabindex:"0","data-compact":D.value,onClick:A,onKeydown:X[0]||(X[0]=le=>o(aa)(le,()=>A(),!0))},Ze(z.value),41,Fr))])),(M(!0),H(_e,null,Ve(F.value,(le,Me)=>(M(),Te(Za,{key:Me,name:o(k)(Y[le.type]),css:o(R)},{default:ke(()=>{var be,Re;return[Y[le.type]?(M(),Te(rt,{key:0,items:$(le.type),"is-last":u.autoApply&&!o(p).keepActionRow,"esc-close":u.escClose,type:le.type,"text-input":u.textInput,config:u.config,"arrow-navigation":u.arrowNavigation,"aria-labels":u.ariaLabels,"overlay-label":(Re=(be=o(f)).timeOverlay)==null?void 0:Re.call(be,le.type),onSelected:Ne=>ae(le.type,Ne),onToggle:Ne=>E(le.type),onResetFlow:X[1]||(X[1]=Ne=>u.$emit("reset-flow"))},Je({"button-icon":ke(()=>[u.$slots["clock-icon"]?ie(u.$slots,"clock-icon",{key:0}):q("",!0),u.$slots["clock-icon"]?q("",!0):(M(),Te(bt(u.timePickerInline?o(Ga):o(Xt)),{key:1}))]),_:2},[u.$slots[`${le.type}-overlay-value`]?{name:"item",fn:ke(({item:Ne})=>[ie(u.$slots,`${le.type}-overlay-value`,{text:Ne.text,value:Ne.value})]),key:"0"}:void 0,u.$slots[`${le.type}-overlay-header`]?{name:"header",fn:ke(()=>[ie(u.$slots,`${le.type}-overlay-header`,{toggle:()=>E(le.type)})]),key:"1"}:void 0]),1032,["items","is-last","esc-close","type","text-input","config","arrow-navigation","aria-labels","overlay-label","onSelected","onToggle"])):q("",!0)]}),_:2},1032,["name","css"]))),128))]))}}}),zr=["data-dp-mobile"],Ur=["aria-label","tabindex"],Er=["role","aria-label","tabindex"],Hr=["aria-label"],en=qe({compatConfig:{MODE:3},__name:"TimePicker",props:{hours:{type:[Number,Array],default:0},minutes:{type:[Number,Array],default:0},seconds:{type:[Number,Array],default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...ma},emits:["update:hours","update:minutes","update:seconds","mount","reset-flow","overlay-opened","overlay-closed","am-pm-change"],setup(e,{expose:a,emit:n}){const t=n,l=e,{buildMatrix:s,setTimePicker:i}=Oa(),f=Fa(),{defaultedTransitions:d,defaultedAriaLabels:T,defaultedTextInput:p,defaultedConfig:v,defaultedRange:m}=Be(l),{transitionName:k,showTransition:R}=ut(d),{hideNavigationButtons:Y}=Dt(),z=Z(null),P=Z(null),N=Z([]),x=Z(null),U=Z(!1);Qe(()=>{t("mount"),!l.timePicker&&l.arrowNavigation?s([ze(z.value)],"time"):i(!0,l.timePicker)});const Q=W(()=>m.value.enabled&&l.modelAuto?Il(l.internalModelValue):!0),S=Z(!1),O=V=>({hours:Array.isArray(l.hours)?l.hours[V]:l.hours,minutes:Array.isArray(l.minutes)?l.minutes[V]:l.minutes,seconds:Array.isArray(l.seconds)?l.seconds[V]:l.seconds}),ne=W(()=>{const V=[];if(m.value.enabled)for(let ee=0;ee<2;ee++)V.push(O(ee));else V.push(O(0));return V}),L=(V,ee=!1,$="")=>{ee||t("reset-flow"),S.value=V,t(V?"overlay-opened":"overlay-closed",Xe.time),l.arrowNavigation&&i(V),oa(()=>{$!==""&&N.value[0]&&N.value[0].openChildCmp($)})},C=W(()=>({dp__btn:!0,dp__button:!0,dp__button_bottom:l.autoApply&&!v.value.keepActionRow})),oe=ua(f,"timePicker"),ce=(V,ee,$)=>m.value.enabled?ee===0?[V,ne.value[1][$]]:[ne.value[0][$],V]:V,ye=V=>{t("update:hours",V)},y=V=>{t("update:minutes",V)},D=V=>{t("update:seconds",V)},h=()=>{if(x.value&&!p.value.enabled&&!l.noOverlayFocus){const V=Nl(x.value);V&&V.focus({preventScroll:!0})}},F=V=>{U.value=!1,t("overlay-closed",V)},J=V=>{U.value=!0,t("overlay-opened",V)};return a({toggleTimePicker:L}),(V,ee)=>{var $;return M(),H("div",{class:"dp--tp-wrap","data-dp-mobile":V.isMobile},[!V.timePicker&&!V.timePickerInline?vt((M(),H("button",{key:0,ref_key:"openTimePickerBtn",ref:z,type:"button",class:De({...C.value,"dp--hidden-el":S.value}),"aria-label":($=o(T))==null?void 0:$.openTimePicker,tabindex:V.noOverlayFocus?void 0:0,"data-test-id":"open-time-picker-btn",onKeydown:ee[0]||(ee[0]=de=>o(aa)(de,()=>L(!0))),onClick:ee[1]||(ee[1]=de=>L(!0))},[V.$slots["clock-icon"]?ie(V.$slots,"clock-icon",{key:0}):q("",!0),V.$slots["clock-icon"]?q("",!0):(M(),Te(o(Xt),{key:1}))],42,Ur)),[[pt,!o(Y)(V.hideNavigation,"time")]]):q("",!0),ta(Za,{name:o(k)(S.value),css:o(R)&&!V.timePickerInline},{default:ke(()=>{var de,c;return[S.value||V.timePicker||V.timePickerInline?(M(),H("div",{key:0,ref_key:"overlayRef",ref:x,role:V.timePickerInline?void 0:"dialog",class:De({dp__overlay:!V.timePickerInline,"dp--overlay-absolute":!l.timePicker&&!V.timePickerInline,"dp--overlay-relative":l.timePicker}),style:sa(V.timePicker?{height:`${o(v).modeHeight}px`}:void 0),"aria-label":(de=o(T))==null?void 0:de.timePicker,tabindex:V.timePickerInline?void 0:0},[me("div",{class:De(V.timePickerInline?"dp__time_picker_inline_container":"dp__overlay_container dp__container_flex dp__time_picker_overlay_container"),style:{display:"flex"}},[V.$slots["time-picker-overlay"]?ie(V.$slots,"time-picker-overlay",{key:0,hours:e.hours,minutes:e.minutes,seconds:e.seconds,setHours:ye,setMinutes:y,setSeconds:D}):q("",!0),V.$slots["time-picker-overlay"]?q("",!0):(M(),H("div",{key:1,class:De(V.timePickerInline?"dp__flex":"dp__overlay_row dp__flex_row")},[(M(!0),H(_e,null,Ve(ne.value,(_,B)=>vt((M(),Te(Lr,We({key:B,ref_for:!0},{...V.$props,order:B,hours:_.hours,minutes:_.minutes,seconds:_.seconds,closeTimePickerBtn:P.value,disabledTimesConfig:e.disabledTimesConfig,disabled:B===0?o(m).fixedStart:o(m).fixedEnd},{ref_for:!0,ref_key:"timeInputRefs",ref:N,"validate-time":(E,r)=>e.validateTime(E,ce(r,B,E)),"onUpdate:hours":E=>ye(ce(E,B,"hours")),"onUpdate:minutes":E=>y(ce(E,B,"minutes")),"onUpdate:seconds":E=>D(ce(E,B,"seconds")),onMounted:h,onOverlayClosed:F,onOverlayOpened:J,onAmPmChange:ee[2]||(ee[2]=E=>V.$emit("am-pm-change",E))}),Je({_:2},[Ve(o(oe),(E,r)=>({name:E,fn:ke(w=>[ie(V.$slots,E,We({ref_for:!0},w))])}))]),1040,["validate-time","onUpdate:hours","onUpdate:minutes","onUpdate:seconds"])),[[pt,B===0?!0:Q.value]])),128))],2)),!V.timePicker&&!V.timePickerInline?vt((M(),H("button",{key:2,ref_key:"closeTimePickerBtn",ref:P,type:"button",class:De({...C.value,"dp--hidden-el":U.value}),"aria-label":(c=o(T))==null?void 0:c.closeTimePicker,tabindex:"0",onKeydown:ee[3]||(ee[3]=_=>o(aa)(_,()=>L(!1))),onClick:ee[4]||(ee[4]=_=>L(!1))},[V.$slots["calendar-icon"]?ie(V.$slots,"calendar-icon",{key:0}):q("",!0),V.$slots["calendar-icon"]?q("",!0):(M(),Te(o(Ga),{key:1}))],42,Hr)),[[pt,!o(Y)(V.hideNavigation,"time")]]):q("",!0)],2)],14,Er)):q("",!0)]}),_:3},8,["name","css"])],8,zr)}}}),an=(e,a,n,t)=>{const{defaultedRange:l}=Be(e),s=(x,U)=>Array.isArray(a[x])?a[x][U]:a[x],i=x=>e.enableSeconds?Array.isArray(a.seconds)?a.seconds[x]:a.seconds:0,f=(x,U)=>x?U!==void 0?xa(x,s("hours",U),s("minutes",U),i(U)):xa(x,a.hours,a.minutes,i()):Cl(j(),i(U)),d=(x,U)=>{a[x]=U},T=W(()=>e.modelAuto&&l.value.enabled?Array.isArray(n.value)?n.value.length>1:!1:l.value.enabled),p=(x,U)=>{const Q=Object.fromEntries(Object.keys(a).map(S=>S===x?[S,U]:[S,a[S]].slice()));if(T.value&&!l.value.disableTimeRangeValidation){const S=ne=>n.value?xa(n.value[ne],Q.hours[ne],Q.minutes[ne],Q.seconds[ne]):null,O=ne=>Ol(n.value[ne],0);return!(Ae(S(0),S(1))&&(Na(S(0),O(1))||ja(S(1),O(0))))}return!0},v=(x,U)=>{p(x,U)&&(d(x,U),t&&t())},m=x=>{v("hours",x)},k=x=>{v("minutes",x)},R=x=>{v("seconds",x)},Y=(x,U,Q,S)=>{U&&m(x),!U&&!Q&&k(x),Q&&R(x),n.value&&S(n.value)},z=x=>{if(x){const U=Array.isArray(x),Q=U?[+x[0].hours,+x[1].hours]:+x.hours,S=U?[+x[0].minutes,+x[1].minutes]:+x.minutes,O=U?[+x[0].seconds,+x[1].seconds]:+x.seconds;d("hours",Q),d("minutes",S),e.enableSeconds&&d("seconds",O)}},P=(x,U)=>{const Q={hours:Array.isArray(a.hours)?a.hours[x]:a.hours,disabledArr:[]};return(U||U===0)&&(Q.hours=U),Array.isArray(e.disabledTimes)&&(Q.disabledArr=l.value.enabled&&Array.isArray(e.disabledTimes[x])?e.disabledTimes[x]:e.disabledTimes),Q},N=W(()=>(x,U)=>{var Q;if(Array.isArray(e.disabledTimes)){const{disabledArr:S,hours:O}=P(x,U),ne=S.filter(L=>+L.hours===O);return((Q=ne[0])==null?void 0:Q.minutes)==="*"?{hours:[O],minutes:void 0,seconds:void 0}:{hours:[],minutes:(ne==null?void 0:ne.map(L=>+L.minutes))??[],seconds:(ne==null?void 0:ne.map(L=>L.seconds?+L.seconds:void 0))??[]}}return{hours:[],minutes:[],seconds:[]}});return{setTime:d,updateHours:m,updateMinutes:k,updateSeconds:R,getSetDateTime:f,updateTimeValues:Y,getSecondsValue:i,assignStartTime:z,validateTime:p,disabledTimesConfig:N}},jr=(e,a)=>{const n=()=>{e.isTextInputDate&&U()},{modelValue:t,time:l}=ot(e,a,n),{defaultedStartTime:s,defaultedRange:i,defaultedTz:f}=Be(e),{updateTimeValues:d,getSetDateTime:T,setTime:p,assignStartTime:v,disabledTimesConfig:m,validateTime:k}=an(e,l,t,R);function R(){a("update-flow-step")}const Y=S=>{const{hours:O,minutes:ne,seconds:L}=S;return{hours:+O,minutes:+ne,seconds:L?+L:0}},z=()=>{if(e.startTime){if(Array.isArray(e.startTime)){const O=Y(e.startTime[0]),ne=Y(e.startTime[1]);return[Oe(j(),O),Oe(j(),ne)]}const S=Y(e.startTime);return Oe(j(),S)}return i.value.enabled?[null,null]:null},P=()=>{if(i.value.enabled){const[S,O]=z();t.value=[ra(T(S,0),f.value.timezone),ra(T(O,1),f.value.timezone)]}else t.value=ra(T(z()),f.value.timezone)},N=S=>Array.isArray(S)?[Ia(j(S[0])),Ia(j(S[1]))]:[Ia(S??j())],x=(S,O,ne)=>{p("hours",S),p("minutes",O),p("seconds",e.enableSeconds?ne:0)},U=()=>{const[S,O]=N(t.value);return i.value.enabled?x([S.hours,O.hours],[S.minutes,O.minutes],[S.seconds,O.seconds]):x(S.hours,S.minutes,S.seconds)};Qe(()=>{if(!e.shadow)return v(s.value),t.value?U():P()});const Q=()=>{Array.isArray(t.value)?t.value=t.value.map((S,O)=>S&&T(S,O)):t.value=T(t.value),a("time-update")};return{modelValue:t,time:l,disabledTimesConfig:m,updateTime:(S,O=!0,ne=!1)=>{d(S,O,ne,Q)},validateTime:k}},Wr=qe({compatConfig:{MODE:3},__name:"TimePickerSolo",props:{...ma},emits:["update:internal-model-value","time-update","am-pm-change","mount","reset-flow","update-flow-step","overlay-toggle"],setup(e,{expose:a,emit:n}){const t=n,l=e,s=Fa(),i=ua(s,"timePicker"),f=Z(null),{time:d,modelValue:T,disabledTimesConfig:p,updateTime:v,validateTime:m}=jr(l,t);return Qe(()=>{l.shadow||t("mount",null)}),a({getSidebarProps:()=>({modelValue:T,time:d,updateTime:v}),toggleTimePicker:(k,R=!1,Y="")=>{var z;(z=f.value)==null||z.toggleTimePicker(k,R,Y)}}),(k,R)=>(M(),Te(_t,{"multi-calendars":0,stretch:"","is-mobile":k.isMobile},{default:ke(()=>[ta(en,We({ref_key:"tpRef",ref:f},k.$props,{hours:o(d).hours,minutes:o(d).minutes,seconds:o(d).seconds,"internal-model-value":k.internalModelValue,"disabled-times-config":o(p),"validate-time":o(m),"onUpdate:hours":R[0]||(R[0]=Y=>o(v)(Y)),"onUpdate:minutes":R[1]||(R[1]=Y=>o(v)(Y,!1)),"onUpdate:seconds":R[2]||(R[2]=Y=>o(v)(Y,!1,!0)),onAmPmChange:R[3]||(R[3]=Y=>k.$emit("am-pm-change",Y)),onResetFlow:R[4]||(R[4]=Y=>k.$emit("reset-flow")),onOverlayClosed:R[5]||(R[5]=Y=>k.$emit("overlay-toggle",{open:!1,overlay:Y})),onOverlayOpened:R[6]||(R[6]=Y=>k.$emit("overlay-toggle",{open:!0,overlay:Y}))}),Je({_:2},[Ve(o(i),(Y,z)=>({name:Y,fn:ke(P=>[ie(k.$slots,Y,je(la(P)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"])]),_:3},8,["is-mobile"]))}}),Kr={class:"dp--header-wrap"},qr={key:0,class:"dp__month_year_wrap"},Qr={key:0},Zr={class:"dp__month_year_wrap"},Gr=["data-dp-element","aria-label","data-test-id","onClick","onKeydown"],Xr=qe({compatConfig:{MODE:3},__name:"DpHeader",props:{month:{type:Number,default:0},year:{type:Number,default:0},instance:{type:Number,default:0},years:{type:Array,default:()=>[]},months:{type:Array,default:()=>[]},...ma},emits:["update-month-year","mount","reset-flow","overlay-closed","overlay-opened"],setup(e,{expose:a,emit:n}){const t=n,l=e,{defaultedTransitions:s,defaultedAriaLabels:i,defaultedMultiCalendars:f,defaultedFilters:d,defaultedConfig:T,defaultedHighlight:p,propDates:v,defaultedUI:m}=Be(l),{transitionName:k,showTransition:R}=ut(s),{buildMatrix:Y}=Oa(),{handleMonthYearChange:z,isDisabled:P,updateMonthYear:N}=gr(l,t),{showLeftIcon:x,showRightIcon:U}=Dt(),Q=Z(!1),S=Z(!1),O=Z(!1),ne=Z([null,null,null,null]);Qe(()=>{t("mount")});const L=c=>({get:()=>l[c],set:_=>{const B=c===ia.month?ia.year:ia.month;t("update-month-year",{[c]:_,[B]:l[B]}),c===ia.month?F(!0):J(!0)}}),C=W(L(ia.month)),oe=W(L(ia.year)),ce=W(()=>c=>({month:l.month,year:l.year,items:c===ia.month?l.months:l.years,instance:l.instance,updateMonthYear:N,toggle:c===ia.month?F:J})),ye=W(()=>l.months.find(_=>_.value===l.month)||{text:"",value:0}),y=W(()=>Ka(l.months,c=>{const _=l.month===c.value,B=tt(c.value,Ul(l.year,v.value.minDate),El(l.year,v.value.maxDate))||d.value.months.includes(c.value),E=ql(p.value,c.value,l.year);return{active:_,disabled:B,highlighted:E}})),D=W(()=>Ka(l.years,c=>{const _=l.year===c.value,B=tt(c.value,qa(v.value.minDate),qa(v.value.maxDate))||d.value.years.includes(c.value),E=rl(p.value,c.value);return{active:_,disabled:B,highlighted:E}})),h=(c,_,B)=>{B!==void 0?c.value=B:c.value=!c.value,c.value?(O.value=!0,t("overlay-opened",_)):(O.value=!1,t("overlay-closed",_))},F=(c=!1,_)=>{V(c),h(Q,Xe.month,_)},J=(c=!1,_)=>{V(c),h(S,Xe.year,_)},V=c=>{c||t("reset-flow")},ee=(c,_)=>{l.arrowNavigation&&(ne.value[_]=ze(c),Y(ne.value,"monthYear"))},$=W(()=>{var c,_,B,E,r,w;return[{type:ia.month,index:1,toggle:F,modelValue:C.value,updateModelValue:ue=>C.value=ue,text:ye.value.text,showSelectionGrid:Q.value,items:y.value,ariaLabel:(c=i.value)==null?void 0:c.openMonthsOverlay,overlayLabel:((B=(_=i.value).monthPicker)==null?void 0:B.call(_,!0))??void 0},{type:ia.year,index:2,toggle:J,modelValue:oe.value,updateModelValue:ue=>oe.value=ue,text:Fl(l.year,l.locale),showSelectionGrid:S.value,items:D.value,ariaLabel:(E=i.value)==null?void 0:E.openYearsOverlay,overlayLabel:((w=(r=i.value).yearPicker)==null?void 0:w.call(r,!0))??void 0}]}),de=W(()=>l.disableYearSelect?[$.value[0]]:l.yearFirst?[...$.value].reverse():$.value);return a({toggleMonthPicker:F,toggleYearPicker:J,handleMonthYearChange:z}),(c,_)=>{var B,E,r,w,ue,xe;return M(),H("div",Kr,[c.$slots["month-year"]?(M(),H("div",qr,[ie(c.$slots,"month-year",je(la({month:e.month,year:e.year,months:e.months,years:e.years,updateMonthYear:o(N),handleMonthYearChange:o(z),instance:e.instance,isDisabled:o(P)})))])):(M(),H(_e,{key:1},[c.$slots["top-extra"]?(M(),H("div",Qr,[ie(c.$slots,"top-extra",{value:c.internalModelValue})])):q("",!0),me("div",Zr,[o(x)(o(f),e.instance)&&!c.vertical?(M(),Te(Ja,{key:0,"aria-label":(B=o(i))==null?void 0:B.prevMonth,disabled:o(P)(!1),class:De((E=o(m))==null?void 0:E.navBtnPrev),"el-name":"action-prev",onActivate:_[0]||(_[0]=A=>o(z)(!1,!0)),onSetRef:_[1]||(_[1]=A=>ee(A,0))},{default:ke(()=>[c.$slots["arrow-left"]?ie(c.$slots,"arrow-left",{key:0}):q("",!0),c.$slots["arrow-left"]?q("",!0):(M(),Te(o(Zt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):q("",!0),me("div",{class:De(["dp__month_year_wrap",{dp__year_disable_select:c.disableYearSelect}])},[(M(!0),H(_e,null,Ve(de.value,(A,ge)=>(M(),H(_e,{key:A.type},[me("button",{ref_for:!0,ref:K=>ee(K,ge+1),type:"button","data-dp-element":`overlay-${A.type}`,class:De(["dp__btn dp__month_year_select",{"dp--hidden-el":O.value}]),"aria-label":`${A.text}-${A.ariaLabel}`,"data-test-id":`${A.type}-toggle-overlay-${e.instance}`,onClick:A.toggle,onKeydown:K=>o(aa)(K,()=>A.toggle(),!0)},[c.$slots[A.type]?ie(c.$slots,A.type,{key:0,text:A.text,value:l[A.type]}):q("",!0),c.$slots[A.type]?q("",!0):(M(),H(_e,{key:1},[_a(Ze(A.text),1)],64))],42,Gr),ta(Za,{name:o(k)(A.showSelectionGrid),css:o(R)},{default:ke(()=>[A.showSelectionGrid?(M(),Te(rt,{key:0,items:A.items,"arrow-navigation":c.arrowNavigation,"hide-navigation":c.hideNavigation,"is-last":c.autoApply&&!o(T).keepActionRow,"skip-button-ref":!1,config:c.config,type:A.type,"header-refs":[],"esc-close":c.escClose,"menu-wrap-ref":c.menuWrapRef,"text-input":c.textInput,"aria-labels":c.ariaLabels,"overlay-label":A.overlayLabel,onSelected:A.updateModelValue,onToggle:A.toggle},Je({"button-icon":ke(()=>[c.$slots["calendar-icon"]?ie(c.$slots,"calendar-icon",{key:0}):q("",!0),c.$slots["calendar-icon"]?q("",!0):(M(),Te(o(Ga),{key:1}))]),_:2},[c.$slots[`${A.type}-overlay-value`]?{name:"item",fn:ke(({item:K})=>[ie(c.$slots,`${A.type}-overlay-value`,{text:K.text,value:K.value})]),key:"0"}:void 0,c.$slots[`${A.type}-overlay`]?{name:"overlay",fn:ke(()=>[ie(c.$slots,`${A.type}-overlay`,We({ref_for:!0},ce.value(A.type)))]),key:"1"}:void 0,c.$slots[`${A.type}-overlay-header`]?{name:"header",fn:ke(()=>[ie(c.$slots,`${A.type}-overlay-header`,{toggle:A.toggle})]),key:"2"}:void 0]),1032,["items","arrow-navigation","hide-navigation","is-last","config","type","esc-close","menu-wrap-ref","text-input","aria-labels","overlay-label","onSelected","onToggle"])):q("",!0)]),_:2},1032,["name","css"])],64))),128))],2),o(x)(o(f),e.instance)&&c.vertical?(M(),Te(Ja,{key:1,"aria-label":(r=o(i))==null?void 0:r.prevMonth,"el-name":"action-prev",disabled:o(P)(!1),class:De((w=o(m))==null?void 0:w.navBtnPrev),onActivate:_[2]||(_[2]=A=>o(z)(!1,!0))},{default:ke(()=>[c.$slots["arrow-up"]?ie(c.$slots,"arrow-up",{key:0}):q("",!0),c.$slots["arrow-up"]?q("",!0):(M(),Te(o(Jt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):q("",!0),o(U)(o(f),e.instance)?(M(),Te(Ja,{key:2,ref:"rightIcon","el-name":"action-next",disabled:o(P)(!0),"aria-label":(ue=o(i))==null?void 0:ue.nextMonth,class:De((xe=o(m))==null?void 0:xe.navBtnNext),onActivate:_[3]||(_[3]=A=>o(z)(!0,!0)),onSetRef:_[4]||(_[4]=A=>ee(A,c.disableYearSelect?2:3))},{default:ke(()=>[c.$slots[c.vertical?"arrow-down":"arrow-right"]?ie(c.$slots,c.vertical?"arrow-down":"arrow-right",{key:0}):q("",!0),c.$slots[c.vertical?"arrow-down":"arrow-right"]?q("",!0):(M(),Te(bt(c.vertical?o(el):o(Gt)),{key:1}))]),_:3},8,["disabled","aria-label","class"])):q("",!0)])],64))])}}}),Jr={class:"dp__calendar_header",role:"row"},eu={key:0,class:"dp__calendar_header_item",role:"gridcell"},au=["aria-label"],tu={key:0,class:"dp__calendar_item dp__week_num",role:"gridcell"},lu={class:"dp__cell_inner"},nu=["id","aria-pressed","aria-disabled","aria-label","tabindex","data-test-id","onClick","onTouchend","onKeydown","onMouseenter","onMouseleave","onMousedown"],ru=qe({compatConfig:{MODE:3},__name:"DpCalendar",props:{mappedDates:{type:Array,default:()=>[]},instance:{type:Number,default:0},month:{type:Number,default:0},year:{type:Number,default:0},...ma},emits:["select-date","set-hover-date","handle-scroll","mount","handle-swipe","handle-space","tooltip-open","tooltip-close"],setup(e,{expose:a,emit:n}){const t=n,l=e,{buildMultiLevelMatrix:s}=Oa(),{defaultedTransitions:i,defaultedConfig:f,defaultedAriaLabels:d,defaultedMultiCalendars:T,defaultedWeekNumbers:p,defaultedMultiDates:v,defaultedUI:m}=Be(l),k=Z(null),R=Z({bottom:"",left:"",transform:""}),Y=Z([]),z=Z(null),P=Z(!0),N=Z(""),x=Z({startX:0,endX:0,startY:0,endY:0}),U=Z([]),Q=Z({left:"50%"}),S=Z(!1),O=W(()=>l.calendar?l.calendar(l.mappedDates):l.mappedDates),ne=W(()=>l.dayNames?Array.isArray(l.dayNames)?l.dayNames:l.dayNames(l.locale,+l.weekStart):Nn(l.formatLocale,l.locale,+l.weekStart));Qe(()=>{t("mount",{cmp:"calendar",refs:Y}),f.value.noSwipe||z.value&&(z.value.addEventListener("touchstart",ee,{passive:!1}),z.value.addEventListener("touchend",$,{passive:!1}),z.value.addEventListener("touchmove",de,{passive:!1})),l.monthChangeOnScroll&&z.value&&z.value.addEventListener("wheel",B,{passive:!1})}),Qa(()=>{f.value.noSwipe||z.value&&(z.value.removeEventListener("touchstart",ee),z.value.removeEventListener("touchend",$),z.value.removeEventListener("touchmove",de)),l.monthChangeOnScroll&&z.value&&z.value.removeEventListener("wheel",B)});const L=A=>A?l.vertical?"vNext":"next":l.vertical?"vPrevious":"previous",C=(A,ge)=>{if(l.transitions){const K=Ke(Ma(j(),l.month,l.year));N.value=Le(Ke(Ma(j(),A,ge)),K)?i.value[L(!0)]:i.value[L(!1)],P.value=!1,oa(()=>{P.value=!0})}},oe=W(()=>({...m.value.calendar??{}})),ce=W(()=>A=>{const ge=Ln(A);return{dp__marker_dot:ge.type==="dot",dp__marker_line:ge.type==="line"}}),ye=W(()=>A=>Ae(A,k.value)),y=W(()=>({dp__calendar:!0,dp__calendar_next:T.value.count>0&&l.instance!==0})),D=W(()=>A=>l.hideOffsetDates?A.current:!0),h=async(A,ge)=>{const{width:K,height:ae}=A.getBoundingClientRect();k.value=ge.value;let u={left:`${K/2}px`},X=-50;if(await oa(),U.value[0]){const{left:te,width:le}=U.value[0].getBoundingClientRect();te<0&&(u={left:"0"},X=0,Q.value.left=`${K/2}px`),window.innerWidth<te+le&&(u={right:"0"},X=0,Q.value.left=`${le-K/2}px`)}R.value={bottom:`${ae}px`,...u,transform:`translateX(${X}%)`}},F=async(A,ge,K)=>{var ae,u,X;const te=ze(Y.value[ge][K]);te&&((ae=A.marker)!=null&&ae.customPosition&&(X=(u=A.marker)==null?void 0:u.tooltip)!=null&&X.length?R.value=A.marker.customPosition(te):await h(te,A),t("tooltip-open",A.marker))},J=async(A,ge,K)=>{var ae,u;if(S.value&&v.value.enabled&&v.value.dragSelect)return t("select-date",A);if(t("set-hover-date",A),(u=(ae=A.marker)==null?void 0:ae.tooltip)!=null&&u.length){if(l.hideOffsetDates&&!A.current)return;await F(A,ge,K)}},V=A=>{k.value&&(k.value=null,R.value=JSON.parse(JSON.stringify({bottom:"",left:"",transform:""})),t("tooltip-close",A.marker))},ee=A=>{x.value.startX=A.changedTouches[0].screenX,x.value.startY=A.changedTouches[0].screenY},$=A=>{x.value.endX=A.changedTouches[0].screenX,x.value.endY=A.changedTouches[0].screenY,c()},de=A=>{l.vertical&&!l.inline&&A.preventDefault()},c=()=>{const A=l.vertical?"Y":"X";Math.abs(x.value[`start${A}`]-x.value[`end${A}`])>10&&t("handle-swipe",x.value[`start${A}`]>x.value[`end${A}`]?"right":"left")},_=(A,ge,K)=>{A&&(Array.isArray(Y.value[ge])?Y.value[ge][K]=A:Y.value[ge]=[A]),l.arrowNavigation&&s(Y.value,"calendar")},B=A=>{l.monthChangeOnScroll&&(A.preventDefault(),t("handle-scroll",A))},E=A=>p.value.type==="local"?hn(A.value,{weekStartsOn:+l.weekStart}):p.value.type==="iso"?gn(A.value):typeof p.value.type=="function"?p.value.type(A.value):"",r=A=>{const ge=A[0];return p.value.hideOnOffsetDates?A.some(K=>K.current)?E(ge):"":E(ge)},w=(A,ge,K=!0)=>{!K&&jn()||(!v.value.enabled||f.value.allowPreventDefault)&&(Sa(A,f.value),t("select-date",ge))},ue=A=>{Sa(A,f.value)},xe=A=>{v.value.enabled&&v.value.dragSelect?(S.value=!0,t("select-date",A)):v.value.enabled&&t("select-date",A)};return a({triggerTransition:C}),(A,ge)=>(M(),H("div",{class:De(y.value)},[me("div",{ref_key:"calendarWrapRef",ref:z,class:De(oe.value),role:"grid"},[me("div",Jr,[A.weekNumbers?(M(),H("div",eu,Ze(A.weekNumName),1)):q("",!0),(M(!0),H(_e,null,Ve(ne.value,(K,ae)=>{var u,X;return M(),H("div",{key:ae,class:"dp__calendar_header_item",role:"gridcell","data-test-id":"calendar-header","aria-label":(X=(u=o(d))==null?void 0:u.weekDay)==null?void 0:X.call(u,ae)},[A.$slots["calendar-header"]?ie(A.$slots,"calendar-header",{key:0,day:K,index:ae}):q("",!0),A.$slots["calendar-header"]?q("",!0):(M(),H(_e,{key:1},[_a(Ze(K),1)],64))],8,au)}),128))]),ge[2]||(ge[2]=me("div",{class:"dp__calendar_header_separator"},null,-1)),ta(Za,{name:N.value,css:!!A.transitions},{default:ke(()=>[P.value?(M(),H("div",{key:0,class:"dp__calendar",role:"rowgroup",onMouseleave:ge[1]||(ge[1]=K=>S.value=!1)},[(M(!0),H(_e,null,Ve(O.value,(K,ae)=>(M(),H("div",{key:ae,class:"dp__calendar_row",role:"row"},[A.weekNumbers?(M(),H("div",tu,[me("div",lu,Ze(r(K.days)),1)])):q("",!0),(M(!0),H(_e,null,Ve(K.days,(u,X)=>{var te,le,Me;return M(),H("div",{id:o(Kt)(u.value),ref_for:!0,ref:be=>_(be,ae,X),key:X+ae,role:"gridcell",class:"dp__calendar_item","aria-pressed":(u.classData.dp__active_date||u.classData.dp__range_start||u.classData.dp__range_start)??void 0,"aria-disabled":u.classData.dp__cell_disabled||void 0,"aria-label":(le=(te=o(d))==null?void 0:te.day)==null?void 0:le.call(te,u),tabindex:!u.current&&A.hideOffsetDates?void 0:0,"data-test-id":o(Kt)(u.value),onClick:at(be=>w(be,u),["prevent"]),onTouchend:be=>w(be,u,!1),onKeydown:be=>o(aa)(be,()=>A.$emit("select-date",u)),onMouseenter:be=>J(u,ae,X),onMouseleave:be=>V(u),onMousedown:be=>xe(u),onMouseup:ge[0]||(ge[0]=be=>S.value=!1)},[me("div",{class:De(["dp__cell_inner",u.classData])},[A.$slots.day&&D.value(u)?ie(A.$slots,"day",{key:0,day:+u.text,date:u.value}):q("",!0),A.$slots.day?q("",!0):(M(),H(_e,{key:1},[_a(Ze(u.text),1)],64)),u.marker&&D.value(u)?(M(),H(_e,{key:2},[A.$slots.marker?ie(A.$slots,"marker",{key:0,marker:u.marker,day:+u.text,date:u.value}):(M(),H("div",{key:1,class:De(ce.value(u.marker)),style:sa(u.marker.color?{backgroundColor:u.marker.color}:{})},null,6))],64)):q("",!0),ye.value(u.value)?(M(),H("div",{key:3,ref_for:!0,ref_key:"activeTooltip",ref:U,class:"dp__marker_tooltip",style:sa(R.value)},[(Me=u.marker)!=null&&Me.tooltip?(M(),H("div",{key:0,class:"dp__tooltip_content",onClick:ue},[(M(!0),H(_e,null,Ve(u.marker.tooltip,(be,Re)=>(M(),H("div",{key:Re,class:"dp__tooltip_text"},[A.$slots["marker-tooltip"]?ie(A.$slots,"marker-tooltip",{key:0,tooltip:be,day:u.value}):q("",!0),A.$slots["marker-tooltip"]?q("",!0):(M(),H(_e,{key:1},[me("div",{class:"dp__tooltip_mark",style:sa(be.color?{backgroundColor:be.color}:{})},null,4),me("div",null,Ze(be.text),1)],64))]))),128)),me("div",{class:"dp__arrow_bottom_tp",style:sa(Q.value)},null,4)])):q("",!0)],4)):q("",!0)],2)],40,nu)}),128))]))),128))],32)):q("",!0)]),_:3},8,["name","css"])],2)],2))}}),Dl=e=>Array.isArray(e),uu=(e,a,n,t)=>{const l=Z([]),s=Z(new Date),i=Z(),f=()=>$(e.isTextInputDate),{modelValue:d,calendars:T,time:p,today:v}=ot(e,a,f),{defaultedMultiCalendars:m,defaultedStartTime:k,defaultedRange:R,defaultedConfig:Y,defaultedTz:z,propDates:P,defaultedMultiDates:N}=Be(e),{validateMonthYearInRange:x,isDisabled:U,isDateRangeAllowed:Q,checkMinMaxRange:S}=Ca(e),{updateTimeValues:O,getSetDateTime:ne,setTime:L,assignStartTime:C,validateTime:oe,disabledTimesConfig:ce}=an(e,p,d,t),ye=W(()=>b=>T.value[b]?T.value[b].month:0),y=W(()=>b=>T.value[b]?T.value[b].year:0),D=b=>!Y.value.keepViewOnOffsetClick||b?!0:!i.value,h=(b,G,fe,we=!1)=>{var I,se;D(we)&&(T.value[b]||(T.value[b]={month:0,year:0}),T.value[b].month=hl(G)?(I=T.value[b])==null?void 0:I.month:G,T.value[b].year=hl(fe)?(se=T.value[b])==null?void 0:se.year:fe)},F=()=>{e.autoApply&&a("select-date")},J=()=>{k.value&&C(k.value)};Qe(()=>{e.shadow||(d.value||(ge(),J()),$(!0),e.focusStartDate&&e.startDate&&ge())});const V=W(()=>{var b;return(b=e.flow)!=null&&b.length&&!e.partialFlow?e.flowStep===e.flow.length:!0}),ee=()=>{e.autoApply&&V.value&&a("auto-apply",e.partialFlow?e.flowStep!==e.flow.length:!1)},$=(b=!1)=>{if(d.value)return Array.isArray(d.value)?(l.value=d.value,w(b)):_(d.value,b);if(m.value.count&&b&&!e.startDate)return c(j(),b)},de=()=>Array.isArray(d.value)&&R.value.enabled?$e(d.value[0])===$e(d.value[1]??d.value[0]):!1,c=(b=new Date,G=!1)=>{if((!m.value.count||!m.value.static||G)&&h(0,$e(b),he(b)),m.value.count&&(!d.value||de()||!m.value.solo)&&(!m.value.solo||G))for(let fe=1;fe<m.value.count;fe++){const we=Oe(j(),{month:ye.value(fe-1),year:y.value(fe-1)}),I=$l(we,{months:1});T.value[fe]={month:$e(I),year:he(I)}}},_=(b,G)=>{c(b),L("hours",$a(b)),L("minutes",Ra(b)),L("seconds",Ha(b)),m.value.count&&G&&A()},B=b=>{if(m.value.count){if(m.value.solo)return 0;const G=$e(b[0]),fe=$e(b[1]);return Math.abs(fe-G)<m.value.count?0:1}return 1},E=(b,G)=>{b[1]&&R.value.showLastInRange?c(b[B(b)],G):c(b[0],G);const fe=(we,I)=>[we(b[0]),b[1]?we(b[1]):p[I][1]];L("hours",fe($a,"hours")),L("minutes",fe(Ra,"minutes")),L("seconds",fe(Ha,"seconds"))},r=(b,G)=>{if((R.value.enabled||e.weekPicker)&&!N.value.enabled)return E(b,G);if(N.value.enabled&&G){const fe=b[b.length-1];return _(fe,G)}},w=b=>{const G=d.value;r(G,b),m.value.count&&m.value.solo&&A()},ue=(b,G)=>{const fe=Oe(j(),{month:ye.value(G),year:y.value(G)}),we=b<0?va(fe,1):Wa(fe,1);x($e(we),he(we),b<0,e.preventMinMaxNavigation)&&(h(G,$e(we),he(we)),a("update-month-year",{instance:G,month:$e(we),year:he(we)}),m.value.count&&!m.value.solo&&xe(G),n())},xe=b=>{for(let G=b-1;G>=0;G--){const fe=Wa(Oe(j(),{month:ye.value(G+1),year:y.value(G+1)}),1);h(G,$e(fe),he(fe))}for(let G=b+1;G<=m.value.count-1;G++){const fe=va(Oe(j(),{month:ye.value(G-1),year:y.value(G-1)}),1);h(G,$e(fe),he(fe))}},A=()=>{if(Array.isArray(d.value)&&d.value.length===2){const b=j(j(d.value[1]?d.value[1]:va(d.value[0],1))),[G,fe]=[$e(d.value[0]),he(d.value[0])],[we,I]=[$e(d.value[1]),he(d.value[1])];(G!==we||G===we&&fe!==I)&&m.value.solo&&h(1,$e(b),he(b))}else d.value&&!Array.isArray(d.value)&&(h(0,$e(d.value),he(d.value)),c(j()))},ge=()=>{e.startDate&&(h(0,$e(j(e.startDate)),he(j(e.startDate))),m.value.count&&xe(0))},K=(b,G)=>{if(e.monthChangeOnScroll){const fe=new Date().getTime()-s.value.getTime(),we=Math.abs(b.deltaY);let I=500;we>1&&(I=100),we>100&&(I=0),fe>I&&(s.value=new Date,ue(e.monthChangeOnScroll!=="inverse"?-b.deltaY:b.deltaY,G))}},ae=(b,G,fe=!1)=>{e.monthChangeOnArrows&&e.vertical===fe&&u(b,G)},u=(b,G)=>{ue(b==="right"?-1:1,G)},X=b=>{if(P.value.markers)return yt(b.value,P.value.markers)},te=(b,G)=>{switch(e.sixWeeks===!0?"append":e.sixWeeks){case"prepend":return[!0,!1];case"center":return[b==0,!0];case"fair":return[b==0||G>b,!0];case"append":return[!1,!1];default:return[!1,!1]}},le=(b,G,fe,we)=>{if(e.sixWeeks&&b.length<6){const I=6-b.length,se=(G.getDay()+7-we)%7,pe=6-(fe.getDay()+7-we)%7,[ka,ha]=te(se,pe);for(let Ya=1;Ya<=I;Ya++)if(ha?!!(Ya%2)==ka:ka){const Aa=b[0].days[0],xt=Me(ga(Aa.value,-7),$e(G));b.unshift({days:xt})}else{const Aa=b[b.length-1],xt=Aa.days[Aa.days.length-1],ln=Me(ga(xt.value,1),$e(G));b.push({days:ln})}}return b},Me=(b,G)=>{const fe=j(b),we=[];for(let I=0;I<7;I++){const se=ga(fe,I),pe=$e(se)!==G;we.push({text:e.hideOffsetDates&&pe?"":se.getDate(),value:se,current:!pe,classData:{}})}return we},be=(b,G)=>{const fe=[],we=new Date(G,b),I=new Date(G,b+1,0),se=e.weekStart,pe=qt(we,{weekStartsOn:se}),ka=ha=>{const Ya=Me(ha,b);if(fe.push({days:Ya}),!fe[fe.length-1].days.some(Aa=>Ae(Ke(Aa.value),Ke(I)))){const Aa=ga(ha,7);ka(Aa)}};return ka(pe),le(fe,we,I,se)},Re=b=>{const G=xa(j(b.value),p.hours,p.minutes,ve());a("date-update",G),N.value.enabled?ul(G,d,N.value.limit):d.value=G,t(),oa().then(()=>{ee()})},Ne=b=>R.value.noDisabledRange?Hl(l.value[0],b).some(G=>U(G)):!1,g=()=>{l.value=d.value?d.value.slice():[],l.value.length===2&&!(R.value.fixedStart||R.value.fixedEnd)&&(l.value=[])},re=(b,G)=>{const fe=[j(b.value),ga(j(b.value),+R.value.autoRange)];Q(fe)?(G&&Pe(b.value),l.value=fe):a("invalid-date",b.value)},Pe=b=>{const G=$e(j(b)),fe=he(j(b));if(h(0,G,fe),m.value.count>0)for(let we=1;we<m.value.count;we++){const I=qn(Oe(j(b),{year:y.value(we-1),month:ye.value(we-1)}));h(we,I.month,I.year)}},Ue=b=>{if(Ne(b.value)||!S(b.value,d.value,R.value.fixedStart?0:1))return a("invalid-date",b.value);l.value=Xl(j(b.value),d,a,R)},fa=(b,G)=>{if(g(),R.value.autoRange)return re(b,G);if(R.value.fixedStart||R.value.fixedEnd)return Ue(b);l.value[0]?S(j(b.value),d.value)&&!Ne(b.value)?Ie(j(b.value),j(l.value[0]))?(l.value.unshift(j(b.value)),a("range-end",l.value[0])):(l.value[1]=j(b.value),a("range-end",l.value[1])):(e.autoApply&&a("auto-apply-invalid",b.value),a("invalid-date",b.value)):(l.value[0]=j(b.value),a("range-start",l.value[0]))},ve=(b=!0)=>e.enableSeconds?Array.isArray(p.seconds)?b?p.seconds[0]:p.seconds[1]:p.seconds:0,Ee=b=>{l.value[b]=xa(l.value[b],p.hours[b],p.minutes[b],ve(b!==1))},ya=()=>{var b,G;l.value[0]&&l.value[1]&&+((b=l.value)==null?void 0:b[0])>+((G=l.value)==null?void 0:G[1])&&(l.value.reverse(),a("range-start",l.value[0]),a("range-end",l.value[1]))},Mt=()=>{l.value.length&&(l.value[0]&&!l.value[1]?Ee(0):(Ee(0),Ee(1),t()),ya(),d.value=l.value.slice(),kt(l.value,a,e.autoApply,e.modelAuto))},st=(b,G=!1)=>{if(U(b.value)||!b.current&&e.hideOffsetDates)return a("invalid-date",b.value);if(i.value=JSON.parse(JSON.stringify(b)),!R.value.enabled)return Re(b);Dl(p.hours)&&Dl(p.minutes)&&!N.value.enabled&&(fa(b,G),Mt())},$t=(b,G)=>{var fe;h(b,G.month,G.year,!0),m.value.count&&!m.value.solo&&xe(b),a("update-month-year",{instance:b,month:G.month,year:G.year}),n(m.value.solo?b:void 0);const we=(fe=e.flow)!=null&&fe.length?e.flow[e.flowStep]:void 0;!G.fromNav&&(we===Xe.month||we===Xe.year)&&t()},At=(b,G)=>{Gl({value:b,modelValue:d,range:R.value.enabled,timezone:G?void 0:z.value.timezone}),F(),e.multiCalendars&&oa().then(()=>$(!0))},Tt=()=>{const b=al(j(),z.value);!R.value.enabled&&!N.value.enabled?d.value=b:d.value&&Array.isArray(d.value)&&d.value[0]?N.value.enabled?d.value=[...d.value,b]:d.value=Ie(b,d.value[0])?[b,d.value[0]]:[d.value[0],b]:d.value=[b],F()},Pt=()=>{if(Array.isArray(d.value))if(N.value.enabled){const b=St();d.value[d.value.length-1]=ne(b)}else d.value=d.value.map((b,G)=>b&&ne(b,G));else d.value=ne(d.value);a("time-update")},St=()=>Array.isArray(d.value)&&d.value.length?d.value[d.value.length-1]:null;return{calendars:T,modelValue:d,month:ye,year:y,time:p,disabledTimesConfig:ce,today:v,validateTime:oe,getCalendarDays:be,getMarker:X,handleScroll:K,handleSwipe:u,handleArrow:ae,selectDate:st,updateMonthYear:$t,presetDate:At,selectCurrentDate:Tt,updateTime:(b,G=!0,fe=!1)=>{O(b,G,fe,Pt)},assignMonthAndYear:c,setStartTime:J}},ou={key:0},su=qe({__name:"DatePicker",props:{...ma},emits:["tooltip-open","tooltip-close","mount","update:internal-model-value","update-flow-step","reset-flow","auto-apply","focus-menu","select-date","range-start","range-end","invalid-fixed-range","time-update","am-pm-change","time-picker-open","time-picker-close","recalculate-position","update-month-year","auto-apply-invalid","date-update","invalid-date","overlay-toggle"],setup(e,{expose:a,emit:n}){const t=n,l=e,{calendars:s,month:i,year:f,modelValue:d,time:T,disabledTimesConfig:p,today:v,validateTime:m,getCalendarDays:k,getMarker:R,handleArrow:Y,handleScroll:z,handleSwipe:P,selectDate:N,updateMonthYear:x,presetDate:U,selectCurrentDate:Q,updateTime:S,assignMonthAndYear:O,setStartTime:ne}=uu(l,t,de,c),L=Fa(),{setHoverDate:C,getDayClassData:oe,clearHoverDate:ce}=$u(d,l),{defaultedMultiCalendars:ye}=Be(l),y=Z([]),D=Z([]),h=Z(null),F=ua(L,"calendar"),J=ua(L,"monthYear"),V=ua(L,"timePicker"),ee=K=>{l.shadow||t("mount",K)};na(s,()=>{l.shadow||setTimeout(()=>{t("recalculate-position")},0)},{deep:!0}),na(ye,(K,ae)=>{K.count-ae.count>0&&O()},{deep:!0});const $=W(()=>K=>k(i.value(K),f.value(K)).map(ae=>({...ae,days:ae.days.map(u=>(u.marker=R(u),u.classData=oe(u),u))})));function de(K){var ae;K||K===0?(ae=D.value[K])==null||ae.triggerTransition(i.value(K),f.value(K)):D.value.forEach((u,X)=>u.triggerTransition(i.value(X),f.value(X)))}function c(){t("update-flow-step")}const _=(K,ae=!1)=>{N(K,ae),l.spaceConfirm&&t("select-date")},B=(K,ae,u=0)=>{var X;(X=y.value[u])==null||X.toggleMonthPicker(K,ae)},E=(K,ae,u=0)=>{var X;(X=y.value[u])==null||X.toggleYearPicker(K,ae)},r=(K,ae,u)=>{var X;(X=h.value)==null||X.toggleTimePicker(K,ae,u)},w=(K,ae)=>{var u;if(!l.range){const X=d.value?d.value:v,te=ae?new Date(ae):X,le=K?qt(te,{weekStartsOn:1}):Sl(te,{weekStartsOn:1});N({value:le,current:$e(te)===i.value(0),text:"",classData:{}}),(u=document.getElementById(Kt(le)))==null||u.focus()}},ue=K=>{var ae;(ae=y.value[0])==null||ae.handleMonthYearChange(K,!0)},xe=K=>{x(0,{month:i.value(0),year:f.value(0)+(K?1:-1),fromNav:!0})},A=(K,ae)=>{K===Xe.time&&t(`time-picker-${ae?"open":"close"}`),t("overlay-toggle",{open:ae,overlay:K})},ge=K=>{t("overlay-toggle",{open:!1,overlay:K}),t("focus-menu")};return a({clearHoverDate:ce,presetDate:U,selectCurrentDate:Q,toggleMonthPicker:B,toggleYearPicker:E,toggleTimePicker:r,handleArrow:Y,updateMonthYear:x,getSidebarProps:()=>({modelValue:d,month:i,year:f,time:T,updateTime:S,updateMonthYear:x,selectDate:N,presetDate:U}),changeMonth:ue,changeYear:xe,selectWeekDate:w,setStartTime:ne}),(K,ae)=>(M(),H(_e,null,[ta(_t,{"multi-calendars":o(ye).count,collapse:K.collapse,"is-mobile":K.isMobile},{default:ke(({instance:u,index:X})=>[K.disableMonthYearSelect?q("",!0):(M(),Te(Xr,We({key:0,ref:te=>{te&&(y.value[X]=te)},months:o(Bl)(K.formatLocale,K.locale,K.monthNameFormat),years:o(tl)(K.yearRange,K.locale,K.reverseYears),month:o(i)(u),year:o(f)(u),instance:u},K.$props,{onMount:ae[0]||(ae[0]=te=>ee(o(Ba).header)),onResetFlow:ae[1]||(ae[1]=te=>K.$emit("reset-flow")),onUpdateMonthYear:te=>o(x)(u,te),onOverlayClosed:ge,onOverlayOpened:ae[2]||(ae[2]=te=>K.$emit("overlay-toggle",{open:!0,overlay:te}))}),Je({_:2},[Ve(o(J),(te,le)=>({name:te,fn:ke(Me=>[ie(K.$slots,te,je(la(Me)))])}))]),1040,["months","years","month","year","instance","onUpdateMonthYear"])),ta(ru,We({ref:te=>{te&&(D.value[X]=te)},"mapped-dates":$.value(u),month:o(i)(u),year:o(f)(u),instance:u},K.$props,{onSelectDate:te=>o(N)(te,u!==1),onHandleSpace:te=>_(te,u!==1),onSetHoverDate:ae[3]||(ae[3]=te=>o(C)(te)),onHandleScroll:te=>o(z)(te,u),onHandleSwipe:te=>o(P)(te,u),onMount:ae[4]||(ae[4]=te=>ee(o(Ba).calendar)),onResetFlow:ae[5]||(ae[5]=te=>K.$emit("reset-flow")),onTooltipOpen:ae[6]||(ae[6]=te=>K.$emit("tooltip-open",te)),onTooltipClose:ae[7]||(ae[7]=te=>K.$emit("tooltip-close",te))}),Je({_:2},[Ve(o(F),(te,le)=>({name:te,fn:ke(Me=>[ie(K.$slots,te,je(la({...Me})))])}))]),1040,["mapped-dates","month","year","instance","onSelectDate","onHandleSpace","onHandleScroll","onHandleSwipe"])]),_:3},8,["multi-calendars","collapse","is-mobile"]),K.enableTimePicker?(M(),H("div",ou,[K.$slots["time-picker"]?ie(K.$slots,"time-picker",je(We({key:0},{time:o(T),updateTime:o(S)}))):(M(),Te(en,We({key:1,ref_key:"timePickerRef",ref:h},K.$props,{hours:o(T).hours,minutes:o(T).minutes,seconds:o(T).seconds,"internal-model-value":K.internalModelValue,"disabled-times-config":o(p),"validate-time":o(m),onMount:ae[8]||(ae[8]=u=>ee(o(Ba).timePicker)),"onUpdate:hours":ae[9]||(ae[9]=u=>o(S)(u)),"onUpdate:minutes":ae[10]||(ae[10]=u=>o(S)(u,!1)),"onUpdate:seconds":ae[11]||(ae[11]=u=>o(S)(u,!1,!0)),onResetFlow:ae[12]||(ae[12]=u=>K.$emit("reset-flow")),onOverlayClosed:ae[13]||(ae[13]=u=>A(u,!1)),onOverlayOpened:ae[14]||(ae[14]=u=>A(u,!0)),onAmPmChange:ae[15]||(ae[15]=u=>K.$emit("am-pm-change",u))}),Je({_:2},[Ve(o(V),(u,X)=>({name:u,fn:ke(te=>[ie(K.$slots,u,je(la(te)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"]))])):q("",!0)],64))}}),iu=(e,a)=>{const n=Z(),{defaultedMultiCalendars:t,defaultedConfig:l,defaultedHighlight:s,defaultedRange:i,propDates:f,defaultedFilters:d,defaultedMultiDates:T}=Be(e),{modelValue:p,year:v,month:m,calendars:k}=ot(e,a),{isDisabled:R}=Ca(e),{selectYear:Y,groupedYears:z,showYearPicker:P,isDisabled:N,toggleYearPicker:x,handleYearSelect:U,handleYear:Q}=Jl({modelValue:p,multiCalendars:t,range:i,highlight:s,calendars:k,propDates:f,month:m,year:v,filters:d,props:e,emit:a}),S=(D,h)=>[D,h].map(F=>wa(F,"MMMM",{locale:e.formatLocale})).join("-"),O=W(()=>D=>p.value?Array.isArray(p.value)?p.value.some(h=>ml(D,h)):ml(p.value,D):!1),ne=D=>{if(i.value.enabled){if(Array.isArray(p.value)){const h=Ae(D,p.value[0])||Ae(D,p.value[1]);return lt(p.value,n.value,D)&&!h}return!1}return!1},L=(D,h)=>D.quarter===vl(h)&&D.year===he(h),C=D=>typeof s.value=="function"?s.value({quarter:vl(D),year:he(D)}):!!s.value.quarters.find(h=>L(h,D)),oe=W(()=>D=>{const h=Oe(new Date,{year:v.value(D)});return An({start:ft(h),end:Pl(h)}).map(F=>{const J=Va(F),V=pl(F),ee=R(F),$=ne(J),de=C(J);return{text:S(J,V),value:J,active:O.value(J),highlighted:de,disabled:ee,isBetween:$}})}),ce=D=>{ul(D,p,T.value.limit),a("auto-apply",!0)},ye=D=>{p.value=ol(p,D,a),kt(p.value,a,e.autoApply,e.modelAuto)},y=D=>{p.value=D,a("auto-apply")};return{defaultedConfig:l,defaultedMultiCalendars:t,groupedYears:z,year:v,isDisabled:N,quarters:oe,showYearPicker:P,modelValue:p,setHoverDate:D=>{n.value=D},selectYear:Y,selectQuarter:(D,h,F)=>{if(!F)return k.value[h].month=$e(pl(D)),T.value.enabled?ce(D):i.value.enabled?ye(D):y(D)},toggleYearPicker:x,handleYearSelect:U,handleYear:Q}},du={class:"dp--quarter-items"},cu=["data-test-id","disabled","onClick","onMouseover"],vu=qe({compatConfig:{MODE:3},__name:"QuarterPicker",props:{...ma},emits:["update:internal-model-value","reset-flow","overlay-closed","auto-apply","range-start","range-end","overlay-toggle","update-month-year"],setup(e,{expose:a,emit:n}){const t=n,l=e,s=Fa(),i=ua(s,"yearMode"),{defaultedMultiCalendars:f,defaultedConfig:d,groupedYears:T,year:p,isDisabled:v,quarters:m,modelValue:k,showYearPicker:R,setHoverDate:Y,selectQuarter:z,toggleYearPicker:P,handleYearSelect:N,handleYear:x}=iu(l,t);return a({getSidebarProps:()=>({modelValue:k,year:p,selectQuarter:z,handleYearSelect:N,handleYear:x})}),(U,Q)=>(M(),Te(_t,{"multi-calendars":o(f).count,collapse:U.collapse,stretch:"","is-mobile":U.isMobile},{default:ke(({instance:S})=>[me("div",{class:"dp-quarter-picker-wrap",style:sa({minHeight:`${o(d).modeHeight}px`})},[U.$slots["top-extra"]?ie(U.$slots,"top-extra",{key:0,value:U.internalModelValue}):q("",!0),me("div",null,[ta(Zl,We(U.$props,{items:o(T)(S),instance:S,"show-year-picker":o(R)[S],year:o(p)(S),"is-disabled":O=>o(v)(S,O),onHandleYear:O=>o(x)(S,O),onYearSelect:O=>o(N)(O,S),onToggleYearPicker:O=>o(P)(S,O==null?void 0:O.flow,O==null?void 0:O.show)}),Je({_:2},[Ve(o(i),(O,ne)=>({name:O,fn:ke(L=>[ie(U.$slots,O,je(la(L)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),me("div",du,[(M(!0),H(_e,null,Ve(o(m)(S),(O,ne)=>(M(),H("div",{key:ne},[me("button",{type:"button",class:De(["dp--qr-btn",{"dp--qr-btn-active":O.active,"dp--qr-btn-between":O.isBetween,"dp--qr-btn-disabled":O.disabled,"dp--highlighted":O.highlighted}]),"data-test-id":O.value,disabled:O.disabled,onClick:L=>o(z)(O.value,S,O.disabled),onMouseover:L=>o(Y)(O.value)},[U.$slots.quarter?ie(U.$slots,"quarter",{key:0,value:O.value,text:O.text}):(M(),H(_e,{key:1},[_a(Ze(O.text),1)],64))],42,cu)]))),128))])],4)]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),tn=(e,a)=>{const n=Z(0);Qe(()=>{t(),window.addEventListener("resize",t,{passive:!0})}),Qa(()=>{window.removeEventListener("resize",t)});const t=()=>{n.value=window.document.documentElement.clientWidth};return{isMobile:W(()=>n.value<=e.value.mobileBreakpoint&&!a?!0:void 0)}},pu=["id","tabindex","role","aria-label"],mu={key:0,class:"dp--menu-load-container"},fu={key:1,class:"dp--menu-header"},yu=["data-dp-mobile"],hu={key:0,class:"dp__sidebar_left"},gu=["data-dp-mobile"],bu=["data-test-id","data-dp-mobile","onClick","onKeydown"],wu={key:2,class:"dp__sidebar_right"},_u={key:3,class:"dp__action_extra"},Ml=qe({compatConfig:{MODE:3},__name:"DatepickerMenu",props:{...wt,shadow:{type:Boolean,default:!1},openOnTop:{type:Boolean,default:!1},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1}},emits:["close-picker","select-date","auto-apply","time-update","flow-step","update-month-year","invalid-select","update:internal-model-value","recalculate-position","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","auto-apply-invalid","date-update","invalid-date","overlay-toggle","menu-blur"],setup(e,{expose:a,emit:n}){const t=n,l=e,s=Z(null),i=W(()=>{const{openOnTop:g,...re}=l;return{...re,isMobile:z.value,flowStep:ye.value,menuWrapRef:s.value}}),{setMenuFocused:f,setShiftKey:d,control:T}=Ql(),p=Fa(),{defaultedTextInput:v,defaultedInline:m,defaultedConfig:k,defaultedUI:R,handleEventPropagation:Y}=Be(l),{isMobile:z}=tn(k,l.shadow),P=Z(null),N=Z(0),x=Z(null),U=Z(!1),Q=Z(null),S=Z(!1),O=g=>{S.value=!0,k.value.allowPreventDefault&&g.preventDefault(),Sa(g,k.value,!0)};Qe(()=>{if(!l.shadow){U.value=!0,ne(),window.addEventListener("resize",ne);const g=ze(s);g&&!v.value.enabled&&!m.value.enabled&&(f(!0),ee()),g&&(g.addEventListener("pointerdown",O),g.addEventListener("mousedown",O))}document.addEventListener("mousedown",Ne)}),Qa(()=>{window.removeEventListener("resize",ne),document.removeEventListener("mousedown",Ne);const g=ze(s);g&&(g.removeEventListener("pointerdown",O),g.removeEventListener("mousedown",O))});const ne=()=>{const g=ze(x);g&&(N.value=g.getBoundingClientRect().width)},{arrowRight:L,arrowLeft:C,arrowDown:oe,arrowUp:ce}=Oa(),{flowStep:ye,updateFlowStep:y,childMount:D,resetFlow:h,handleFlow:F}=Au(l,t,Q),J=W(()=>l.monthPicker?xr:l.yearPicker?Or:l.timePicker?Wr:l.quarterPicker?vu:su),V=W(()=>{var g;if(k.value.arrowLeft)return k.value.arrowLeft;const re=(g=s.value)==null?void 0:g.getBoundingClientRect(),Pe=l.getInputRect();return(Pe==null?void 0:Pe.width)<(N==null?void 0:N.value)&&(Pe==null?void 0:Pe.left)<=((re==null?void 0:re.left)??0)?`${(Pe==null?void 0:Pe.width)/2}px`:(Pe==null?void 0:Pe.right)>=((re==null?void 0:re.right)??0)&&(Pe==null?void 0:Pe.width)<(N==null?void 0:N.value)?`${(N==null?void 0:N.value)-(Pe==null?void 0:Pe.width)/2}px`:"50%"}),ee=()=>{const g=ze(s);g&&g.focus({preventScroll:!0})},$=W(()=>{var g;return((g=Q.value)==null?void 0:g.getSidebarProps())||{}}),de=()=>{l.openOnTop&&t("recalculate-position")},c=ua(p,"action"),_=W(()=>l.monthPicker||l.yearPicker?ua(p,"monthYear"):l.timePicker?ua(p,"timePicker"):ua(p,"shared")),B=W(()=>l.openOnTop?"dp__arrow_bottom":"dp__arrow_top"),E=W(()=>({dp__menu_disabled:l.disabled,dp__menu_readonly:l.readonly,"dp-menu-loading":l.loading})),r=W(()=>({dp__menu:!0,dp__menu_index:!m.value.enabled,dp__relative:m.value.enabled,...R.value.menu??{}})),w=g=>{Sa(g,k.value,!0)},ue=g=>{l.escClose&&(t("close-picker"),Y(g))},xe=g=>{if(l.arrowNavigation){if(g===ea.up)return ce();if(g===ea.down)return oe();if(g===ea.left)return C();if(g===ea.right)return L()}else g===ea.left||g===ea.up?u("handleArrow",ea.left,0,g===ea.up):u("handleArrow",ea.right,0,g===ea.down)},A=g=>{d(g.shiftKey),!l.disableMonthYearSelect&&g.code===Ye.tab&&g.target.classList.contains("dp__menu")&&T.value.shiftKeyInMenu&&(g.preventDefault(),Sa(g,k.value,!0),t("close-picker"))},ge=()=>{ee(),t("time-picker-close")},K=g=>{var re,Pe,Ue;(re=Q.value)==null||re.toggleTimePicker(!1,!1),(Pe=Q.value)==null||Pe.toggleMonthPicker(!1,!1,g),(Ue=Q.value)==null||Ue.toggleYearPicker(!1,!1,g)},ae=(g,re=0)=>{var Pe,Ue,fa;return g==="month"?(Pe=Q.value)==null?void 0:Pe.toggleMonthPicker(!1,!0,re):g==="year"?(Ue=Q.value)==null?void 0:Ue.toggleYearPicker(!1,!0,re):g==="time"?(fa=Q.value)==null?void 0:fa.toggleTimePicker(!0,!1):K(re)},u=(g,...re)=>{var Pe,Ue;(Pe=Q.value)!=null&&Pe[g]&&((Ue=Q.value)==null||Ue[g](...re))},X=()=>{u("selectCurrentDate")},te=(g,re)=>{u("presetDate",dn(g),re)},le=()=>{u("clearHoverDate")},Me=(g,re)=>{u("updateMonthYear",g,re)},be=(g,re)=>{g.preventDefault(),xe(re)},Re=g=>{var re,Pe,Ue;if(A(g),g.key===Ye.home||g.key===Ye.end)return u("selectWeekDate",g.key===Ye.home,g.target.getAttribute("id"));switch((g.key===Ye.pageUp||g.key===Ye.pageDown)&&(g.shiftKey?(u("changeYear",g.key===Ye.pageUp),(re=jt(s.value,"overlay-year"))==null||re.focus()):(u("changeMonth",g.key===Ye.pageUp),(Pe=jt(s.value,g.key===Ye.pageUp?"action-prev":"action-next"))==null||Pe.focus()),g.target.getAttribute("id")&&((Ue=s.value)==null||Ue.focus({preventScroll:!0}))),g.key){case Ye.esc:return ue(g);case Ye.arrowLeft:return be(g,ea.left);case Ye.arrowRight:return be(g,ea.right);case Ye.arrowUp:return be(g,ea.up);case Ye.arrowDown:return be(g,ea.down);default:return}},Ne=g=>{var re;m.value.enabled&&!m.value.input&&!((re=s.value)!=null&&re.contains(g.target))&&S.value&&(S.value=!1,t("menu-blur"))};return a({updateMonthYear:Me,switchView:ae,handleFlow:F,onValueCleared:()=>{var g,re;(re=(g=Q.value)==null?void 0:g.setStartTime)==null||re.call(g)}}),(g,re)=>{var Pe,Ue,fa;return M(),H("div",{id:g.uid?`dp-menu-${g.uid}`:void 0,ref_key:"dpMenuRef",ref:s,tabindex:o(m).enabled?void 0:"0",role:o(m).enabled?void 0:"dialog","aria-label":(Pe=g.ariaLabels)==null?void 0:Pe.menu,class:De(r.value),style:sa({"--dp-arrow-left":V.value}),onMouseleave:le,onClick:w,onKeydown:Re},[(g.disabled||g.readonly)&&o(m).enabled||g.loading?(M(),H("div",{key:0,class:De(E.value)},[g.loading?(M(),H("div",mu,re[19]||(re[19]=[me("span",{class:"dp--menu-loader"},null,-1)]))):q("",!0)],2)):q("",!0),g.$slots["menu-header"]?(M(),H("div",fu,[ie(g.$slots,"menu-header")])):q("",!0),!o(m).enabled&&!g.teleportCenter?(M(),H("div",{key:2,class:De(B.value)},null,2)):q("",!0),me("div",{ref_key:"innerMenuRef",ref:x,class:De({dp__menu_content_wrapper:((Ue=g.presetDates)==null?void 0:Ue.length)||!!g.$slots["left-sidebar"]||!!g.$slots["right-sidebar"],"dp--menu-content-wrapper-collapsed":e.collapse&&(((fa=g.presetDates)==null?void 0:fa.length)||!!g.$slots["left-sidebar"]||!!g.$slots["right-sidebar"])}),"data-dp-mobile":o(z),style:sa({"--dp-menu-width":`${N.value}px`})},[g.$slots["left-sidebar"]?(M(),H("div",hu,[ie(g.$slots,"left-sidebar",je(la($.value)))])):q("",!0),g.presetDates.length?(M(),H("div",{key:1,class:De({"dp--preset-dates-collapsed":e.collapse,"dp--preset-dates":!0}),"data-dp-mobile":o(z)},[(M(!0),H(_e,null,Ve(g.presetDates,(ve,Ee)=>(M(),H(_e,{key:Ee},[ve.slot?ie(g.$slots,ve.slot,{key:0,presetDate:te,label:ve.label,value:ve.value}):(M(),H("button",{key:1,type:"button",style:sa(ve.style||{}),class:De(["dp__btn dp--preset-range",{"dp--preset-range-collapsed":e.collapse}]),"data-test-id":ve.testId??void 0,"data-dp-mobile":o(z),onClick:at(ya=>te(ve.value,ve.noTz),["prevent"]),onKeydown:ya=>o(aa)(ya,()=>te(ve.value,ve.noTz),!0)},Ze(ve.label),47,bu))],64))),128))],10,gu)):q("",!0),me("div",{ref_key:"calendarWrapperRef",ref:P,class:"dp__instance_calendar",role:"document"},[(M(),Te(bt(J.value),We({ref_key:"dynCmpRef",ref:Q},i.value,{"flow-step":o(ye),onMount:o(D),onUpdateFlowStep:o(y),onResetFlow:o(h),onFocusMenu:ee,onSelectDate:re[0]||(re[0]=ve=>g.$emit("select-date")),onDateUpdate:re[1]||(re[1]=ve=>g.$emit("date-update",ve)),onTooltipOpen:re[2]||(re[2]=ve=>g.$emit("tooltip-open",ve)),onTooltipClose:re[3]||(re[3]=ve=>g.$emit("tooltip-close",ve)),onAutoApply:re[4]||(re[4]=ve=>g.$emit("auto-apply",ve)),onRangeStart:re[5]||(re[5]=ve=>g.$emit("range-start",ve)),onRangeEnd:re[6]||(re[6]=ve=>g.$emit("range-end",ve)),onInvalidFixedRange:re[7]||(re[7]=ve=>g.$emit("invalid-fixed-range",ve)),onTimeUpdate:re[8]||(re[8]=ve=>g.$emit("time-update")),onAmPmChange:re[9]||(re[9]=ve=>g.$emit("am-pm-change",ve)),onTimePickerOpen:re[10]||(re[10]=ve=>g.$emit("time-picker-open",ve)),onTimePickerClose:ge,onRecalculatePosition:de,onUpdateMonthYear:re[11]||(re[11]=ve=>g.$emit("update-month-year",ve)),onAutoApplyInvalid:re[12]||(re[12]=ve=>g.$emit("auto-apply-invalid",ve)),onInvalidDate:re[13]||(re[13]=ve=>g.$emit("invalid-date",ve)),onOverlayToggle:re[14]||(re[14]=ve=>g.$emit("overlay-toggle",ve)),"onUpdate:internalModelValue":re[15]||(re[15]=ve=>g.$emit("update:internal-model-value",ve))}),Je({_:2},[Ve(_.value,(ve,Ee)=>({name:ve,fn:ke(ya=>[ie(g.$slots,ve,je(la({...ya})))])}))]),1040,["flow-step","onMount","onUpdateFlowStep","onResetFlow"]))],512),g.$slots["right-sidebar"]?(M(),H("div",wu,[ie(g.$slots,"right-sidebar",je(la($.value)))])):q("",!0),g.$slots["action-extra"]?(M(),H("div",_u,[g.$slots["action-extra"]?ie(g.$slots,"action-extra",{key:0,selectCurrentDate:X}):q("",!0)])):q("",!0)],14,yu),!g.autoApply||o(k).keepActionRow?(M(),Te(_r,We({key:3,"menu-mount":U.value},i.value,{"calendar-width":N.value,onClosePicker:re[16]||(re[16]=ve=>g.$emit("close-picker")),onSelectDate:re[17]||(re[17]=ve=>g.$emit("select-date")),onInvalidSelect:re[18]||(re[18]=ve=>g.$emit("invalid-select")),onSelectNow:X}),Je({_:2},[Ve(o(c),(ve,Ee)=>({name:ve,fn:ke(ya=>[ie(g.$slots,ve,je(la({...ya})))])}))]),1040,["menu-mount","calendar-width"])):q("",!0)],46,pu)}}});var za=(e=>(e.center="center",e.left="left",e.right="right",e))(za||{});const ku=({menuRef:e,menuRefInner:a,inputRef:n,pickerWrapperRef:t,inline:l,emit:s,props:i,slots:f})=>{const{defaultedConfig:d}=Be(i),T=Z({}),p=Z(!1),v=Z({top:"0",left:"0"}),m=Z(!1),k=et(i,"teleportCenter");na(k,()=>{v.value=JSON.parse(JSON.stringify({})),Q()});const R=h=>{if(i.teleport){const F=h.getBoundingClientRect();return{left:F.left+window.scrollX,top:F.top+window.scrollY}}return{top:0,left:0}},Y=(h,F)=>{v.value.left=`${h+F-T.value.width}px`},z=h=>{v.value.left=`${h}px`},P=(h,F)=>{i.position===za.left&&z(h),i.position===za.right&&Y(h,F),i.position===za.center&&(v.value.left=`${h+F/2-T.value.width/2}px`)},N=h=>{const{width:F,height:J}=h.getBoundingClientRect(),{top:V,left:ee}=R(h);return{top:+V,left:+ee,width:F,height:J}},x=()=>{v.value.left="50%",v.value.top="50%",v.value.transform="translate(-50%, -50%)",v.value.position="fixed",delete v.value.opacity},U=()=>{const h=ze(n);v.value=i.altPosition(h)},Q=(h=!0)=>{var F;if(!l.value.enabled){if(k.value)return x();if(i.altPosition!==null)return U();if(h){const J=i.teleport?(F=a.value)==null?void 0:F.$el:e.value;J&&(T.value=J.getBoundingClientRect()),s("recalculate-position")}return ce()}},S=({inputEl:h,left:F,width:J})=>{window.screen.width>768&&!p.value&&P(F,J),L(h)},O=h=>{const{top:F,left:J,height:V,width:ee}=N(h);v.value.top=`${V+F+ +i.offset}px`,m.value=!1,p.value||(v.value.left=`${J+ee/2-T.value.width/2}px`),S({inputEl:h,left:J,width:ee})},ne=h=>{const{top:F,left:J,width:V}=N(h);v.value.top=`${F-+i.offset-T.value.height}px`,m.value=!0,S({inputEl:h,left:J,width:V})},L=h=>{if(i.autoPosition){const{left:F,width:J}=N(h),{left:V,right:ee}=T.value;if(!p.value){if(Math.abs(V)!==Math.abs(ee)){if(V<=0)return p.value=!0,z(F);if(ee>=document.documentElement.clientWidth)return p.value=!0,Y(F,J)}return P(F,J)}}},C=()=>{const h=ze(n);if(h){if(i.autoPosition===da.top)return da.top;if(i.autoPosition===da.bottom)return da.bottom;const{height:F}=T.value,{top:J,height:V}=h.getBoundingClientRect(),ee=window.innerHeight-J-V,$=J;return F<=ee?da.bottom:F>ee&&F<=$?da.top:ee>=$?da.bottom:da.top}return da.bottom},oe=h=>C()===da.bottom?O(h):ne(h),ce=()=>{const h=ze(n);if(h)return i.autoPosition?oe(h):O(h)},ye=function(h){if(h){const F=h.scrollHeight>h.clientHeight,J=window.getComputedStyle(h).overflowY.indexOf("hidden")!==-1;return F&&!J}return!0},y=function(h){return!h||h===document.body||h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?window:ye(h)?h:y(h.assignedSlot&&d.value.shadowDom?h.assignedSlot.parentNode:h.parentNode)},D=h=>{if(h)switch(i.position){case za.left:return{left:0,transform:"translateX(0)"};case za.right:return{left:`${h.width}px`,transform:"translateX(-100%)"};default:return{left:`${h.width/2}px`,transform:"translateX(-50%)"}}return{}};return{openOnTop:m,menuStyle:v,xCorrect:p,setMenuPosition:Q,getScrollableParent:y,shadowRender:(h,F,J)=>{var V,ee,$;const de=document.createElement("div"),c=(V=ze(n))==null?void 0:V.getBoundingClientRect();de.setAttribute("id","dp--temp-container");const _=(ee=t.value)!=null&&ee.clientWidth?t.value:document.body;_.append(de);const B=D(c),E=d.value.shadowDom?Object.keys(f).filter(w=>["right-sidebar","left-sidebar","top-extra","action-extra"].includes(w)):Object.keys(f),r=sn(F,{...J,shadow:!0,style:{opacity:0,position:"absolute",...B}},Object.fromEntries(E.map(w=>[w,f[w]])));h!=null&&(r.appContext=h.appContext),sl(r,de),T.value=($=r.el)==null?void 0:$.getBoundingClientRect(),sl(null,de),_.removeChild(de)}}},Ta=[{name:"clock-icon",use:["time","calendar","shared"]},{name:"arrow-left",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-right",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-up",use:["time","calendar","month-year","shared"]},{name:"arrow-down",use:["time","calendar","month-year","shared"]},{name:"calendar-icon",use:["month-year","time","calendar","shared","year-mode"]},{name:"day",use:["calendar","shared"]},{name:"month-overlay-value",use:["calendar","month-year","shared"]},{name:"year-overlay-value",use:["calendar","month-year","shared","year-mode"]},{name:"year-overlay",use:["month-year","shared"]},{name:"month-overlay",use:["month-year","shared"]},{name:"month-overlay-header",use:["month-year","shared"]},{name:"year-overlay-header",use:["month-year","shared"]},{name:"hours-overlay-value",use:["calendar","time","shared"]},{name:"hours-overlay-header",use:["calendar","time","shared"]},{name:"minutes-overlay-value",use:["calendar","time","shared"]},{name:"minutes-overlay-header",use:["calendar","time","shared"]},{name:"seconds-overlay-value",use:["calendar","time","shared"]},{name:"seconds-overlay-header",use:["calendar","time","shared"]},{name:"hours",use:["calendar","time","shared"]},{name:"minutes",use:["calendar","time","shared"]},{name:"month",use:["calendar","month-year","shared"]},{name:"year",use:["calendar","month-year","shared","year-mode"]},{name:"action-buttons",use:["action"]},{name:"action-preview",use:["action"]},{name:"calendar-header",use:["calendar","shared"]},{name:"marker-tooltip",use:["calendar","shared"]},{name:"action-extra",use:["menu"]},{name:"time-picker-overlay",use:["calendar","time","shared"]},{name:"am-pm-button",use:["calendar","time","shared"]},{name:"left-sidebar",use:["menu"]},{name:"right-sidebar",use:["menu"]},{name:"month-year",use:["month-year","shared"]},{name:"time-picker",use:["menu","shared"]},{name:"action-row",use:["action"]},{name:"marker",use:["calendar","shared"]},{name:"quarter",use:["shared"]},{name:"top-extra",use:["shared","month-year"]},{name:"tp-inline-arrow-up",use:["shared","time"]},{name:"tp-inline-arrow-down",use:["shared","time"]},{name:"menu-header",use:["menu"]}],Du=[{name:"trigger"},{name:"input-icon"},{name:"clear-icon"},{name:"dp-input"}],Mu={all:()=>Ta,monthYear:()=>Ta.filter(e=>e.use.includes("month-year")),input:()=>Du,timePicker:()=>Ta.filter(e=>e.use.includes("time")),action:()=>Ta.filter(e=>e.use.includes("action")),calendar:()=>Ta.filter(e=>e.use.includes("calendar")),menu:()=>Ta.filter(e=>e.use.includes("menu")),shared:()=>Ta.filter(e=>e.use.includes("shared")),yearMode:()=>Ta.filter(e=>e.use.includes("year-mode"))},ua=(e,a,n)=>{const t=[];return Mu[a]().forEach(l=>{e[l.name]&&t.push(l.name)}),n!=null&&n.length&&n.forEach(l=>{l.slot&&t.push(l.slot)}),t},ut=e=>{const a=W(()=>t=>e.value?t?e.value.open:e.value.close:""),n=W(()=>t=>e.value?t?e.value.menuAppearTop:e.value.menuAppearBottom:"");return{transitionName:a,showTransition:!!e.value,menuTransition:n}},ot=(e,a,n)=>{const{defaultedRange:t,defaultedTz:l}=Be(e),s=j(ra(j(),l.value.timezone)),i=Z([{month:$e(s),year:he(s)}]),f=m=>{const k={hours:$a(s),minutes:Ra(s),seconds:0};return t.value.enabled?[k[m],k[m]]:k[m]},d=nt({hours:f("hours"),minutes:f("minutes"),seconds:f("seconds")});na(t,(m,k)=>{m.enabled!==k.enabled&&(d.hours=f("hours"),d.minutes=f("minutes"),d.seconds=f("seconds"))},{deep:!0});const T=W({get:()=>e.internalModelValue,set:m=>{!e.readonly&&!e.disabled&&a("update:internal-model-value",m)}}),p=W(()=>m=>i.value[m]?i.value[m].month:0),v=W(()=>m=>i.value[m]?i.value[m].year:0);return na(T,(m,k)=>{n&&JSON.stringify(m??{})!==JSON.stringify(k??{})&&n()},{deep:!0}),{calendars:i,time:d,modelValue:T,month:p,year:v,today:s}},$u=(e,a)=>{const{defaultedMultiCalendars:n,defaultedMultiDates:t,defaultedUI:l,defaultedHighlight:s,defaultedTz:i,propDates:f,defaultedRange:d}=Be(a),{isDisabled:T}=Ca(a),p=Z(null),v=Z(ra(new Date,i.value.timezone)),m=r=>{!r.current&&a.hideOffsetDates||(p.value=r.value)},k=()=>{p.value=null},R=r=>Array.isArray(e.value)&&d.value.enabled&&e.value[0]&&p.value?r?Le(p.value,e.value[0]):Ie(p.value,e.value[0]):!0,Y=(r,w)=>{const ue=()=>e.value?w?e.value[0]||null:e.value[1]:null,xe=e.value&&Array.isArray(e.value)?ue():null;return Ae(j(r.value),xe)},z=r=>{const w=Array.isArray(e.value)?e.value[0]:null;return r?!Ie(p.value??null,w):!0},P=(r,w=!0)=>(d.value.enabled||a.weekPicker)&&Array.isArray(e.value)&&e.value.length===2?a.hideOffsetDates&&!r.current?!1:Ae(j(r.value),e.value[w?0:1]):d.value.enabled?Y(r,w)&&z(w)||Ae(r.value,Array.isArray(e.value)?e.value[0]:null)&&R(w):!1,N=(r,w)=>{if(Array.isArray(e.value)&&e.value[0]&&e.value.length===1){const ue=Ae(r.value,p.value);return w?Le(e.value[0],r.value)&&ue:Ie(e.value[0],r.value)&&ue}return!1},x=r=>!e.value||a.hideOffsetDates&&!r.current?!1:d.value.enabled?a.modelAuto&&Array.isArray(e.value)?Ae(r.value,e.value[0]?e.value[0]:v.value):!1:t.value.enabled&&Array.isArray(e.value)?e.value.some(w=>Ae(w,r.value)):Ae(r.value,e.value?e.value:v.value),U=r=>{if(d.value.autoRange||a.weekPicker){if(p.value){if(a.hideOffsetDates&&!r.current)return!1;const w=ga(p.value,+d.value.autoRange),ue=Da(j(p.value),a.weekStart);return a.weekPicker?Ae(ue[1],j(r.value)):Ae(w,j(r.value))}return!1}return!1},Q=r=>{if(d.value.autoRange||a.weekPicker){if(p.value){const w=ga(p.value,+d.value.autoRange);if(a.hideOffsetDates&&!r.current)return!1;const ue=Da(j(p.value),a.weekStart);return a.weekPicker?Le(r.value,ue[0])&&Ie(r.value,ue[1]):Le(r.value,p.value)&&Ie(r.value,w)}return!1}return!1},S=r=>{if(d.value.autoRange||a.weekPicker){if(p.value){if(a.hideOffsetDates&&!r.current)return!1;const w=Da(j(p.value),a.weekStart);return a.weekPicker?Ae(w[0],r.value):Ae(p.value,r.value)}return!1}return!1},O=r=>lt(e.value,p.value,r.value),ne=()=>a.modelAuto&&Array.isArray(a.internalModelValue)?!!a.internalModelValue[0]:!1,L=()=>a.modelAuto?Il(a.internalModelValue):!0,C=r=>{if(a.weekPicker)return!1;const w=d.value.enabled?!P(r)&&!P(r,!1):!0;return!T(r.value)&&!x(r)&&!(!r.current&&a.hideOffsetDates)&&w},oe=r=>d.value.enabled?a.modelAuto?ne()&&x(r):!1:x(r),ce=r=>s.value?Hn(r.value,f.value.highlight):!1,ye=r=>{const w=T(r.value);return w&&(typeof s.value=="function"?!s.value(r.value,w):!s.value.options.highlightDisabled)},y=r=>{var w;return typeof s.value=="function"?s.value(r.value):(w=s.value.weekdays)==null?void 0:w.includes(r.value.getDay())},D=r=>(d.value.enabled||a.weekPicker)&&(!(n.value.count>0)||r.current)&&L()&&!(!r.current&&a.hideOffsetDates)&&!x(r)?O(r):!1,h=r=>{if(Array.isArray(e.value)&&e.value.length===1){const{before:w,after:ue}=wl(+d.value.maxRange,e.value[0]);return ja(r.value,w)||Na(r.value,ue)}return!1},F=r=>{if(Array.isArray(e.value)&&e.value.length===1){const{before:w,after:ue}=wl(+d.value.minRange,e.value[0]);return lt([w,ue],e.value[0],r.value)}return!1},J=r=>d.value.enabled&&(d.value.maxRange||d.value.minRange)?d.value.maxRange&&d.value.minRange?h(r)||F(r):d.value.maxRange?h(r):F(r):!1,V=r=>{const{isRangeStart:w,isRangeEnd:ue}=c(r),xe=d.value.enabled?w||ue:!1;return{dp__cell_offset:!r.current,dp__pointer:!a.disabled&&!(!r.current&&a.hideOffsetDates)&&!T(r.value)&&!J(r),dp__cell_disabled:T(r.value)||J(r),dp__cell_highlight:!ye(r)&&(ce(r)||y(r))&&!oe(r)&&!xe&&!S(r)&&!(D(r)&&a.weekPicker)&&!ue,dp__cell_highlight_active:!ye(r)&&(ce(r)||y(r))&&oe(r),dp__today:!a.noToday&&Ae(r.value,v.value)&&r.current,"dp--past":Ie(r.value,v.value),"dp--future":Le(r.value,v.value)}},ee=r=>({dp__active_date:oe(r),dp__date_hover:C(r)}),$=r=>{if(e.value&&!Array.isArray(e.value)){const w=Da(e.value,a.weekStart);return{...B(r),dp__range_start:Ae(w[0],r.value),dp__range_end:Ae(w[1],r.value),dp__range_between_week:Le(r.value,w[0])&&Ie(r.value,w[1])}}return{...B(r)}},de=r=>{if(e.value&&Array.isArray(e.value)){const w=Da(e.value[0],a.weekStart),ue=e.value[1]?Da(e.value[1],a.weekStart):[];return{...B(r),dp__range_start:Ae(w[0],r.value)||Ae(ue[0],r.value),dp__range_end:Ae(w[1],r.value)||Ae(ue[1],r.value),dp__range_between_week:Le(r.value,w[0])&&Ie(r.value,w[1])||Le(r.value,ue[0])&&Ie(r.value,ue[1]),dp__range_between:Le(r.value,w[1])&&Ie(r.value,ue[0])}}return{...B(r)}},c=r=>{const w=n.value.count>0?r.current&&P(r)&&L():P(r)&&L(),ue=n.value.count>0?r.current&&P(r,!1)&&L():P(r,!1)&&L();return{isRangeStart:w,isRangeEnd:ue}},_=r=>{const{isRangeStart:w,isRangeEnd:ue}=c(r);return{dp__range_start:w,dp__range_end:ue,dp__range_between:D(r),dp__date_hover:Ae(r.value,p.value)&&!w&&!ue&&!a.weekPicker,dp__date_hover_start:N(r,!0),dp__date_hover_end:N(r,!1)}},B=r=>({..._(r),dp__cell_auto_range:Q(r),dp__cell_auto_range_start:S(r),dp__cell_auto_range_end:U(r)}),E=r=>d.value.enabled?d.value.autoRange?B(r):a.modelAuto?{...ee(r),..._(r)}:a.weekPicker?de(r):_(r):a.weekPicker?$(r):ee(r);return{setHoverDate:m,clearHoverDate:k,getDayClassData:r=>a.hideOffsetDates&&!r.current?{}:{...V(r),...E(r),[a.dayClass?a.dayClass(r.value,a.internalModelValue):""]:!0,...l.value.calendarCell??{}}}},Ca=e=>{const{defaultedFilters:a,defaultedRange:n,propDates:t,defaultedMultiDates:l}=Be(e),s=y=>t.value.disabledDates?typeof t.value.disabledDates=="function"?t.value.disabledDates(j(y)):!!yt(y,t.value.disabledDates):!1,i=y=>t.value.maxDate?e.yearPicker?he(y)>he(t.value.maxDate):Le(y,t.value.maxDate):!1,f=y=>t.value.minDate?e.yearPicker?he(y)<he(t.value.minDate):Ie(y,t.value.minDate):!1,d=y=>{const D=i(y),h=f(y),F=s(y),J=a.value.months.map(c=>+c).includes($e(y)),V=e.disabledWeekDays.length?e.disabledWeekDays.some(c=>+c===Pn(y)):!1,ee=k(y),$=he(y),de=$<+e.yearRange[0]||$>+e.yearRange[1];return!(D||h||F||J||de||V||ee)},T=(y,D)=>Ie(...Pa(t.value.minDate,y,D))||Ae(...Pa(t.value.minDate,y,D)),p=(y,D)=>Le(...Pa(t.value.maxDate,y,D))||Ae(...Pa(t.value.maxDate,y,D)),v=(y,D,h)=>{let F=!1;return t.value.maxDate&&h&&p(y,D)&&(F=!0),t.value.minDate&&!h&&T(y,D)&&(F=!0),F},m=(y,D,h,F)=>{let J=!1;return F&&(t.value.minDate||t.value.maxDate)?t.value.minDate&&t.value.maxDate?J=v(y,D,h):(t.value.minDate&&T(y,D)||t.value.maxDate&&p(y,D))&&(J=!0):J=!0,J},k=y=>Array.isArray(t.value.allowedDates)&&!t.value.allowedDates.length?!0:t.value.allowedDates?!yt(y,t.value.allowedDates,Ll(e.monthPicker,e.yearPicker)):!1,R=y=>!d(y),Y=y=>n.value.noDisabledRange?!Tl({start:y[0],end:y[1]}).some(D=>R(D)):!0,z=y=>{if(y){const D=he(y);return D>=+e.yearRange[0]&&D<=e.yearRange[1]}return!0},P=(y,D)=>!!(Array.isArray(y)&&y[D]&&(n.value.maxRange||n.value.minRange)&&z(y[D])),N=(y,D,h=0)=>{if(P(D,h)&&z(y)){const F=cn(y,D[h]),J=Hl(D[h],y),V=J.length===1?0:J.filter($=>R($)).length,ee=Math.abs(F)-(n.value.minMaxRawRange?0:V);if(n.value.minRange&&n.value.maxRange)return ee>=+n.value.minRange&&ee<=+n.value.maxRange;if(n.value.minRange)return ee>=+n.value.minRange;if(n.value.maxRange)return ee<=+n.value.maxRange}return!0},x=()=>!e.enableTimePicker||e.monthPicker||e.yearPicker||e.ignoreTimeValidation,U=y=>Array.isArray(y)?[y[0]?Bt(y[0]):null,y[1]?Bt(y[1]):null]:Bt(y),Q=(y,D,h)=>y.find(F=>+F.hours===$a(D)&&F.minutes==="*"?!0:+F.minutes===Ra(D)&&+F.hours===$a(D))&&h,S=(y,D,h)=>{const[F,J]=y,[V,ee]=D;return!Q(F,V,h)&&!Q(J,ee,h)&&h},O=(y,D)=>{const h=Array.isArray(D)?D:[D];return Array.isArray(e.disabledTimes)?Array.isArray(e.disabledTimes[0])?S(e.disabledTimes,h,y):!h.some(F=>Q(e.disabledTimes,F,y)):y},ne=(y,D)=>{const h=Array.isArray(D)?[Ia(D[0]),D[1]?Ia(D[1]):void 0]:Ia(D),F=!e.disabledTimes(h);return y&&F},L=(y,D)=>e.disabledTimes?Array.isArray(e.disabledTimes)?O(D,y):ne(D,y):D,C=y=>{let D=!0;if(!y||x())return!0;const h=!t.value.minDate&&!t.value.maxDate?U(y):y;return(e.maxTime||t.value.maxDate)&&(D=bl(e.maxTime,t.value.maxDate,"max",He(h),D)),(e.minTime||t.value.minDate)&&(D=bl(e.minTime,t.value.minDate,"min",He(h),D)),L(y,D)},oe=y=>{if(!e.monthPicker)return!0;let D=!0;const h=j(ca(y));if(t.value.minDate&&t.value.maxDate){const F=j(ca(t.value.minDate)),J=j(ca(t.value.maxDate));return Le(h,F)&&Ie(h,J)||Ae(h,F)||Ae(h,J)}if(t.value.minDate){const F=j(ca(t.value.minDate));D=Le(h,F)||Ae(h,F)}if(t.value.maxDate){const F=j(ca(t.value.maxDate));D=Ie(h,F)||Ae(h,F)}return D},ce=W(()=>y=>!e.enableTimePicker||e.ignoreTimeValidation?!0:C(y)),ye=W(()=>y=>e.monthPicker?Array.isArray(y)&&(n.value.enabled||l.value.enabled)?!y.filter(D=>!oe(D)).length:oe(y):!0);return{isDisabled:R,validateDate:d,validateMonthYearInRange:m,isDateRangeAllowed:Y,checkMinMaxRange:N,isValidTime:C,isTimeValid:ce,isMonthValid:ye}},Dt=()=>{const e=W(()=>(t,l)=>t==null?void 0:t.includes(l)),a=W(()=>(t,l)=>t.count?t.solo?!0:l===0:!0),n=W(()=>(t,l)=>t.count?t.solo?!0:l===t.count-1:!0);return{hideNavigationButtons:e,showLeftIcon:a,showRightIcon:n}},Au=(e,a,n)=>{const t=Z(0),l=nt({[Ba.timePicker]:!e.enableTimePicker||e.timePicker||e.monthPicker,[Ba.calendar]:!1,[Ba.header]:!1}),s=W(()=>e.monthPicker||e.timePicker),i=v=>{var m;if((m=e.flow)!=null&&m.length){if(!v&&s.value)return p();l[v]=!0,Object.keys(l).filter(k=>!l[k]).length||p()}},f=()=>{var v,m;(v=e.flow)!=null&&v.length&&t.value!==-1&&(t.value+=1,a("flow-step",t.value),p()),((m=e.flow)==null?void 0:m.length)===t.value&&oa().then(()=>d())},d=()=>{t.value=-1},T=(v,m,...k)=>{var R,Y;e.flow[t.value]===v&&n.value&&((Y=(R=n.value)[m])==null||Y.call(R,...k))},p=(v=0)=>{v&&(t.value+=v),T(Xe.month,"toggleMonthPicker",!0),T(Xe.year,"toggleYearPicker",!0),T(Xe.calendar,"toggleTimePicker",!1,!0),T(Xe.time,"toggleTimePicker",!0,!0);const m=e.flow[t.value];(m===Xe.hours||m===Xe.minutes||m===Xe.seconds)&&T(m,"toggleTimePicker",!0,!0,m)};return{childMount:i,updateFlowStep:f,resetFlow:d,handleFlow:p,flowStep:t}},Tu={key:1,class:"dp__input_wrap"},Pu=["id","name","inputmode","placeholder","disabled","readonly","required","value","autocomplete","aria-label","aria-disabled","aria-invalid"],Su={key:2,class:"dp--clear-btn"},xu=["aria-label"],Ru=qe({compatConfig:{MODE:3},__name:"DatepickerInput",props:{isMenuOpen:{type:Boolean,default:!1},inputValue:{type:String,default:""},...wt},emits:["clear","open","update:input-value","set-input-date","close","select-date","set-empty-date","toggle","focus-prev","focus","blur","real-blur","text-input"],setup(e,{expose:a,emit:n}){const t=n,l=e,{defaultedTextInput:s,defaultedAriaLabels:i,defaultedInline:f,defaultedConfig:d,defaultedRange:T,defaultedMultiDates:p,defaultedUI:v,getDefaultPattern:m,getDefaultStartTime:k}=Be(l),{checkMinMaxRange:R}=Ca(l),Y=Z(),z=Z(null),P=Z(!1),N=Z(!1),x=W(()=>({dp__pointer:!l.disabled&&!l.readonly&&!s.value.enabled,dp__disabled:l.disabled,dp__input_readonly:!s.value.enabled,dp__input:!0,dp__input_icon_pad:!l.hideInputIcon,dp__input_valid:typeof l.state=="boolean"?l.state:!1,dp__input_invalid:typeof l.state=="boolean"?!l.state:!1,dp__input_focus:P.value||l.isMenuOpen,dp__input_reg:!s.value.enabled,...v.value.input??{}})),U=()=>{t("set-input-date",null),l.clearable&&l.autoApply&&(t("set-empty-date"),Y.value=null)},Q=$=>{const de=k();return Wn($,s.value.format??m(),de??jl({},l.enableSeconds),l.inputValue,N.value,l.formatLocale)},S=$=>{const{rangeSeparator:de}=s.value,[c,_]=$.split(`${de}`);if(c){const B=Q(c.trim()),E=_?Q(_.trim()):void 0;if(Na(B,E))return;const r=B&&E?[B,E]:[B];R(E,r,0)&&(Y.value=B?r:null)}},O=()=>{N.value=!0},ne=$=>{if(T.value.enabled)S($);else if(p.value.enabled){const de=$.split(";");Y.value=de.map(c=>Q(c.trim())).filter(c=>c)}else Y.value=Q($)},L=$=>{var de;const c=typeof $=="string"?$:(de=$.target)==null?void 0:de.value;c!==""?(s.value.openMenu&&!l.isMenuOpen&&t("open"),ne(c),t("set-input-date",Y.value)):U(),N.value=!1,t("update:input-value",c),t("text-input",$,Y.value)},C=$=>{s.value.enabled?(ne($.target.value),s.value.enterSubmit&&Wt(Y.value)&&l.inputValue!==""?(t("set-input-date",Y.value,!0),Y.value=null):s.value.enterSubmit&&l.inputValue===""&&(Y.value=null,t("clear"))):ye($)},oe=($,de)=>{s.value.enabled&&s.value.tabSubmit&&!de&&ne($.target.value),s.value.tabSubmit&&Wt(Y.value)&&l.inputValue!==""?(t("set-input-date",Y.value,!0,!0),Y.value=null):s.value.tabSubmit&&l.inputValue===""&&(Y.value=null,t("clear",!0))},ce=()=>{P.value=!0,t("focus"),oa().then(()=>{var $;s.value.enabled&&s.value.selectOnFocus&&(($=z.value)==null||$.select())})},ye=$=>{if(Sa($,d.value,!0),s.value.enabled&&s.value.openMenu&&!f.value.input){if(s.value.openMenu==="open"&&!l.isMenuOpen)return t("open");if(s.value.openMenu==="toggle")return t("toggle")}else s.value.enabled||t("toggle")},y=()=>{t("real-blur"),P.value=!1,(!l.isMenuOpen||f.value.enabled&&f.value.input)&&t("blur"),l.autoApply&&s.value.enabled&&Y.value&&!l.isMenuOpen&&(t("set-input-date",Y.value),t("select-date"),Y.value=null)},D=$=>{Sa($,d.value,!0),t("clear")},h=()=>{t("close")},F=$=>{if($.key==="Tab"&&oe($),$.key==="Enter"&&C($),$.key==="Escape"&&s.value.escClose&&h(),!s.value.enabled){if($.code==="Tab")return;$.preventDefault()}},J=()=>{var $;($=z.value)==null||$.focus({preventScroll:!0})},V=$=>{Y.value=$},ee=$=>{$.key===Ye.tab&&oe($,!0)};return a({focusInput:J,setParsedDate:V}),($,de)=>{var c,_,B;return M(),H("div",{onClick:ye},[$.$slots.trigger&&!$.$slots["dp-input"]&&!o(f).enabled?ie($.$slots,"trigger",{key:0}):q("",!0),!$.$slots.trigger&&(!o(f).enabled||o(f).input)?(M(),H("div",Tu,[$.$slots["dp-input"]&&!$.$slots.trigger&&(!o(f).enabled||o(f).enabled&&o(f).input)?ie($.$slots,"dp-input",{key:0,value:e.inputValue,isMenuOpen:e.isMenuOpen,onInput:L,onEnter:C,onTab:oe,onClear:D,onBlur:y,onKeypress:F,onPaste:O,onFocus:ce,openMenu:()=>$.$emit("open"),closeMenu:()=>$.$emit("close"),toggleMenu:()=>$.$emit("toggle")}):q("",!0),$.$slots["dp-input"]?q("",!0):(M(),H("input",{key:1,id:$.uid?`dp-input-${$.uid}`:void 0,ref_key:"inputRef",ref:z,"data-test-id":"dp-input",name:$.name,class:De(x.value),inputmode:o(s).enabled?"text":"none",placeholder:$.placeholder,disabled:$.disabled,readonly:$.readonly,required:$.required,value:e.inputValue,autocomplete:$.autocomplete,"aria-label":(c=o(i))==null?void 0:c.input,"aria-disabled":$.disabled||void 0,"aria-invalid":$.state===!1?!0:void 0,onInput:L,onBlur:y,onFocus:ce,onKeypress:F,onKeydown:de[0]||(de[0]=E=>F(E)),onPaste:O},null,42,Pu)),me("div",{onClick:de[3]||(de[3]=E=>t("toggle"))},[$.$slots["input-icon"]&&!$.hideInputIcon?(M(),H("span",{key:0,class:"dp__input_icon",onClick:de[1]||(de[1]=E=>t("toggle"))},[ie($.$slots,"input-icon")])):q("",!0),!$.$slots["input-icon"]&&!$.hideInputIcon&&!$.$slots["dp-input"]?(M(),Te(o(Ga),{key:1,"aria-label":(_=o(i))==null?void 0:_.calendarIcon,class:"dp__input_icon dp__input_icons",onClick:de[2]||(de[2]=E=>t("toggle"))},null,8,["aria-label"])):q("",!0)]),$.$slots["clear-icon"]&&($.alwaysClearable||e.inputValue&&$.clearable&&!$.disabled&&!$.readonly)?(M(),H("span",Su,[ie($.$slots,"clear-icon",{clear:D})])):q("",!0),!$.$slots["clear-icon"]&&($.alwaysClearable||$.clearable&&e.inputValue&&!$.disabled&&!$.readonly)?(M(),H("button",{key:3,"aria-label":(B=o(i))==null?void 0:B.clearInput,class:"dp--clear-btn",type:"button",onKeydown:de[4]||(de[4]=E=>o(aa)(E,()=>D(E),!0,ee)),onClick:de[5]||(de[5]=at(E=>D(E),["prevent"]))},[ta(o(Vl),{class:"dp__input_icons","data-test-id":"clear-icon"})],40,xu)):q("",!0)])):q("",!0)])}}}),Ou=typeof window<"u"?window:void 0,Ut=()=>{},Cu=e=>pn()?(mn(e),!0):!1,Yu=(e,a,n,t)=>{if(!e)return Ut;let l=Ut;const s=na(()=>o(e),f=>{l(),f&&(f.removeEventListener(a,n),f.addEventListener(a,n,t),l=()=>{f.removeEventListener(a,n,t),l=Ut})},{immediate:!0,flush:"post"}),i=()=>{s(),l()};return Cu(i),i},Vu=(e,a,n,t={})=>{const{window:l=Ou,event:s="pointerdown"}=t;return l?Yu(l,s,i=>{const f=ze(e),d=ze(a);!f||!d||f===i.target||i.composedPath().includes(f)||i.composedPath().includes(d)||n(i)},{passive:!0}):void 0},Bu=["data-dp-mobile"],Iu=qe({compatConfig:{MODE:3},__name:"VueDatePicker",props:{...wt},emits:["update:model-value","update:model-timezone-value","text-submit","closed","cleared","open","focus","blur","internal-model-change","recalculate-position","flow-step","update-month-year","invalid-select","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","date-update","invalid-date","overlay-toggle","text-input"],setup(e,{expose:a,emit:n}){const t=n,l=e,s=Fa(),i=Z(!1),f=et(l,"modelValue"),d=et(l,"timezone"),T=Z(null),p=Z(null),v=Z(null),m=Z(!1),k=Z(null),R=Z(!1),Y=Z(!1),z=Z(!1),P=Z(!1),{setMenuFocused:N,setShiftKey:x}=Ql(),{clearArrowNav:U}=Oa(),{validateDate:Q,isValidTime:S}=Ca(l),{defaultedTransitions:O,defaultedTextInput:ne,defaultedInline:L,defaultedConfig:C,defaultedRange:oe,defaultedMultiDates:ce}=Be(l),{menuTransition:ye,showTransition:y}=ut(O),{isMobile:D}=tn(C),h=on();Qe(()=>{r(l.modelValue),oa().then(()=>{if(!L.value.enabled){const I=c(k.value);I==null||I.addEventListener("scroll",te),window==null||window.addEventListener("resize",le)}}),L.value.enabled&&(i.value=!0),window==null||window.addEventListener("keyup",Me),window==null||window.addEventListener("keydown",be)}),Qa(()=>{if(!L.value.enabled){const I=c(k.value);I==null||I.removeEventListener("scroll",te),window==null||window.removeEventListener("resize",le)}window==null||window.removeEventListener("keyup",Me),window==null||window.removeEventListener("keydown",be)});const F=ua(s,"all",l.presetDates),J=ua(s,"input");na([f,d],()=>{r(f.value)},{deep:!0});const{openOnTop:V,menuStyle:ee,xCorrect:$,setMenuPosition:de,getScrollableParent:c,shadowRender:_}=ku({menuRef:T,menuRefInner:p,inputRef:v,pickerWrapperRef:k,inline:L,emit:t,props:l,slots:s}),{inputValue:B,internalModelValue:E,parseExternalModelValue:r,emitModelValue:w,formatInputValue:ue,checkBeforeEmit:xe}=hr(t,l,m),A=W(()=>({dp__main:!0,dp__theme_dark:l.dark,dp__theme_light:!l.dark,dp__flex_display:L.value.enabled,"dp--flex-display-collapsed":z.value,dp__flex_display_with_input:L.value.input})),ge=W(()=>l.dark?"dp__theme_dark":"dp__theme_light"),K=W(()=>l.teleport?{to:typeof l.teleport=="boolean"?"body":l.teleport,disabled:!l.teleport||L.value.enabled}:{}),ae=W(()=>({class:"dp__outer_menu_wrap"})),u=W(()=>L.value.enabled&&(l.timePicker||l.monthPicker||l.yearPicker||l.quarterPicker)),X=()=>{var I,se;return((se=(I=v.value)==null?void 0:I.$el)==null?void 0:se.getBoundingClientRect())??{width:0,left:0,right:0}},te=()=>{i.value&&(C.value.closeOnScroll?Ee():de())},le=()=>{var I;i.value&&de();const se=((I=p.value)==null?void 0:I.$el.getBoundingClientRect().width)??0;z.value=document.body.offsetWidth<=se},Me=I=>{I.key==="Tab"&&!L.value.enabled&&!l.teleport&&C.value.tabOutClosesMenu&&(k.value.contains(document.activeElement)||Ee()),Y.value=I.shiftKey},be=I=>{Y.value=I.shiftKey},Re=()=>{!l.disabled&&!l.readonly&&(_(h,Ml,l),de(!1),i.value=!0,i.value&&t("open"),i.value||ve(),r(l.modelValue))},Ne=()=>{var I,se;B.value="",ve(),(I=p.value)==null||I.onValueCleared(),(se=v.value)==null||se.setParsedDate(null),t("update:model-value",null),t("update:model-timezone-value",null),t("cleared"),C.value.closeOnClearValue&&Ee()},g=()=>{const I=E.value;return!I||!Array.isArray(I)&&Q(I)?!0:Array.isArray(I)?ce.value.enabled||I.length===2&&Q(I[0])&&Q(I[1])?!0:oe.value.partialRange&&!l.timePicker?Q(I[0]):!1:!1},re=()=>{xe()&&g()?(w(),Ee()):t("invalid-select",E.value)},Pe=I=>{Ue(),w(),C.value.closeOnAutoApply&&!I&&Ee()},Ue=()=>{v.value&&ne.value.enabled&&v.value.setParsedDate(E.value)},fa=(I=!1)=>{l.autoApply&&S(E.value)&&g()&&(oe.value.enabled&&Array.isArray(E.value)?(oe.value.partialRange||E.value.length===2)&&Pe(I):Pe(I))},ve=()=>{ne.value.enabled||(E.value=null)},Ee=(I=!1)=>{var se,pe;I&&E.value&&C.value.setDateOnMenuClose&&re(),L.value.enabled||(i.value&&(i.value=!1,$.value=!1,N(!1),x(!1),U(),t("closed"),B.value&&r(f.value)),ve(),t("blur"),(pe=(se=p.value)==null?void 0:se.$el)==null||pe.remove())},ya=(I,se,pe=!1)=>{if(!I){E.value=null;return}const ka=Array.isArray(I)?!I.some(Ya=>!Q(Ya)):Q(I),ha=S(I);ka&&ha?(P.value=!0,E.value=I,se?(R.value=pe,re(),t("text-submit")):l.autoApply&&fa(),oa().then(()=>{P.value=!1})):t("invalid-date",I)},Mt=()=>{l.autoApply&&S(E.value)&&w(),Ue()},st=()=>i.value?Ee():Re(),$t=I=>{E.value=I},At=()=>{ne.value.enabled&&(m.value=!0,ue()),t("focus")},Tt=()=>{if(ne.value.enabled&&(m.value=!1,r(l.modelValue),R.value)){const I=En(k.value,Y.value);I==null||I.focus()}t("blur")},Pt=I=>{p.value&&p.value.updateMonthYear(0,{month:yl(I.month),year:yl(I.year)})},St=I=>{r(I??l.modelValue)},b=(I,se)=>{var pe;(pe=p.value)==null||pe.switchView(I,se)},G=(I,se)=>C.value.onClickOutside?C.value.onClickOutside(I,se):Ee(!0),fe=(I=0)=>{var se;(se=p.value)==null||se.handleFlow(I)},we=()=>T;return Vu(T,v,I=>G(g,I)),a({closeMenu:Ee,selectDate:re,clearValue:Ne,openMenu:Re,onScroll:te,formatInputValue:ue,updateInternalModelValue:$t,setMonthYear:Pt,parseModel:St,switchView:b,toggleMenu:st,handleFlow:fe,getDpWrapMenuRef:we}),(I,se)=>(M(),H("div",{ref_key:"pickerWrapperRef",ref:k,class:De(A.value),"data-datepicker-instance":"","data-dp-mobile":o(D)},[ta(Ru,We({ref_key:"inputRef",ref:v,"input-value":o(B),"onUpdate:inputValue":se[0]||(se[0]=pe=>mt(B)?B.value=pe:null),"is-menu-open":i.value},I.$props,{onClear:Ne,onOpen:Re,onSetInputDate:ya,onSetEmptyDate:o(w),onSelectDate:re,onToggle:st,onClose:Ee,onFocus:At,onBlur:Tt,onRealBlur:se[1]||(se[1]=pe=>m.value=!1),onTextInput:se[2]||(se[2]=pe=>I.$emit("text-input",pe))}),Je({_:2},[Ve(o(J),(pe,ka)=>({name:pe,fn:ke(ha=>[ie(I.$slots,pe,je(la(ha)))])}))]),1040,["input-value","is-menu-open","onSetEmptyDate"]),(M(),Te(bt(I.teleport?un:"div"),je(la(K.value)),{default:ke(()=>[ta(Za,{name:o(ye)(o(V)),css:o(y)&&!o(L).enabled},{default:ke(()=>[i.value?(M(),H("div",We({key:0,ref_key:"dpWrapMenuRef",ref:T},ae.value,{class:{"dp--menu-wrapper":!o(L).enabled},style:o(L).enabled?void 0:o(ee)}),[ta(Ml,We({ref_key:"dpMenuRef",ref:p},I.$props,{"internal-model-value":o(E),"onUpdate:internalModelValue":se[3]||(se[3]=pe=>mt(E)?E.value=pe:null),class:{[ge.value]:!0,"dp--menu-wrapper":I.teleport},"open-on-top":o(V),"no-overlay-focus":u.value,collapse:z.value,"get-input-rect":X,"is-text-input-date":P.value,onClosePicker:Ee,onSelectDate:re,onAutoApply:fa,onTimeUpdate:Mt,onFlowStep:se[4]||(se[4]=pe=>I.$emit("flow-step",pe)),onUpdateMonthYear:se[5]||(se[5]=pe=>I.$emit("update-month-year",pe)),onInvalidSelect:se[6]||(se[6]=pe=>I.$emit("invalid-select",o(E))),onAutoApplyInvalid:se[7]||(se[7]=pe=>I.$emit("invalid-select",pe)),onInvalidFixedRange:se[8]||(se[8]=pe=>I.$emit("invalid-fixed-range",pe)),onRecalculatePosition:o(de),onTooltipOpen:se[9]||(se[9]=pe=>I.$emit("tooltip-open",pe)),onTooltipClose:se[10]||(se[10]=pe=>I.$emit("tooltip-close",pe)),onTimePickerOpen:se[11]||(se[11]=pe=>I.$emit("time-picker-open",pe)),onTimePickerClose:se[12]||(se[12]=pe=>I.$emit("time-picker-close",pe)),onAmPmChange:se[13]||(se[13]=pe=>I.$emit("am-pm-change",pe)),onRangeStart:se[14]||(se[14]=pe=>I.$emit("range-start",pe)),onRangeEnd:se[15]||(se[15]=pe=>I.$emit("range-end",pe)),onDateUpdate:se[16]||(se[16]=pe=>I.$emit("date-update",pe)),onInvalidDate:se[17]||(se[17]=pe=>I.$emit("invalid-date",pe)),onOverlayToggle:se[18]||(se[18]=pe=>I.$emit("overlay-toggle",pe)),onMenuBlur:se[19]||(se[19]=pe=>I.$emit("blur"))}),Je({_:2},[Ve(o(F),(pe,ka)=>({name:pe,fn:ke(ha=>[ie(I.$slots,pe,je(la({...ha})))])}))]),1040,["internal-model-value","class","open-on-top","no-overlay-focus","collapse","is-text-input-date","onRecalculatePosition"])],16)):q("",!0)]),_:3},8,["name","css"])]),_:3},16))],10,Bu))}}),ht=(()=>{const e=Iu;return e.install=a=>{a.component("Vue3DatePicker",e)},e})(),Nu=Object.freeze(Object.defineProperty({__proto__:null,default:ht},Symbol.toStringTag,{value:"Module"}));Object.entries(Nu).forEach(([e,a])=>{e!=="default"&&(ht[e]=a)});const Fu={class:"flex justify-content-center"},Lu={class:"ms-2"},zu={class:"mt-1 text-start"},Eu=qe({__name:"TimeRangeSelect",props:{modelValue:{required:!0,type:String}},emits:["update:modelValue"],setup(e,{emit:a}){const n=wn(),t=new Date,l=new Date;l.setDate(l.getDate()+1);const s=Z(""),i=Z(""),f=e,d=a,T=W(()=>s.value!==""||i.value!==""?`${il(s.value)}-${il(i.value)}`:"");function p(){const v=f.modelValue.split("-");v[0]?s.value=Rt(v[0].length===8?dl(v[0],"yyyyMMdd"):parseInt(v[0])*1e3):s.value="",v.length>1&&v[1]?i.value=Rt(v[1].length===8?dl(v[1],"yyyyMMdd"):parseInt(v[1])*1e3):i.value="",d("update:modelValue",T.value)}return na(()=>T.value,()=>d("update:modelValue",T.value)),na(()=>f.modelValue,()=>{p()}),Qe(()=>{f.modelValue?p():s.value=Rt(new Date(t.getFullYear(),t.getMonth()-1,1)),d("update:modelValue",T.value)}),(v,m)=>(M(),H("div",null,[me("div",Fu,[me("div",null,[m[2]||(m[2]=me("label",{for:"dateFrom"},"Start Date",-1)),ta(o(ht),{id:"dateFrom",modelValue:o(s),"onUpdate:modelValue":m[0]||(m[0]=k=>mt(s)?s.value=k:null),dark:o(n).isDarkTheme,"max-date":o(t),"model-type":"yyyy-MM-dd",format:"yyyy-MM-dd",class:"mt-1","text-input":"","auto-apply":"","enable-time-picker":!1},null,8,["modelValue","dark","max-date"])]),me("div",Lu,[m[3]||(m[3]=me("label",{for:"dateTo"},"End Date",-1)),ta(o(ht),{modelValue:o(i),"onUpdate:modelValue":m[1]||(m[1]=k=>mt(i)?i.value=k:null),dark:o(n).isDarkTheme,class:"mt-1","max-date":o(l),"model-type":"yyyy-MM-dd",format:"yyyy-MM-dd","text-input":"","auto-apply":"","enable-time-picker":!1},null,8,["modelValue","dark","max-date"])])]),me("label",zu,[m[4]||(m[4]=_a(" Timerange: ")),me("b",null,Ze(o(T)),1)])]))}});export{Eu as _};
//# sourceMappingURL=TimeRangeSelect.vue_vue_type_script_setup_true_lang-DJKJVTiO.js.map
