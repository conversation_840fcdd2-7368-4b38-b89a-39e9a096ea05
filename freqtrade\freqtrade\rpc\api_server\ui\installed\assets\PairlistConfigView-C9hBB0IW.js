import{c as ae,s as ke,a as ye,_ as he,b as we}from"./index-Dt2Q_yjR.js";import{_ as Ve}from"./check-circle-C_wO5mMc.js";import{d as M,c1 as $e,c as C,a as r,e as d,f as t,k as y,l as v,z as U,aV as le,a5 as ie,M as Se,s as W,W as re,Q as Be,u as ue,r as x,R as te,S as oe,j as T,U as ce,c2 as I,bY as Pe,C as ne,b as p,_ as de,a3 as Ee,n as O,h as k,F as A,m as D,Y as me,a6 as pe,i as fe,g as Y,x as q,H as _e,o as Ie,c3 as Ue,t as ge}from"./index-Cwqm8wBn.js";import{s as Ne}from"./index-D6LFPO4a.js";import{_ as ze,a as Te}from"./chevron-up-DmDiFdj7.js";import{_ as Me,u as se,m as Fe}from"./useSortable-66GE0llz.js";import{s as Re}from"./index-DfIy61Gi.js";import{_ as Oe}from"./ExchangeSelect.vue_vue_type_script_setup_true_lang-CExpuEN2.js";import"./check-olqpNIE9.js";import"./plus-box-outline-CDxaZbJP.js";import"./index-Bpiivi0c.js";var V=(u=>(u.string="string",u.number="number",u.boolean="boolean",u.option="option",u.list="list",u))(V||{});const Ae={class:"relative group copy-container"},De={key:0,class:"text-sm"},je={class:"text-start border rounded-sm border-surface-500 p-2 m-0 bg-surface-50 dark:bg-surface-900 overflow-auto"},He=M({__name:"CopyableTextfield",props:{content:{type:[String,Array],required:!0},isValid:{type:Boolean,default:!0}},setup(u){const{copy:n,isSupported:e,copied:a}=$e();return(g,m)=>{const f=Ve,i=ae;return r(),C("div",Ae,[d("div",{class:"absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity hover:cursor-pointer flex dark:bg-surface-700 bg-surface-200 p-1 rounded-sm items-center justify-center flex-row",onClick:m[0]||(m[0]=o=>t(n)(typeof u.content=="string"?u.content:JSON.stringify(u.content)))},[t(a)?(r(),C("span",De,"Copied!")):y("",!0),t(e)&&t(a)?(r(),v(f,{key:1})):y("",!0),!t(a)&&t(e)&&u.isValid?(r(),v(i,{key:2})):y("",!0)]),d("pre",je,[d("code",null,U(u.content),1)])])}}}),Ge={class:"pb-1 flex flex-row text-start"},Je={class:"w-2/5"},Le={class:"flex flex-col w-full"},qe={class:"text-muted text-sm text-surface-500"},We=M({__name:"PairlistConfigParameter",props:le({param:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(u){const n=ie(u,"modelValue");return(e,a)=>{const g=W,m=re,f=Se("BFormSelect"),i=ze;return r(),C("div",Ge,[d("label",Je,U(e.param.description),1),d("div",Le,[e.param.type===t(V).string||e.param.type===t(V).number?(r(),v(g,{key:0,modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=o=>n.value=o),size:"small"},null,8,["modelValue"])):y("",!0),e.param.type===t(V).boolean?(r(),v(m,{key:1,modelValue:n.value,"onUpdate:modelValue":a[1]||(a[1]=o=>n.value=o)},null,8,["modelValue"])):y("",!0),e.param.type===t(V).option?(r(),v(f,{key:2,modelValue:n.value,"onUpdate:modelValue":a[2]||(a[2]=o=>n.value=o),options:e.param.options},null,8,["modelValue","options"])):y("",!0),e.param.type===t(V).list?(r(),v(i,{key:3,modelValue:n.value,"onUpdate:modelValue":a[3]||(a[3]=o=>n.value=o)},null,8,["modelValue"])):y("",!0),d("span",qe,U(e.param.help),1)])])}}}),j=Be("pairlistConfig",()=>{var K,X,Z,ee;const u=ue(),n=x(!1),e=x(),a=x(((K=u.activeBot)==null?void 0:K.stakeCurrency)??"USDT"),g=x([]),m=x(!1),f=x({exchange:((X=u.activeBot)==null?void 0:X.botState.exchange)??"",trade_mode:{trading_mode:((Z=u.activeBot)==null?void 0:Z.botState.trading_mode)??te.SPOT,margin_mode:((ee=u.activeBot)==null?void 0:ee.botState.trading_mode)===te.FUTURES?oe.ISOLATED:oe.NONE}}),i=x(_()),o=x([]),c=x(""),$=T(()=>{var s;return!!((s=i.value.pairlists[0])!=null&&s.is_pairlist_generator)}),P=T(()=>$.value&&i.value.pairlists.length>0),S=T(()=>JSON.stringify(Q(),null,2)),w=s=>o.value.findIndex(l=>l.name===s)>-1;function h(s,l){s=structuredClone(I(s)),s.showParameters=!1,s.id||(s.id=Date.now().toString(36)+Math.random().toString(36).substring(2));for(const b in s.params)s.params[b].value=Pe(s.params[b].default)?s.params[b].default:"";i.value.pairlists.splice(l,0,s)}function B(s){i.value.pairlists.splice(s,1)}function N(s=""){const l=o.value.findIndex(b=>b.name===i.value.name);i.value.name=s,l>-1?o.value[l]=structuredClone(I(i.value)):o.value.push(structuredClone(I(i.value)))}function R(s){const l=_({name:s});o.value.push(l),i.value=structuredClone(l)}function H(s=""){const l=_({name:s,pairlists:I(i.value.pairlists),blacklist:I(i.value.blacklist)});o.value.push(l),i.value=structuredClone(l)}function G(){const s=o.value.findIndex(l=>l.name===i.value.name);s>-1&&(o.value.splice(s,1),F(o.value.length>0?o.value[0].name:"default"))}function F(s){const l=o.value.find(b=>s===b.name);l?i.value=structuredClone(I(l)):R(s)}function _({name:s="",pairlists:l=[],blacklist:b=[]}={}){return{name:s,pairlists:l,blacklist:b}}function z(){i.value.blacklist.push("")}function J(s){i.value.blacklist.splice(s,1)}function ve(s){const l=o.value.find(b=>b.name===s);l&&(i.value.blacklist=structuredClone(I(l.blacklist)))}async function be(){const s=xe();n.value=!0;try{const{job_id:l}=await u.activeBot.evaluatePairlist(s);console.log("jobId",l),e.value=window.setInterval(async()=>{if(!(await u.activeBot.getBackgroundJobStatus(l)).running){clearInterval(e.value);const E=await u.activeBot.getPairlistEvalResult(l);n.value=!1,E.status==="success"?g.value=E.result.whitelist:E.error&&(ne(E.error,"error"),n.value=!1)}},1e3)}catch{ne("Evaluation failed","error"),n.value=!1}}function Ce(s,l){return s===V.number?Number(l):s===V.boolean?!!l:s===V.list?l:String(l)}function xe(){const l={pairlists:Q(),stake_currency:a.value,blacklist:i.value.blacklist};return m.value&&(console.log("setting custom exchange props"),l.exchange=f.value.exchange,l.trading_mode=f.value.trade_mode.trading_mode,l.margin_mode=f.value.trade_mode.margin_mode),l}function Q(){const s=[];return i.value.pairlists.forEach(l=>{const b={method:l.name};for(const E in l.params){const L=l.params[E];L.value&&(b[E]=Ce(L.type,L.value))}s.push(b)}),s}return ce(()=>i.value,()=>{c.value=i.value.name},{deep:!0}),{evaluating:n,whitelist:g,config:i,configJSON:S,savedConfigs:o,configName:c,startPairlistEvaluation:be,addToConfig:h,removeFromConfig:B,saveConfig:N,duplicateConfig:H,deleteConfig:G,newConfig:R,selectOrCreateConfig:F,addToBlacklist:z,removeFromBlacklist:J,duplicateBlacklist:ve,isSavedConfig:w,firstPairlistIsGenerator:$,pairlistValid:P,stakeCurrency:a,customExchange:m,selectedExchange:f}},{persist:{key:"ftPairlistConfig",pick:["savedConfigs","configName"]}}),Ye={class:"shadow-sm rounded-sm border border-surface-300 dark:border-surface-700"},Qe={class:"flex w-full text-start items-center bg-surface-200 dark:bg-surface-700 p-2 border-b border-surface-300 dark:border-surface-600"},Ke={class:"flex grow items-center"},Xe={class:"font-bold"},Ze={class:"text-sm"},et={key:0,class:"p-2"},tt=M({__name:"PairlistConfigItem",props:le({index:{}},{modelValue:{required:!0},modelModifiers:{}}),emits:["update:modelValue"],setup(u){const n=j(),e=ie(u,"modelValue"),a=T(()=>Object.keys(e.value.params).length>0);function g(){a.value&&(e.value.showParameters=!e.value.showParameters)}return(m,f)=>{const i=Me,o=de,c=Ee,$=Te,P=We;return r(),C("div",Ye,[d("div",Qe,[d("div",Ke,[p(i,{role:"button",class:"handle me-2 ms-2 shrink",width:"24",height:"24"}),d("div",{role:"button",class:"flex grow items-start flex-col user-select-none",onClick:g},[d("span",Xe,U(e.value.name),1),d("span",Ze,U(e.value.description),1)])]),p(o,{role:"button",width:"24",height:"24",class:"mx-2",onClick:f[0]||(f[0]=S=>t(n).removeFromConfig(m.index))}),e.value.showParameters?y("",!0):(r(),v(c,{key:0,class:O([t(a)&&!e.value.showParameters?"visible":"invisible","fs-4"]),role:"button",onClick:g},null,8,["class"])),e.value.showParameters?(r(),v($,{key:1,class:O([t(a)&&e.value.showParameters?"visible":"invisible","fs-4"]),role:"button",onClick:g},null,8,["class"])):y("",!0)]),p(me,null,{default:k(()=>[e.value.showParameters?(r(),C("div",et,[(r(!0),C(A,null,D(e.value.params,(S,w)=>(r(),v(P,{key:w,modelValue:e.value.params[w].value,"onUpdate:modelValue":h=>e.value.params[w].value=h,param:S},null,8,["modelValue","onUpdate:modelValue","param"]))),128))])):y("",!0)]),_:1})])}}}),ot={class:"flex mb-4 items-center gap-2"},nt=M({__name:"PairlistConfigBlacklist",setup(u){const n=j(),e=x(""),a=T(()=>n.savedConfigs.filter(g=>g.name!==n.config.name).map(g=>g.name));return(g,m)=>{const f=pe,i=ae,o=Y,c=W,$=de,P=ye,S=ke,w=Re;return r(),v(w,{toggleable:"",header:"Blacklist",collapsed:""},{default:k(()=>[d("div",ot,[m[3]||(m[3]=d("span",{class:"col-auto"},"Copy from:",-1)),p(f,{modelValue:t(e),"onUpdate:modelValue":m[0]||(m[0]=h=>fe(e)?e.value=h:null),size:"small",class:"grow",options:t(a)},null,8,["modelValue","options"]),p(o,{title:"Copy",size:"small",severity:"secondary",onClick:m[1]||(m[1]=h=>t(n).duplicateBlacklist(t(e)))},{icon:k(()=>[p(i)]),_:1})]),(r(!0),C(A,null,D(t(n).config.blacklist,(h,B)=>(r(),v(S,{key:B,class:"mb-2",size:"sm"},{default:k(()=>[p(c,{modelValue:t(n).config.blacklist[B],"onUpdate:modelValue":N=>t(n).config.blacklist[B]=N,size:"small"},null,8,["modelValue","onUpdate:modelValue"]),p(P,null,{default:k(()=>[p(o,{size:"small",severity:"secondary",onClick:N=>t(n).removeFromBlacklist(B)},{icon:k(()=>[p($)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),128)),p(o,{size:"small",onClick:m[2]||(m[2]=h=>t(n).addToBlacklist())},{default:k(()=>m[4]||(m[4]=[q("Add")])),_:1,__:[4]})]),_:1})}}}),st={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function at(u,n){return r(),C("svg",st,n[0]||(n[0]=[d("path",{fill:"currentColor",d:"M15 9H5V5h10m-3 14a3 3 0 0 1-3-3a3 3 0 0 1 3-3a3 3 0 0 1 3 3a3 3 0 0 1-3 3m5-16H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7z"},null,-1)]))}const lt=_e({name:"mdi-content-save",render:at}),it={class:"flex flex-col sm:flex-row mb-2 gap-2"},rt={key:1},ut=M({__name:"PairlistConfigActions",setup(u){const n=j();return(e,a)=>{const g=lt,m=Y,f=pe,i=we;return r(),C("div",it,[p(m,{title:"Save configuration",size:"small",severity:"primary",onClick:a[0]||(a[0]=o=>t(n).saveConfig(t(n).config.name))},{default:k(()=>[p(g)]),_:1}),p(he,{modelValue:t(n).config.name,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).config.name=o),"editable-name":"config","allow-add":!0,"allow-duplicate":!0,"allow-edit":!0,class:"flex grow",onDelete:t(n).deleteConfig,onDuplicate:a[4]||(a[4]=(o,c)=>t(n).duplicateConfig(c)),onNew:a[5]||(a[5]=o=>t(n).newConfig(o)),onRename:a[6]||(a[6]=(o,c)=>t(n).saveConfig(c))},{default:k(()=>[p(f,{modelValue:t(n).configName,"onUpdate:modelValue":[a[1]||(a[1]=o=>t(n).configName=o),a[2]||(a[2]=o=>t(n).selectOrCreateConfig(o))],size:"small",class:"w-full text-start",options:t(n).savedConfigs.map(o=>o.name)},null,8,["modelValue","options"])]),_:1},8,["modelValue","onDelete"]),p(m,{title:"Evaluate pairlist",disabled:t(n).evaluating||!t(n).pairlistValid,severity:"primary",class:"px-5",size:"small",onClick:a[7]||(a[7]=o=>t(n).startPairlistEvaluation())},{default:k(()=>[t(n).evaluating?(r(),v(i,{key:0,class:"h-5 w-5"})):(r(),C("span",rt,"Evaluate"))]),_:1},8,["disabled"])])}}}),ct={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function dt(u,n){return r(),C("svg",ct,n[0]||(n[0]=[d("path",{fill:"currentColor",d:"m17 12l-5 5v-3H8v-4h4V7zM3 19V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m2 0h14V5H5z"},null,-1)]))}const mt=_e({name:"mdi-arrow-right-bold-box-outline",render:dt}),pt={class:"flex px-3 mb-3 gap-3 flex-col lg:flex-row"},ft={class:"flex grow items-start flex-col"},_t={class:"font-bold"},gt={class:"text-sm text-muted-color"},vt={class:"flex flex-col grow"},bt={class:"border rounded-sm border-surface-500 p-2 mb-2"},Ct={class:"flex items-center gap-2 my-2"},xt={class:"mb-2 border rounded-sm border-surface-500 p-2 text-start"},kt={class:"flex flex-col w-full lg:w-3/12"},yt={class:"relative overflow-auto"},ht=M({__name:"PairlistConfigurator",setup(u){const n=ue(),e=j(),a=x([]),g=x(null),m=x(null),f=x("Config"),i=T(()=>e.config.pairlists.length==0);return se(m,a.value,{group:{name:"configurator",pull:"clone",put:!1},sort:!1,filter:".no-drag",dragClass:"dragging"}),se(g,e.config.pairlists,{handle:".handle",group:"configurator",onUpdate:async o=>{Fe(e.config.pairlists,o.oldIndex,o.newIndex)},onAdd:o=>{const c=a.value[o.oldIndex];e.addToConfig(c,o.newIndex),o.clone.replaceWith(o.item),o.clone.remove()}}),Ie(async()=>{a.value=(await n.activeBot.getPairlists()).pairlists.sort((o,c)=>o.is_pairlist_generator===c.is_pairlist_generator?o.name.localeCompare(c.name):o.is_pairlist_generator?-1:1),e.selectOrCreateConfig(e.isSavedConfig(e.configName)?e.configName:"default")}),ce(()=>e.whitelist,()=>{f.value="Results"}),(o,c)=>{const $=mt,P=Y,S=ut,w=W,h=re,B=Oe,N=nt,R=Ue,H=tt,G=Ne,F=He;return r(),C("div",pt,[d("ul",{ref_key:"availablePairlistsEl",ref:m,class:"divide-y border-x border-surface-500 rounded-sm border-y divide-solid divide-surface-500"},[(r(!0),C(A,null,D(t(a),_=>(r(),C("li",{key:_.name,class:O([{"no-drag text-gray-500 hover:cursor-default":t(e).config.pairlists.length==0&&!_.is_pairlist_generator},"pairlist flex text-start items-center py-2 px-3 hover:cursor-grab"])},[d("div",ft,[d("span",_t,U(_.name),1),d("span",gt,U(_.description),1)]),p(P,{severity:"secondary",class:"dark:text-white",variant:"text",disabled:t(e).config.pairlists.length==0&&!_.is_pairlist_generator,onClick:z=>t(e).addToConfig(_,t(e).config.pairlists.length)},{icon:k(()=>[p($)]),_:2},1032,["disabled","onClick"])],2))),128))],512),d("div",vt,[p(S),d("div",bt,[d("div",Ct,[c[4]||(c[4]=d("span",{class:"col-auto"},"Stake currency: ",-1)),p(w,{modelValue:t(e).stakeCurrency,"onUpdate:modelValue":c[0]||(c[0]=_=>t(e).stakeCurrency=_),size:"small"},null,8,["modelValue"])]),d("div",xt,[p(h,{modelValue:t(e).customExchange,"onUpdate:modelValue":c[1]||(c[1]=_=>t(e).customExchange=_),class:"mb-2"},{default:k(()=>c[5]||(c[5]=[q(" Custom Exchange ")])),_:1,__:[5]},8,["modelValue"]),p(me,{name:"fade"},{default:k(()=>[t(e).customExchange?(r(),v(B,{key:0,modelValue:t(e).selectedExchange,"onUpdate:modelValue":c[2]||(c[2]=_=>t(e).selectedExchange=_)},null,8,["modelValue"])):y("",!0)]),_:1})])]),p(N),t(e).config.pairlists.length>0&&!t(e).firstPairlistIsGenerator?(r(),v(R,{key:0,class:"my-2",severity:"warn"},{default:k(()=>c[6]||(c[6]=[q(" First entry in the pairlist must be a Generating pairlist, like StaticPairList or VolumePairList. ")])),_:1,__:[6]})):y("",!0),d("div",{ref_key:"pairlistConfigsEl",ref:g,class:O(["flex flex-col grow relative border rounded-sm border-surface-500 p-1 gap-2",{empty:t(i)}])},[(r(!0),C(A,null,D(t(e).config.pairlists,(_,z)=>(r(),v(H,{key:_.id,modelValue:t(e).config.pairlists[z],"onUpdate:modelValue":J=>t(e).config.pairlists[z]=J,index:z,onRemove:t(e).removeFromConfig},null,8,["modelValue","onUpdate:modelValue","index","onRemove"]))),128))],2)]),d("div",kt,[p(G,{modelValue:t(f),"onUpdate:modelValue":c[3]||(c[3]=_=>fe(f)?f.value=_:null),class:"mb-2",size:"small","allow-empty":!1,"option-label":"value","option-value":"value",options:[{value:"Config"},{value:"Results",disabled:t(e).whitelist.length===0}],"option-disabled":"disabled"},null,8,["modelValue","options"]),d("div",yt,[t(f)==="Config"?(r(),v(F,{key:0,class:"lg:a22bsolute w-full",content:t(e).configJSON,"is-valid":t(e).pairlistValid},null,8,["content","is-valid"])):y("",!0),t(f)==="Results"?(r(),v(F,{key:1,class:"lg:abs22olute w-full",content:t(e).whitelist},null,8,["content"])):y("",!0)])])])}}}),wt=ge(ht,[["__scopeId","data-v-47402f67"]]),Vt={};function $t(u,n){const e=wt;return r(),v(e,{class:"pt-4"})}const Rt=ge(Vt,[["render",$t]]);export{Rt as default};
//# sourceMappingURL=PairlistConfigView-C9hBB0IW.js.map
