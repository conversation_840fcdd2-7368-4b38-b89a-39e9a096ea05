import{H as o,c as t,a as n,e as r}from"./index-Cwqm8wBn.js";const c={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function l(s,e){return n(),t("svg",c,e[0]||(e[0]=[r("path",{fill:"currentColor",d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10s10-4.5 10-10S17.5 2 12 2m-2 15l-5-5l1.41-1.41L10 14.17l7.59-7.59L19 8z"},null,-1)]))}const i=o({name:"mdi-check-circle",render:l});export{i as _};
//# sourceMappingURL=check-circle-C_wO5mMc.js.map
