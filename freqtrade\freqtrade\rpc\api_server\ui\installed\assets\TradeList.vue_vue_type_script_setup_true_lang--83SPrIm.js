import{_ as re,S as Ne,V as Re,t as We,H as Ue,I as je,y as Ke,F as qe,aP as Ge,n as pe,j as he,aT as Xe,q as be,a2 as de,bE as Ye,cZ as J,T as Qe,R as te,c_ as ge,c6 as _,by as Ze,c$ as Je,a5 as et,d0 as ye,a7 as xe,d1 as tt,d2 as at,a4 as rt,aj as nt,aW as it,d3 as ot,d4 as lt}from"./installCanvasRenderer-SA1tPojE.js";import{ab as st}from"./chartZoom-DB0tK3Do.js";import{d as X,aV as ke,a5 as we,u as ne,r as V,b9 as ut,l as z,a as y,b2 as ie,h as T,e as k,w as Se,c as C,k as H,f as b,b as x,i as R,s as Ve,ba as ee,z as I,g as Y,x as W,bb as Te,Z as dt,$ as ct,a0 as ft,ag as mt,aq as vt,an as pt,bc as ht,bd as bt,N as K,a7 as gt,j as yt,E as xt,H as Q,be as Ee,B as kt,K as wt,U as St,aS as Vt,F as j,m as Tt,D as ce,bf as Et}from"./index-Cwqm8wBn.js";import{s as Be}from"./index-CONYmxgd.js";import{s as Pe}from"./index-D6LFPO4a.js";import{s as Bt,a as Pt}from"./index-DhBpwJns.js";import{a as Ct,_ as Dt}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";import{s as Lt}from"./index-BL9ilpkx.js";var Ce=function(t){re(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],a.visualDrawType="stroke",a}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(Ne);Re(Ce,st,!0);var _t=function(t){re(e,t);function e(){var a=t!==null&&t.apply(this,arguments)||this;return a.type=e.type,a}return e.prototype.render=function(a,n,i){var r=a.getData(),o=this.group,l=this._data;this._data||o.removeAll();var f=a.get("layout")==="horizontal"?1:0;r.diff(l).add(function(m){if(r.hasValue(m)){var c=r.getItemLayout(m),s=fe(c,r,m,f,!0);r.setItemGraphicEl(m,s),o.add(s)}}).update(function(m,c){var s=l.getItemGraphicEl(c);if(!r.hasValue(m)){o.remove(s);return}var p=r.getItemLayout(m);s?(We(s),De(p,s,r,m)):s=fe(p,r,m,f),o.add(s),r.setItemGraphicEl(m,s)}).remove(function(m){var c=l.getItemGraphicEl(m);c&&o.remove(c)}).execute(),this._data=r},e.prototype.remove=function(a){var n=this.group,i=this._data;this._data=null,i&&i.eachItemGraphicEl(function(r){r&&n.remove(r)})},e.type="boxplot",e}(Ue),At=function(){function t(){}return t}(),Mt=function(t){re(e,t);function e(a){var n=t.call(this,a)||this;return n.type="boxplotBoxPath",n}return e.prototype.getDefaultShape=function(){return new At},e.prototype.buildPath=function(a,n){var i=n.points,r=0;for(a.moveTo(i[r][0],i[r][1]),r++;r<4;r++)a.lineTo(i[r][0],i[r][1]);for(a.closePath();r<i.length;r++)a.moveTo(i[r][0],i[r][1]),r++,a.lineTo(i[r][0],i[r][1])},e}(Ge);function fe(t,e,a,n,i){var r=t.ends,o=new Mt({shape:{points:i?$t(r,n,t):r}});return De(t,o,e,a,i),o}function De(t,e,a,n,i){var r=a.hostModel,o=je[i?"initProps":"updateProps"];o(e,{shape:{points:t.ends}},r,n),e.useStyle(a.getItemVisual(n,"style")),e.style.strokeNoScale=!0,e.z2=100;var l=a.getItemModel(n),f=l.getModel("emphasis");Ke(e,l),qe(e,f.get("focus"),f.get("blurScope"),f.get("disabled"))}function $t(t,e,a){return pe(t,function(n){return n=n.slice(),n[e]=a.initBaseline,n})}var q=he;function It(t){var e=Ft(t);q(e,function(a){var n=a.seriesModels;n.length&&(Ot(a),q(n,function(i,r){zt(i,a.boxOffsetList[r],a.boxWidthList[r])}))})}function Ft(t){var e=[],a=[];return t.eachSeriesByType("boxplot",function(n){var i=n.getBaseAxis(),r=Xe(a,i);r<0&&(r=a.length,a[r]=i,e[r]={axis:i,seriesModels:[]}),e[r].seriesModels.push(n)}),e}function Ot(t){var e=t.axis,a=t.seriesModels,n=a.length,i=t.boxWidthList=[],r=t.boxOffsetList=[],o=[],l;if(e.type==="category")l=e.getBandWidth();else{var f=0;q(a,function(h){f=Math.max(f,h.getData().count())});var m=e.getExtent();l=Math.abs(m[1]-m[0])/f}q(a,function(h){var d=h.get("boxWidth");be(d)||(d=[d,d]),o.push([de(d[0],l)||0,de(d[1],l)||0])});var c=l*.8-2,s=c/n*.3,p=(c-s*(n-1))/n,w=p/2-c/2;q(a,function(h,d){r.push(w),w+=s+p,i.push(Math.min(Math.max(p,o[d][0]),o[d][1]))})}function zt(t,e,a){var n=t.coordinateSystem,i=t.getData(),r=a/2,o=t.get("layout")==="horizontal"?0:1,l=1-o,f=["x","y"],m=i.mapDimension(f[o]),c=i.mapDimensionsAll(f[l]);if(m==null||c.length<5)return;for(var s=0;s<i.count();s++){var p=i.get(m,s),w=u(p,c[2],s),h=u(p,c[0],s),d=u(p,c[1],s),E=u(p,c[3],s),B=u(p,c[4],s),g=[];F(g,d,!1),F(g,E,!0),g.push(h,d,B,E),D(g,h),D(g,B),D(g,w),i.setItemLayout(s,{initBaseline:w[l],ends:g})}function u($,O,N){var S=i.get(O,N),U=[];U[o]=$,U[l]=S;var v;return isNaN($)||isNaN(S)?v=[NaN,NaN]:(v=n.dataToPoint(U),v[o]+=e),v}function F($,O,N){var S=O.slice(),U=O.slice();S[o]+=r,U[o]-=r,N?$.push(S,U):$.push(U,S)}function D($,O){var N=O.slice(),S=O.slice();N[o]-=r,S[o]+=r,$.push(N,S)}}function Ht(t,e){e=e||{};for(var a=[],n=[],i=e.boundIQR,r=i==="none"||i===0,o=0;o<t.length;o++){var l=Ye(t[o].slice()),f=J(l,.25),m=J(l,.5),c=J(l,.75),s=l[0],p=l[l.length-1],w=(i??1.5)*(c-f),h=r?s:Math.max(s,f-w),d=r?p:Math.min(p,c+w),E=e.itemNameFormatter,B=Qe(E)?E({value:o}):te(E)?E.replace("{value}",o+""):o+"";a.push([B,h,f,m,c,d]);for(var g=0;g<l.length;g++){var u=l[g];if(u<h||u>d){var F=[B,u];n.push(F)}}}return{boxData:a,outliers:n}}var Nt={type:"echarts:boxplot",transform:function(e){var a=e.upstream;if(a.sourceFormat!==ge){var n="";_(n)}var i=Ht(a.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:i.boxData},{data:i.outliers}]}};function lr(t){t.registerSeriesModel(Ce),t.registerChartView(_t),t.registerLayout(It),t.registerTransform(Nt)}var me={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},Rt=function(){function t(e){var a=this._condVal=te(e)?new RegExp(e):at(e)?e:null;if(a==null){var n="";_(n)}}return t.prototype.evaluate=function(e){var a=typeof e;return te(a)?this._condVal.test(e):rt(a)?this._condVal.test(e+""):!1},t}(),Wt=function(){function t(){}return t.prototype.evaluate=function(){return this.value},t}(),Ut=function(){function t(){}return t.prototype.evaluate=function(){for(var e=this.children,a=0;a<e.length;a++)if(!e[a].evaluate())return!1;return!0},t}(),jt=function(){function t(){}return t.prototype.evaluate=function(){for(var e=this.children,a=0;a<e.length;a++)if(e[a].evaluate())return!0;return!1},t}(),Kt=function(){function t(){}return t.prototype.evaluate=function(){return!this.child.evaluate()},t}(),qt=function(){function t(){}return t.prototype.evaluate=function(){for(var e=!!this.valueParser,a=this.getValue,n=a(this.valueGetterParam),i=e?this.valueParser(n):null,r=0;r<this.subCondList.length;r++)if(!this.subCondList[r].evaluate(e?i:n))return!1;return!0},t}();function oe(t,e){if(t===!0||t===!1){var a=new Wt;return a.value=t,a}var n="";return Le(t)||_(n),t.and?ve("and",t,e):t.or?ve("or",t,e):t.not?Gt(t,e):Xt(t,e)}function ve(t,e,a){var n=e[t],i="";be(n)||_(i),n.length||_(i);var r=t==="and"?new Ut:new jt;return r.children=pe(n,function(o){return oe(o,a)}),r.children.length||_(i),r}function Gt(t,e){var a=t.not,n="";Le(a)||_(n);var i=new Kt;return i.child=oe(a,e),i.child||_(n),i}function Xt(t,e){for(var a="",n=e.prepareGetValue(t),i=[],r=et(t),o=t.parser,l=o?ye(o):null,f=0;f<r.length;f++){var m=r[f];if(!(m==="parser"||e.valueGetterAttrMap.get(m))){var c=xe(me,m)?me[m]:m,s=t[m],p=l?l(s):s,w=tt(c,p)||c==="reg"&&new Rt(p);w||_(a),i.push(w)}}i.length||_(a);var h=new qt;return h.valueGetterParam=n,h.valueParser=l,h.getValue=e.getValue,h.subCondList=i,h}function Le(t){return Ze(t)&&!Je(t)}var Yt=function(){function t(e,a){this._cond=oe(e,a)}return t.prototype.evaluate=function(){return this._cond.evaluate()},t}();function Qt(t,e){return new Yt(t,e)}var Zt={type:"echarts:filter",transform:function(t){for(var e=t.upstream,a,n=Qt(t.config,{valueGetterAttrMap:nt({dimension:!0}),prepareGetValue:function(l){var f="",m=l.dimension;xe(l,"dimension")||_(f);var c=e.getDimensionInfo(m);return c||_(f),{dimIdx:c.index}},getValue:function(l){return e.retrieveValueFromItem(a,l.dimIdx)}}),i=[],r=0,o=e.count();r<o;r++)a=e.getRawDataItem(r),n.evaluate()&&i.push(a);return{data:i}}},Jt={type:"echarts:sort",transform:function(t){var e=t.upstream,a=t.config,n="",i=it(a);i.length||_(n);var r=[];he(i,function(c){var s=c.dimension,p=c.order,w=c.parser,h=c.incomparable;if(s==null&&_(n),p!=="asc"&&p!=="desc"&&_(n),h&&h!=="min"&&h!=="max"){var d="";_(d)}if(p!=="asc"&&p!=="desc"){var E="";_(E)}var B=e.getDimensionInfo(s);B||_(n);var g=w?ye(w):null;w&&!g&&_(n),r.push({dimIdx:B.index,parser:g,comparator:new ot(p,h)})});var o=e.sourceFormat;o!==ge&&o!==lt&&_(n);for(var l=[],f=0,m=e.count();f<m;f++)l.push(e.getRawDataItem(f));return l.sort(function(c,s){for(var p=0;p<r.length;p++){var w=r[p],h=e.retrieveValueFromItem(c,w.dimIdx),d=e.retrieveValueFromItem(s,w.dimIdx);w.parser&&(h=w.parser(h),d=w.parser(d));var E=w.comparator.evaluate(h,d);if(E!==0)return E}return 0}),{data:l}}};function sr(t){t.registerTransform(Zt),t.registerTransform(Jt)}const ea={key:0},ta={for:"stake-input",class:"block font-medium mb-1"},aa={key:1},ra={key:2},na={class:"flex justify-end gap-2"},ia=X({__name:"ForceEntryForm",props:ke({pair:{type:String,default:""},positionIncrease:{type:Boolean,default:!1}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(t){const e=t,a=we(t,"modelValue"),n=ne(),i=V(),r=V(""),o=V(void 0),l=V(void 0),f=V(void 0),m=V(""),c=V(ut.long),s=V("force_entry"),p=[{value:"market",text:"Market"},{value:"limit",text:"Limit"}],w=[{value:"long",text:"Long"},{value:"short",text:"Short"}],h=()=>{var u;return(u=i.value)==null?void 0:u.checkValidity()},d=async()=>{if(!h())return;const g={pair:r.value};o.value&&(g.price=Number(o.value)),m.value&&(g.ordertype=m.value),l.value&&(g.stakeamount=l.value),n.activeBot.botApiVersion>=2.13&&n.activeBot.shortAllowed&&(g.side=c.value),n.activeBot.botApiVersion>=2.16&&s.value&&(g.entry_tag=s.value),f.value&&(g.leverage=f.value),n.activeBot.forceentry(g),await Te(),a.value=!1},E=()=>{var g,u,F,D,$,O,N,S;console.log("resetForm"),r.value=e.pair,o.value=void 0,l.value=void 0,m.value=((u=(g=n.activeBot.botState)==null?void 0:g.order_types)==null?void 0:u.forcebuy)||((D=(F=n.activeBot.botState)==null?void 0:F.order_types)==null?void 0:D.force_entry)||((O=($=n.activeBot.botState)==null?void 0:$.order_types)==null?void 0:O.buy)||((S=(N=n.activeBot.botState)==null?void 0:N.order_types)==null?void 0:S.entry)||"limit"},B=()=>{d()};return(g,u)=>{const F=Pe,D=Ve,$=Be,O=Y,N=ie;return y(),z(N,{visible:a.value,"onUpdate:visible":u[9]||(u[9]=S=>a.value=S),header:t.positionIncrease?`Increasing position for ${t.pair}`:"Force entering a trade",modal:"",onShow:E,onHide:E},{footer:T(()=>[k("div",na,[x(O,{severity:"secondary",size:"small",onClick:u[8]||(u[8]=S=>a.value=!1)},{default:T(()=>u[16]||(u[16]=[W(" Cancel ")])),_:1,__:[16]}),x(O,{severity:"primary",size:"small",onClick:B},{default:T(()=>u[17]||(u[17]=[W(" Enter Position ")])),_:1,__:[17]})])]),default:T(()=>[k("form",{ref_key:"form",ref:i,class:"space-y-4 md:min-w-[32rem]",onSubmit:Se(d,["prevent"])},[b(n).activeBot.botApiVersion>=2.13&&b(n).activeBot.shortAllowed?(y(),C("div",ea,[u[10]||(u[10]=k("label",{class:"block font-medium mb-1"},"Order direction (Long or Short)",-1)),x(F,{modelValue:b(c),"onUpdate:modelValue":u[0]||(u[0]=S=>R(c)?c.value=S:null),options:w,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])])):H("",!0),k("div",null,[u[11]||(u[11]=k("label",{for:"pair-input",class:"block font-medium mb-1"},"Pair",-1)),x(D,{id:"pair-input",modelValue:b(r),"onUpdate:modelValue":u[1]||(u[1]=S=>R(r)?r.value=S:null),disabled:t.positionIncrease,required:"",class:"w-full",onKeydown:ee(B,["enter"]),onFocus:u[2]||(u[2]=S=>S.target.select())},null,8,["modelValue","disabled"])]),k("div",null,[u[12]||(u[12]=k("label",{for:"price-input",class:"block font-medium mb-1"},"Price [optional]",-1)),x($,{id:"price-input",modelValue:b(o),"onUpdate:modelValue":u[3]||(u[3]=S=>R(o)?o.value=S:null),"show-buttons":"",min:0,"max-fraction-digits":8,step:.1,class:"w-full",onKeydown:ee(B,["enter"])},null,8,["modelValue"])]),k("div",null,[k("label",ta,"* Stake-amount in "+I(b(n).activeBot.stakeCurrency)+" [optional]",1),x($,{id:"stake-input",modelValue:b(l),"onUpdate:modelValue":u[4]||(u[4]=S=>R(l)?l.value=S:null),"show-buttons":"",min:0,step:b(n).activeBot.stakeCurrency==="USDT"?10:1,"max-fraction-digits":5,fluid:""},null,8,["modelValue","step"])]),b(n).activeBot.botApiVersion>2.16&&b(n).activeBot.shortAllowed?(y(),C("div",aa,[u[13]||(u[13]=k("label",{for:"leverage-input",class:"block font-medium mb-1"},"Leverage to apply [optional]",-1)),x($,{id:"leverage-input",modelValue:b(f),"onUpdate:modelValue":u[5]||(u[5]=S=>R(f)?f.value=S:null),"show-buttons":"",min:0,step:1,"max-fraction-digits":1,class:"w-full",onKeydown:ee(B,["enter"])},null,8,["modelValue"])])):H("",!0),k("div",null,[u[14]||(u[14]=k("label",{class:"block text-sm font-medium mb-1"},"OrderType",-1)),x(F,{modelValue:b(m),"onUpdate:modelValue":u[6]||(u[6]=S=>R(m)?m.value=S:null),options:p,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])]),b(n).activeBot.botApiVersion>1.16?(y(),C("div",ra,[u[15]||(u[15]=k("label",{for:"enterTag-input",class:"block text-sm font-medium mb-1"},"* Custom entry tag [optional]",-1)),x(D,{id:"enterTag-input",modelValue:b(s),"onUpdate:modelValue":u[7]||(u[7]=S=>R(s)?s.value=S:null),class:"w-full"},null,8,["modelValue"])])):H("",!0)],544)]),_:1},8,["visible","header"])}}});var oa=dt`
    .p-slider {
        position: relative;
        background: dt('slider.track.background');
        border-radius: dt('slider.track.border.radius');
    }

    .p-slider-handle {
        cursor: grab;
        touch-action: none;
        user-select: none;
        display: flex;
        justify-content: center;
        align-items: center;
        height: dt('slider.handle.height');
        width: dt('slider.handle.width');
        background: dt('slider.handle.background');
        border-radius: dt('slider.handle.border.radius');
        transition:
            background dt('slider.transition.duration'),
            color dt('slider.transition.duration'),
            border-color dt('slider.transition.duration'),
            box-shadow dt('slider.transition.duration'),
            outline-color dt('slider.transition.duration');
        outline-color: transparent;
    }

    .p-slider-handle::before {
        content: '';
        width: dt('slider.handle.content.width');
        height: dt('slider.handle.content.height');
        display: block;
        background: dt('slider.handle.content.background');
        border-radius: dt('slider.handle.content.border.radius');
        box-shadow: dt('slider.handle.content.shadow');
        transition: background dt('slider.transition.duration');
    }

    .p-slider:not(.p-disabled) .p-slider-handle:hover {
        background: dt('slider.handle.hover.background');
    }

    .p-slider:not(.p-disabled) .p-slider-handle:hover::before {
        background: dt('slider.handle.content.hover.background');
    }

    .p-slider-handle:focus-visible {
        box-shadow: dt('slider.handle.focus.ring.shadow');
        outline: dt('slider.handle.focus.ring.width') dt('slider.handle.focus.ring.style') dt('slider.handle.focus.ring.color');
        outline-offset: dt('slider.handle.focus.ring.offset');
    }

    .p-slider-range {
        display: block;
        background: dt('slider.range.background');
        border-radius: dt('slider.track.border.radius');
    }

    .p-slider.p-slider-horizontal {
        height: dt('slider.track.size');
    }

    .p-slider-horizontal .p-slider-range {
        inset-block-start: 0;
        inset-inline-start: 0;
        height: 100%;
    }

    .p-slider-horizontal .p-slider-handle {
        inset-block-start: 50%;
        margin-block-start: calc(-1 * calc(dt('slider.handle.height') / 2));
        margin-inline-start: calc(-1 * calc(dt('slider.handle.width') / 2));
    }

    .p-slider-vertical {
        min-height: 100px;
        width: dt('slider.track.size');
    }

    .p-slider-vertical .p-slider-handle {
        inset-inline-start: 50%;
        margin-inline-start: calc(-1 * calc(dt('slider.handle.width') / 2));
        margin-block-end: calc(-1 * calc(dt('slider.handle.height') / 2));
    }

    .p-slider-vertical .p-slider-range {
        inset-block-end: 0;
        inset-inline-start: 0;
        width: 100%;
    }
`,la={handle:{position:"absolute"},range:{position:"absolute"}},sa={root:function(e){var a=e.instance,n=e.props;return["p-slider p-component",{"p-disabled":n.disabled,"p-invalid":a.$invalid,"p-slider-horizontal":n.orientation==="horizontal","p-slider-vertical":n.orientation==="vertical"}]},range:"p-slider-range",handle:"p-slider-handle"},ua=ct.extend({name:"slider",style:oa,classes:sa,inlineStyles:la}),da={name:"BaseSlider",extends:ft,props:{min:{type:Number,default:0},max:{type:Number,default:100},orientation:{type:String,default:"horizontal"},step:{type:Number,default:null},range:{type:Boolean,default:!1},tabindex:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:ua,provide:function(){return{$pcSlider:this,$parentInstance:this}}};function G(t){"@babel/helpers - typeof";return G=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(t)}function ca(t,e,a){return(e=fa(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function fa(t){var e=ma(t,"string");return G(e)=="symbol"?e:e+""}function ma(t,e){if(G(t)!="object"||!t)return t;var a=t[Symbol.toPrimitive];if(a!==void 0){var n=a.call(t,e);if(G(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function va(t){return ga(t)||ba(t)||ha(t)||pa()}function pa(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ha(t,e){if(t){if(typeof t=="string")return ae(t,e);var a={}.toString.call(t).slice(8,-1);return a==="Object"&&t.constructor&&(a=t.constructor.name),a==="Map"||a==="Set"?Array.from(t):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?ae(t,e):void 0}}function ba(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ga(t){if(Array.isArray(t))return ae(t)}function ae(t,e){(e==null||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}var _e={name:"Slider",extends:da,inheritAttrs:!1,emits:["change","slideend"],dragging:!1,handleIndex:null,initX:null,initY:null,barWidth:null,barHeight:null,dragListener:null,dragEndListener:null,beforeUnmount:function(){this.unbindDragListeners()},methods:{updateDomData:function(){var e=this.$el.getBoundingClientRect();this.initX=e.left+ht(),this.initY=e.top+bt(),this.barWidth=this.$el.offsetWidth,this.barHeight=this.$el.offsetHeight},setValue:function(e){var a,n=e.touches?e.touches[0].pageX:e.pageX,i=e.touches?e.touches[0].pageY:e.pageY;this.orientation==="horizontal"?pt(this.$el)?a=(this.initX+this.barWidth-n)*100/this.barWidth:a=(n-this.initX)*100/this.barWidth:a=(this.initY+this.barHeight-i)*100/this.barHeight;var r=(this.max-this.min)*(a/100)+this.min;if(this.step){var o=this.range?this.value[this.handleIndex]:this.value,l=r-o;l<0?r=o+Math.ceil(r/this.step-o/this.step)*this.step:l>0&&(r=o+Math.floor(r/this.step-o/this.step)*this.step)}else r=Math.floor(r);this.updateModel(e,r)},updateModel:function(e,a){var n=Math.round(a*100)/100,i;this.range?(i=this.value?va(this.value):[],this.handleIndex==0?(n<this.min?n=this.min:n>=this.max&&(n=this.max),i[0]=n):(n>this.max?n=this.max:n<=this.min&&(n=this.min),i[1]=n)):(n<this.min?n=this.min:n>this.max&&(n=this.max),i=n),this.writeValue(i,e),this.$emit("change",i)},onDragStart:function(e,a){this.disabled||(this.$el.setAttribute("data-p-sliding",!0),this.dragging=!0,this.updateDomData(),this.range&&this.value[0]===this.max?this.handleIndex=0:this.handleIndex=a,e.currentTarget.focus())},onDrag:function(e){this.dragging&&this.setValue(e)},onDragEnd:function(e){this.dragging&&(this.dragging=!1,this.$el.setAttribute("data-p-sliding",!1),this.$emit("slideend",{originalEvent:e,value:this.value}))},onBarClick:function(e){this.disabled||vt(e.target,"data-pc-section")!=="handle"&&(this.updateDomData(),this.setValue(e))},onMouseDown:function(e,a){this.bindDragListeners(),this.onDragStart(e,a)},onKeyDown:function(e,a){switch(this.handleIndex=a,e.code){case"ArrowDown":case"ArrowLeft":this.decrementValue(e,a),e.preventDefault();break;case"ArrowUp":case"ArrowRight":this.incrementValue(e,a),e.preventDefault();break;case"PageDown":this.decrementValue(e,a,!0),e.preventDefault();break;case"PageUp":this.incrementValue(e,a,!0),e.preventDefault();break;case"Home":this.updateModel(e,this.min),e.preventDefault();break;case"End":this.updateModel(e,this.max),e.preventDefault();break}},onBlur:function(e,a){var n,i;(n=(i=this.formField).onBlur)===null||n===void 0||n.call(i,e)},decrementValue:function(e,a){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i;this.range?this.step?i=this.value[a]-this.step:i=this.value[a]-1:this.step?i=this.value-this.step:!this.step&&n?i=this.value-10:i=this.value-1,this.updateModel(e,i),e.preventDefault()},incrementValue:function(e,a){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i;this.range?this.step?i=this.value[a]+this.step:i=this.value[a]+1:this.step?i=this.value+this.step:!this.step&&n?i=this.value+10:i=this.value+1,this.updateModel(e,i),e.preventDefault()},bindDragListeners:function(){this.dragListener||(this.dragListener=this.onDrag.bind(this),document.addEventListener("mousemove",this.dragListener)),this.dragEndListener||(this.dragEndListener=this.onDragEnd.bind(this),document.addEventListener("mouseup",this.dragEndListener))},unbindDragListeners:function(){this.dragListener&&(document.removeEventListener("mousemove",this.dragListener),this.dragListener=null),this.dragEndListener&&(document.removeEventListener("mouseup",this.dragEndListener),this.dragEndListener=null)},rangeStyle:function(){if(this.range){var e=this.rangeEndPosition>this.rangeStartPosition?this.rangeEndPosition-this.rangeStartPosition:this.rangeStartPosition-this.rangeEndPosition,a=this.rangeEndPosition>this.rangeStartPosition?this.rangeStartPosition:this.rangeEndPosition;return this.horizontal?{"inset-inline-start":a+"%",width:e+"%"}:{bottom:a+"%",height:e+"%"}}else return this.horizontal?{width:this.handlePosition+"%"}:{height:this.handlePosition+"%"}},handleStyle:function(){return this.horizontal?{"inset-inline-start":this.handlePosition+"%"}:{bottom:this.handlePosition+"%"}},rangeStartHandleStyle:function(){return this.horizontal?{"inset-inline-start":this.rangeStartPosition+"%"}:{bottom:this.rangeStartPosition+"%"}},rangeEndHandleStyle:function(){return this.horizontal?{"inset-inline-start":this.rangeEndPosition+"%"}:{bottom:this.rangeEndPosition+"%"}}},computed:{value:function(){var e;if(this.range){var a,n,i,r;return[(a=(n=this.d_value)===null||n===void 0?void 0:n[0])!==null&&a!==void 0?a:this.min,(i=(r=this.d_value)===null||r===void 0?void 0:r[1])!==null&&i!==void 0?i:this.max]}return(e=this.d_value)!==null&&e!==void 0?e:this.min},horizontal:function(){return this.orientation==="horizontal"},vertical:function(){return this.orientation==="vertical"},handlePosition:function(){return this.value<this.min?0:this.value>this.max?100:(this.value-this.min)*100/(this.max-this.min)},rangeStartPosition:function(){return this.value&&this.value[0]!==void 0?this.value[0]<this.min?0:(this.value[0]-this.min)*100/(this.max-this.min):0},rangeEndPosition:function(){return this.value&&this.value.length===2&&this.value[1]!==void 0?this.value[1]>this.max?100:(this.value[1]-this.min)*100/(this.max-this.min):100},dataP:function(){return mt(ca({},this.orientation,this.orientation))}}},ya=["data-p"],xa=["data-p"],ka=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"],wa=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"],Sa=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"];function Va(t,e,a,n,i,r){return y(),C("div",K({class:t.cx("root"),onClick:e[18]||(e[18]=function(){return r.onBarClick&&r.onBarClick.apply(r,arguments)})},t.ptmi("root"),{"data-p-sliding":!1,"data-p":r.dataP}),[k("span",K({class:t.cx("range"),style:[t.sx("range"),r.rangeStyle()]},t.ptm("range"),{"data-p":r.dataP}),null,16,xa),t.range?H("",!0):(y(),C("span",K({key:0,class:t.cx("handle"),style:[t.sx("handle"),r.handleStyle()],onTouchstartPassive:e[0]||(e[0]=function(o){return r.onDragStart(o)}),onTouchmovePassive:e[1]||(e[1]=function(o){return r.onDrag(o)}),onTouchend:e[2]||(e[2]=function(o){return r.onDragEnd(o)}),onMousedown:e[3]||(e[3]=function(o){return r.onMouseDown(o)}),onKeydown:e[4]||(e[4]=function(o){return r.onKeyDown(o)}),onBlur:e[5]||(e[5]=function(o){return r.onBlur(o)}),tabindex:t.tabindex,role:"slider","aria-valuemin":t.min,"aria-valuenow":t.d_value,"aria-valuemax":t.max,"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel,"aria-orientation":t.orientation},t.ptm("handle"),{"data-p":r.dataP}),null,16,ka)),t.range?(y(),C("span",K({key:1,class:t.cx("handle"),style:[t.sx("handle"),r.rangeStartHandleStyle()],onTouchstartPassive:e[6]||(e[6]=function(o){return r.onDragStart(o,0)}),onTouchmovePassive:e[7]||(e[7]=function(o){return r.onDrag(o)}),onTouchend:e[8]||(e[8]=function(o){return r.onDragEnd(o)}),onMousedown:e[9]||(e[9]=function(o){return r.onMouseDown(o,0)}),onKeydown:e[10]||(e[10]=function(o){return r.onKeyDown(o,0)}),onBlur:e[11]||(e[11]=function(o){return r.onBlur(o,0)}),tabindex:t.tabindex,role:"slider","aria-valuemin":t.min,"aria-valuenow":t.d_value?t.d_value[0]:null,"aria-valuemax":t.max,"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel,"aria-orientation":t.orientation},t.ptm("startHandler"),{"data-p":r.dataP}),null,16,wa)):H("",!0),t.range?(y(),C("span",K({key:2,class:t.cx("handle"),style:[t.sx("handle"),r.rangeEndHandleStyle()],onTouchstartPassive:e[12]||(e[12]=function(o){return r.onDragStart(o,1)}),onTouchmovePassive:e[13]||(e[13]=function(o){return r.onDrag(o)}),onTouchend:e[14]||(e[14]=function(o){return r.onDragEnd(o)}),onMousedown:e[15]||(e[15]=function(o){return r.onMouseDown(o,1)}),onKeydown:e[16]||(e[16]=function(o){return r.onKeyDown(o,1)}),onBlur:e[17]||(e[17]=function(o){return r.onBlur(o,1)}),tabindex:t.tabindex,role:"slider","aria-valuemin":t.min,"aria-valuenow":t.d_value?t.d_value[1]:null,"aria-valuemax":t.max,"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel,"aria-orientation":t.orientation},t.ptm("endHandler"),{"data-p":r.dataP}),null,16,Sa)):H("",!0)],16,ya)}_e.render=Va;const Ta={class:"mb-4"},Ea={class:"mb-2"},Ba={for:"stake-input",class:"block font-medium mb-1"},Pa={class:"text-sm italic ml-1"},Ca={class:"space-y-2"},Da={class:"flex justify-end gap-2"},La=X({__name:"ForceExitForm",props:ke({trade:{type:Object,required:!0},stakeCurrencyDecimals:{type:Number,required:!0}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(t){const e=t,a=we(t,"modelValue"),n=ne(),i=V(),r=V(void 0),o=V("limit"),l=()=>{var d;return(d=i.value)==null?void 0:d.checkValidity()};async function f(){if(!l())return;const h={tradeid:String(e.trade.trade_id)};o.value&&(h.ordertype=o.value),r.value&&(h.amount=r.value),await Te(),n.activeBot.forceexit(h),a.value=!1}function m(){var h,d,E,B;r.value=e.trade.amount,o.value=((d=(h=n.activeBot.botState)==null?void 0:h.order_types)==null?void 0:d.force_exit)||((B=(E=n.activeBot.botState)==null?void 0:E.order_types)==null?void 0:B.exit)||"limit"}function c(){f()}const s=gt(r,250,{maxWait:500}),p=yt(()=>s.value&&e.trade.current_rate?`~${xt(s.value*e.trade.current_rate,e.trade.quote_currency||"",e.stakeCurrencyDecimals)} (Estimated value) `:""),w=[{value:"market",text:"Market"},{value:"limit",text:"Limit"}];return(h,d)=>{const E=Be,B=_e,g=Pe,u=Y,F=ie;return y(),z(F,{visible:a.value,"onUpdate:visible":d[4]||(d[4]=D=>a.value=D),header:"Force exiting a trade",modal:"",onShow:m,onHide:m},{footer:T(()=>[k("div",Da,[x(u,{severity:"secondary",size:"small",onClick:d[3]||(d[3]=D=>a.value=!1)},{default:T(()=>d[7]||(d[7]=[W("Cancel")])),_:1,__:[7]}),x(u,{severity:"primary",size:"small",onClick:c},{default:T(()=>d[8]||(d[8]=[W("Exit Position")])),_:1,__:[8]})])]),default:T(()=>[k("form",{ref_key:"form",ref:i,class:"space-y-4 md:min-w-[32rem]",onSubmit:Se(f,["prevent"])},[k("div",Ta,[k("p",Ea,[k("span",null,"Exiting Trade #"+I(t.trade.trade_id)+" "+I(t.trade.pair)+".",1),d[5]||(d[5]=k("br",null,null,-1)),k("span",null,"Currently owning "+I(t.trade.amount)+" "+I(t.trade.base_currency),1)])]),k("div",null,[k("label",Ba,[W(" Amount in "+I(t.trade.base_currency)+" [optional] ",1),k("span",Pa,I(p.value),1)]),k("div",Ca,[x(E,{id:"stake-input",modelValue:r.value,"onUpdate:modelValue":d[0]||(d[0]=D=>r.value=D),min:0,max:t.trade.amount,"use-grouping":!1,step:1e-6,"max-fraction-digits":8,class:"w-full","show-buttons":""},null,8,["modelValue","max"]),x(B,{modelValue:r.value,"onUpdate:modelValue":d[1]||(d[1]=D=>r.value=D),min:0,max:t.trade.amount,step:1e-6,class:"w-full"},null,8,["modelValue","max"])])]),k("div",null,[d[6]||(d[6]=k("label",{class:"block font-medium mb-1"},"*OrderType",-1)),x(g,{modelValue:o.value,"onUpdate:modelValue":d[2]||(d[2]=D=>o.value=D),options:w,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])])],544)]),_:1},8,["visible"])}}}),_a={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Aa(t,e){return y(),C("svg",_a,e[0]||(e[0]=[k("path",{fill:"currentColor",d:"M2 12c0 5 4 9 9 9c2.4 0 4.7-.9 6.4-2.6l-1.5-1.5c-1.3 1.4-3 2.1-4.9 2.1c-6.2 0-9.4-7.5-4.9-11.9S18 5.8 18 12h-3l4 4h.1l3.9-4h-3c0-5-4-9-9-9s-9 4-9 9m8 3h2v2h-2zm0-8h2v6h-2z"},null,-1)]))}const Ma=Q({name:"mdi-reload-alert",render:Aa}),$a={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ia(t,e){return y(),C("svg",$a,e[0]||(e[0]=[k("path",{fill:"currentColor",d:"M18 11h-3v3h-2v-3h-3V9h3V6h2v3h3m2-5v12H8V4zm0-2H8c-1.1 0-2 .9-2 2v12a2 2 0 0 0 2 2h12c1.11 0 2-.89 2-2V4a2 2 0 0 0-2-2M4 6H2v14a2 2 0 0 0 2 2h14v-2H4z"},null,-1)]))}const Fa=Q({name:"mdi-plus-box-multiple-outline",render:Ia}),Oa={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function za(t,e){return y(),C("svg",Oa,e[0]||(e[0]=[k("path",{fill:"currentColor",d:"M4 20h14v2H4a2 2 0 0 1-2-2V6h2zM20.22 2H7.78C6.8 2 6 2.8 6 3.78v12.44C6 17.2 6.8 18 7.78 18h12.44c.98 0 1.78-.8 1.78-1.78V3.78C22 2.8 21.2 2 20.22 2M19 13.6L17.6 15L14 11.4L10.4 15L9 13.6l3.6-3.6L9 6.4L10.4 5L14 8.6L17.6 5L19 6.4L15.4 10z"},null,-1)]))}const Ha=Q({name:"mdi-close-box-multiple",render:za}),Na={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ra(t,e){return y(),C("svg",Na,e[0]||(e[0]=[k("path",{fill:"currentColor",d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-3.4 14L12 13.4L8.4 17L7 15.6l3.6-3.6L7 8.4L8.4 7l3.6 3.6L15.6 7L17 8.4L13.4 12l3.6 3.6z"},null,-1)]))}const Wa=Q({name:"mdi-close-box",render:Ra}),Ua={class:"flex flex-col gap-1"},ja=X({__name:"TradeActions",props:{botApiVersion:{type:Number,default:1},trade:{type:Object,required:!0},enableForceEntry:{type:Boolean,default:!1}},emits:["forceExit","forceExitPartial","cancelOpenOrder","reloadTrade","deleteTrade","forceEntry"],setup(t){return(e,a)=>{const n=Wa,i=Y,r=Ha,o=Ee,l=Fa,f=Ma,m=kt;return y(),C("div",Ua,[t.botApiVersion<=1.1?(y(),z(i,{key:0,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit",label:"Forceexit",onClick:a[0]||(a[0]=c=>e.$emit("forceExit",t.trade))},{icon:T(()=>[x(n)]),_:1})):H("",!0),t.botApiVersion>1.1?(y(),z(i,{key:1,size:"small",class:"justify-start!",severity:"secondary",title:"Forceexit limit",label:"Forceexit limit",onClick:a[1]||(a[1]=c=>e.$emit("forceExit",t.trade,"limit"))},{icon:T(()=>[x(n)]),_:1})):H("",!0),t.botApiVersion>1.1?(y(),z(i,{key:2,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit market",label:"Forceexit market",onClick:a[2]||(a[2]=c=>e.$emit("forceExit",t.trade,"market"))},{icon:T(()=>[x(n)]),_:1})):H("",!0),t.botApiVersion>2.16?(y(),z(i,{key:3,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit partial",label:"Forceexit partial",onClick:a[3]||(a[3]=c=>e.$emit("forceExitPartial",t.trade))},{icon:T(()=>[x(r)]),_:1})):H("",!0),t.botApiVersion>=2.24&&(t.trade.open_order_id||t.trade.has_open_orders)?(y(),z(i,{key:4,class:"justify-start!",size:"small",severity:"secondary",title:"Cancel open orders",label:"Cancel open orders",onClick:a[4]||(a[4]=c=>e.$emit("cancelOpenOrder",t.trade))},{icon:T(()=>[x(o)]),_:1})):H("",!0),t.enableForceEntry?(y(),z(i,{key:5,class:"justify-start!",size:"small",severity:"secondary",title:"Increase position",label:"Increase position",onClick:a[5]||(a[5]=c=>e.$emit("forceEntry",t.trade))},{icon:T(()=>[x(l)]),_:1})):H("",!0),t.botApiVersion>=2.28?(y(),z(i,{key:6,class:"justify-start!",size:"small",severity:"secondary",title:"Reload",label:"Reload",onClick:a[6]||(a[6]=c=>e.$emit("reloadTrade",t.trade))},{icon:T(()=>[x(f)]),_:1})):H("",!0),x(i,{class:"justify-start!",size:"small",severity:"secondary",title:"Delete trade",label:"Delete trade",onClick:a[7]||(a[7]=c=>e.$emit("deleteTrade",t.trade))},{icon:T(()=>[x(m)]),_:1})])}}}),Ka={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function qa(t,e){return y(),C("svg",Ka,e[0]||(e[0]=[k("path",{fill:"currentColor",d:"M10 9a1 1 0 0 1 1-1a1 1 0 0 1 1 1v4.47l1.21.13l4.94 2.19c.53.24.85.77.85 1.35v4.36c-.03.82-.68 1.47-1.5 1.5H11c-.38 0-.74-.15-1-.43l-4.9-4.2l.74-.77c.19-.21.46-.32.74-.32h.22L10 19zm1-4a4 4 0 0 1 4 4c0 1.5-.8 2.77-2 3.46v-1.22c.61-.55 1-1.35 1-2.24a3 3 0 0 0-3-3a3 3 0 0 0-3 3c0 .89.39 1.69 1 2.24v1.22C7.8 11.77 7 10.5 7 9a4 4 0 0 1 4-4"},null,-1)]))}const Ga=Q({name:"mdi-gesture-tap",render:qa}),Xa=X({__name:"TradeActionsPopover",props:{trade:{type:Object,required:!0},id:{type:Number,required:!0},botApiVersion:{type:Number,required:!0},enableForceEntry:{type:Boolean,default:!1}},emits:["forceExit","forceExitPartial","cancelOpenOrder","reloadTrade","deleteTrade","forceEntry"],setup(t,{emit:e}){const a=e,n=V(!1);function i(s,p=void 0){n.value=!1,a("forceExit",s,p)}function r(s){n.value=!1,a("forceExitPartial",s)}function o(s){n.value=!1,a("cancelOpenOrder",s)}function l(s){n.value=!1,a("reloadTrade",s)}function f(s){n.value=!1,a("deleteTrade",s)}function m(s){n.value=!1,a("forceEntry",s)}const c=V(null);return(s,p)=>{var B;const w=Ga,h=Y,d=ja,E=Ee;return y(),C("div",null,[x(h,{id:`btn-actions-${t.id}`,class:"btn-xs",size:"small",severity:"secondary",title:"Actions",onClick:(B=b(c))==null?void 0:B.toggle},{default:T(()=>[x(w)]),_:1},8,["id","onClick"]),x(b(Lt),{ref_key:"popover",ref:c,target:`btn-actions-${t.id}`,title:`Actions for ${t.trade.pair}`,triggers:"manual",placement:"left"},{default:T(()=>{var g;return[x(d,{trade:t.trade,"bot-api-version":t.botApiVersion,"enable-force-entry":t.enableForceEntry,onForceExit:i,onForceExitPartial:r,onDeleteTrade:p[0]||(p[0]=u=>f(t.trade)),onCancelOpenOrder:o,onReloadTrade:l,onForceEntry:m},null,8,["trade","bot-api-version","enable-force-entry"]),x(h,{class:"mt-1 w-full text-start",size:"small",severity:"secondary",label:"Close Actions menu",onClick:(g=b(c))==null?void 0:g.hide},{icon:T(()=>[x(E,{class:"me-1"})]),_:1},8,["onClick"])]}),_:1},8,["target","title"])])}}}),Ya={class:"h-full overflow-auto w-full"},Qa={class:"flex justify-end gap-2 p-2"},ur=X({__name:"TradeList",props:{trades:{},title:{default:"Trades"},stakeCurrency:{default:""},activeTrades:{type:Boolean,default:!1},showFilter:{type:Boolean,default:!1},multiBotView:{type:Boolean,default:!1},emptyText:{default:"No Trades to show."}},setup(t){const e=t,a=ne(),n=Et(),i=wt(),r=V(1),o=V(),l=V(""),f=V({}),m=e.activeTrades?200:15,c=V(),s=V(!1),p=V(!1),w=V(""),h=V(null),d=V({visible:!1,trade:{}});function E(v){return ce(v,a.activeBot.stakeCurrencyDecimals)}const B=V([{field:"trade_id",header:"ID"},{field:"pair",header:"Pair"},{field:"amount",header:"Amount"},e.activeTrades?{field:"stake_amount",header:"Stake amount"}:{field:"max_stake_amount",header:"Total stake amount"},{field:"open_rate",header:"Open rate"},{field:e.activeTrades?"current_rate":"close_rate",header:e.activeTrades?"Current rate":"Close rate"},{field:"profit",header:e.activeTrades?"Current profit %":"Profit %"},{field:"open_timestamp",header:"Open date"},...e.activeTrades?[{field:"actions",header:""}]:[{field:"close_timestamp",header:"Close date"},{field:"exit_reason",header:"Close Reason"}]]);e.multiBotView&&B.value.unshift({field:"botName",header:"Bot"});const g=V(void 0);function u(v,P=void 0){f.value=v,h.value=1,w.value=`Really exit trade ${v.trade_id} (Pair ${v.pair}) using ${P} Order?`,g.value=P,i.confirmDialog===!0?p.value=!0:F()}function F(){if(h.value===0){const v={tradeid:String(f.value.trade_id),botId:f.value.botId};a.deleteTradeMulti(v).catch(P=>console.log(P.response))}if(h.value===1){const v={tradeid:String(f.value.trade_id),botId:f.value.botId};g.value&&(v.ordertype=g.value),a.forceSellMulti(v).then(P=>console.log(P)).catch(P=>console.log(P.response))}if(h.value===3){const v={tradeid:String(f.value.trade_id),botId:f.value.botId};a.cancelOpenOrderMulti(v)}g.value=void 0,p.value=!1}function D(v){w.value=`Really delete trade ${v.trade_id} (Pair ${v.pair})?`,h.value=0,f.value=v,p.value=!0}function $(v){f.value=v,s.value=!0}function O(v){w.value=`Cancel open order for trade ${v.trade_id} (Pair ${v.pair})?`,f.value=v,h.value=3,p.value=!0}function N(v){a.reloadTradeMulti({tradeid:String(v.trade_id),botId:v.botId})}function S(v){d.value.trade=v,d.value.visible=!0}const U=({data:v})=>{e.multiBotView&&a.selectedBot!==v.botId&&a.selectBot(v.botId),v&&v.trade_id!==a.activeBot.detailTradeId?(a.activeBot.setDetailTrade(v),e.multiBotView&&n.push({name:"Freqtrade Trading"})):a.activeBot.setDetailTrade(null)};return St(()=>a.activeBot.detailTradeId,v=>{e.trades.findIndex(Z=>Z.trade_id===v)<0&&(o.value=void 0)}),(v,P)=>{var ue;const Z=Xa,Ae=Ct,le=Dt,Me=Pt,$e=Ve,Ie=Bt,Fe=La,Oe=ia,se=Y,ze=ie;return y(),C("div",Ya,[x(Ie,{ref_key:"tradesTable",ref:c,selection:b(o),"onUpdate:selection":P[1]||(P[1]=A=>R(o)?o.value=A:null),value:v.trades.filter(A=>{var L,M;return A.pair.toLowerCase().includes(b(l).toLowerCase())||((L=A.exit_reason)==null?void 0:L.toLowerCase().includes(b(l).toLowerCase()))||((M=A.enter_tag)==null?void 0:M.toLowerCase().includes(b(l).toLowerCase()))}),rows:b(m),paginator:!v.activeTrades,first:(b(r)-1)*b(m),"selection-mode":"single",class:"text-center",size:"small",scrollable:!0,"scroll-height":"flex",onRowClick:U},Vt({empty:T(()=>[W(I(v.emptyText),1)]),default:T(()=>[(y(!0),C(j,null,Tt(b(B),A=>(y(),z(Me,{key:A.field,field:A.field,header:A.header},{body:T(({data:L,field:M,index:He})=>[M==="trade_id"?(y(),C(j,{key:0},[W(I(L.trade_id)+" "+I(b(a).activeBot.botApiVersion>2&&L.trading_mode!=="spot"?(L.trade_id?"| ":"")+(L.is_short?"Short":"Long"):""),1)],64)):M==="pair"?(y(),C(j,{key:1},[W(I(`${L.pair}${L.open_order_id||L.has_open_orders?"*":""}`),1)],64)):M==="actions"?(y(),z(Z,{key:2,id:He,"enable-force-entry":b(a).activeBot.botState.force_entry_enable,trade:L,"bot-api-version":b(a).activeBot.botApiVersion,onDeleteTrade:Za=>D(L),onForceExit:u,onForceExitPartial:$,onCancelOpenOrder:O,onReloadTrade:N,onForceEntry:S},null,8,["id","enable-force-entry","trade","bot-api-version","onDeleteTrade"])):M==="stake_amount"||M==="max_stake_amount"?(y(),C(j,{key:3},[W(I(E(L[M]))+" "+I(L.trading_mode!=="spot"?`(${L.leverage}x)`:""),1)],64)):M==="open_rate"||M==="current_rate"||M==="close_rate"?(y(),C(j,{key:4},[W(I(("formatPrice"in v?v.formatPrice:b(ce))(L[M])),1)],64)):M==="profit"?(y(),z(Ae,{key:5,trade:L},null,8,["trade"])):M==="open_timestamp"?(y(),z(le,{key:6,date:L.open_timestamp},null,8,["date"])):M==="close_timestamp"?(y(),z(le,{key:7,date:L.close_timestamp??0},null,8,["date"])):(y(),C(j,{key:8},[W(I(L[M]),1)],64))]),_:2},1032,["field","header"]))),128))]),_:2},[v.showFilter?{name:"header",fn:T(()=>[k("div",Qa,[x($e,{modelValue:b(l),"onUpdate:modelValue":P[0]||(P[0]=A=>R(l)?l.value=A:null),placeholder:"Filter",class:"w-64",size:"small"},null,8,["modelValue"])])]),key:"0"}:void 0]),1032,["selection","value","rows","paginator","first"]),v.activeTrades?(y(),z(Fe,{key:0,modelValue:b(s),"onUpdate:modelValue":P[2]||(P[2]=A=>R(s)?s.value=A:null),trade:b(f),"stake-currency-decimals":b(a).activeBot.botState.stake_currency_decimals??3},null,8,["modelValue","trade","stake-currency-decimals"])):H("",!0),x(Oe,{modelValue:b(d).visible,"onUpdate:modelValue":P[3]||(P[3]=A=>b(d).visible=A),pair:(ue=b(d).trade)==null?void 0:ue.pair,"position-increase":""},null,8,["modelValue","pair"]),x(ze,{visible:b(p),"onUpdate:visible":P[5]||(P[5]=A=>R(p)?p.value=A:null),modal:!0,header:"Exit trade"},{footer:T(()=>[x(se,{label:"Cancel",onClick:P[4]||(P[4]=A=>p.value=!1)}),x(se,{label:"Confirm",severity:"danger",onClick:F})]),default:T(()=>[k("p",null,I(b(w)),1)]),_:1},8,["visible"])])}}});export{Ha as _,Fa as a,ia as b,ur as c,lr as d,sr as i};
//# sourceMappingURL=TradeList.vue_vue_type_script_setup_true_lang--83SPrIm.js.map
