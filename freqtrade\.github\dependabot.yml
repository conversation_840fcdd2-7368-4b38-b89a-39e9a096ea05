version: 2
updates:
- package-ecosystem: docker
  directories:
    - "/"
    - "/docker"
  schedule:
    interval: daily
  ignore:
  - dependency-name: "*"
    update-types: ["version-update:semver-major"]
  open-pull-requests-limit: 10

- package-ecosystem: pip
  directory: "/"
  schedule:
    interval: weekly
    time: "03:00"
    timezone: "Etc/UTC"
  open-pull-requests-limit: 15
  target-branch: develop
  groups:
    types:
      patterns:
        - "types-*"
    pytest:
      patterns:
        - "pytest*"
    mkdocs:
      patterns:
        - "mkdocs*"

- package-ecosystem: "github-actions"
  directory: "/"
  schedule:
    interval: "weekly"
  open-pull-requests-limit: 10
  target-branch: develop
