
@property
def plot_config(self):
    return {
        # Main plot indicators (Moving averages, ...)
        "main_plot": {
            "tema": {},
            "sar": {"color": "white"},
        },
        "subplots": {
            # Subplots - each dict defines one additional plot
            "MACD": {
                "macd": {"color": "blue"},
                "macdsignal": {"color": "orange"},
            },
            "RSI": {
                "rsi": {"color": "red"},
            }
        }
    }
