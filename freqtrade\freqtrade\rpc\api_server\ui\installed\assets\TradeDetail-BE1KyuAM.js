import{H as S,c as P,a as r,e as u,d as q,n as b,l,k as o,z as a,a2 as B,a$ as D,b as y,h as s,x as n,f as i,A as v,E as h,D as f,G as k,F as w,m as F,bP as R,t as L}from"./index-Cwqm8wBn.js";import{c as I,a as M,_ as O}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";const N={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function E(e,g){return r(),P("svg",N,g[0]||(g[0]=[u("path",{fill:"currentColor",d:"M1 3h22L12 22"},null,-1)]))}const j=S({name:"mdi-triangle-down",render:E}),A={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function G(e,g){return r(),P("svg",A,g[0]||(g[0]=[u("path",{fill:"currentColor",d:"M1 21h22L12 2"},null,-1)]))}const H=S({name:"mdi-triangle",render:G}),U={class:"flex w-full"},Z=q({__name:"ValuePair",props:{description:{type:String,required:!0},help:{type:String,default:"",required:!1},classLabel:{type:String,default:"w-4/12 font-bold mb-0"},classValue:{type:String,default:"w-8/12"}},setup(e){return(g,t)=>{const c=I;return r(),P("div",U,[u("div",{class:b(["flex justify-between me-2",e.classLabel])},[u("label",null,a(e.description),1),e.help?(r(),l(c,{key:0,hint:e.help},null,8,["hint"])):o("",!0)],2),u("div",{class:b(e.classValue)},[B(g.$slots,"default")],2)])}}}),J={class:"text-start grid grid-cols-1 lg:grid-cols-2 gap-4 px-2"},K={class:""},Q={key:0},W={class:"mt-2 lg:mt-0"},X={key:3},Y={key:4},_=["title"],x={key:3,title:"remaining"},p={title:"Filled"},ee=q({__name:"TradeDetail",props:{trade:{required:!0,type:Object},stakeCurrency:{required:!0,type:String}},setup(e){R(t=>({a2f9fe70:i(g).colorUp,"19be3f8f":i(g).colorDown}));const g=D();return(t,c)=>{const d=Z,C=M,V=H,z=j,T=O;return r(),P("div",J,[u("div",K,[c[1]||(c[1]=u("h5",{class:"detail-header"},"General",-1)),y(d,{description:"Trade Id"},{default:s(()=>[n(a(e.trade.trade_id),1)]),_:1}),y(d,{description:"Pair"},{default:s(()=>[n(a(e.trade.pair),1)]),_:1}),y(d,{description:"Open date"},{default:s(()=>[n(a(("timestampms"in t?t.timestampms:i(v))(e.trade.open_timestamp)),1)]),_:1}),e.trade.enter_tag?(r(),l(d,{key:0,description:"Entry tag"},{default:s(()=>[n(a(e.trade.enter_tag),1)]),_:1})):o("",!0),e.trade.is_open?(r(),l(d,{key:1,description:"Stake"},{default:s(()=>[n(a(("formatPriceCurrency"in t?t.formatPriceCurrency:i(h))(e.trade.stake_amount,e.stakeCurrency))+" "+a(e.trade.leverage&&e.trade.leverage!==1?`(${e.trade.leverage}x)`:""),1)]),_:1})):o("",!0),e.trade.is_open?o("",!0):(r(),l(d,{key:2,description:"Total Stake"},{default:s(()=>[n(a(("formatPriceCurrency"in t?t.formatPriceCurrency:i(h))(e.trade.max_stake_amount??e.trade.stake_amount,e.stakeCurrency))+" "+a(e.trade.leverage&&e.trade.leverage!==1?`(${e.trade.leverage}x)`:""),1)]),_:1})),y(d,{description:"Amount"},{default:s(()=>[n(a(e.trade.amount),1)]),_:1}),y(d,{description:"Open Rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.open_rate)),1)]),_:1}),e.trade.is_open&&e.trade.current_rate?(r(),l(d,{key:3,description:"Current Rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.current_rate)),1)]),_:1})):o("",!0),!e.trade.is_open&&e.trade.close_rate?(r(),l(d,{key:4,description:"Close Rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.close_rate)),1)]),_:1})):o("",!0),e.trade.close_timestamp?(r(),l(d,{key:5,description:"Close date"},{default:s(()=>[n(a(("timestampms"in t?t.timestampms:i(v))(e.trade.close_timestamp)),1)]),_:1})):o("",!0),e.trade.is_open&&e.trade.realized_profit&&!e.trade.total_profit_abs?(r(),l(d,{key:6,description:"Realized Profit"},{default:s(()=>[y(C,{class:"ms-2",trade:e.trade,mode:"realized"},null,8,["trade"])]),_:1})):o("",!0),e.trade.is_open&&e.trade.total_profit_abs?(r(),l(d,{key:7,description:"Total Profit"},{default:s(()=>[y(C,{class:"ms-2",trade:e.trade,mode:"total"},null,8,["trade"])]),_:1})):o("",!0),e.trade.profit_ratio&&e.trade.profit_abs?(r(),l(d,{key:8,description:`${e.trade.is_open?"Current Profit":"Close Profit"}`},{default:s(()=>[y(C,{class:"ms-2",trade:e.trade},null,8,["trade"])]),_:1},8,["description"])):o("",!0),u("details",null,[c[0]||(c[0]=u("summary",null,"Details",-1)),e.trade.min_rate?(r(),l(d,{key:0,description:"Min Rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.min_rate)),1)]),_:1})):o("",!0),e.trade.max_rate?(r(),l(d,{key:1,description:"Max Rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.max_rate)),1)]),_:1})):o("",!0),y(d,{description:"Open-Fees"},{default:s(()=>[n(a(e.trade.fee_open_cost)+" "+a(e.trade.quote_currency)+" ",1),e.trade.quote_currency!==e.trade.fee_open_currency?(r(),P("span",Q," (in "+a(e.trade.fee_open_currency)+") ",1)):o("",!0),n(" ("+a(("formatPercent"in t?t.formatPercent:i(k))(e.trade.fee_open))+") ",1)]),_:1}),e.trade.fee_close_cost&&e.trade.fee_close?(r(),l(d,{key:2,description:"Fees close"},{default:s(()=>[n(a(e.trade.fee_close_cost)+" "+a(e.trade.fee_close_currency)+" ("+a(("formatPercent"in t?t.formatPercent:i(k))(e.trade.fee_close))+") ",1)]),_:1})):o("",!0)])]),u("div",W,[c[5]||(c[5]=u("h5",{class:"detail-header"},"Stoploss",-1)),y(d,{description:"Stoploss"},{default:s(()=>[n(a(("formatPercent"in t?t.formatPercent:i(k))(e.trade.stop_loss_pct/100))+" | "+a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.stop_loss_abs)),1)]),_:1}),e.trade.initial_stop_loss_pct&&e.trade.initial_stop_loss_abs?(r(),l(d,{key:0,description:"Initial Stoploss"},{default:s(()=>[n(a(("formatPercent"in t?t.formatPercent:i(k))(e.trade.initial_stop_loss_pct/100))+" | "+a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.initial_stop_loss_abs)),1)]),_:1})):o("",!0),e.trade.is_open&&e.trade.stoploss_current_dist_ratio&&e.trade.stoploss_current_dist?(r(),l(d,{key:1,description:"Current stoploss dist"},{default:s(()=>[n(a(("formatPercent"in t?t.formatPercent:i(k))(e.trade.stoploss_current_dist_ratio))+" | "+a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.stoploss_current_dist)),1)]),_:1})):o("",!0),e.trade.stoploss_last_update_timestamp?(r(),l(d,{key:2,description:"Stoploss last updated"},{default:s(()=>[n(a(("timestampms"in t?t.timestampms:i(v))(e.trade.stoploss_last_update_timestamp)),1)]),_:1})):o("",!0),e.trade.trading_mode!==void 0&&e.trade.trading_mode!=="spot"?(r(),P("div",X,[c[2]||(c[2]=u("h5",{class:"detail-header"},"Futures/Margin",-1)),y(d,{description:"Direction"},{default:s(()=>[n(a(e.trade.is_short?"short":"long")+" - "+a(e.trade.leverage)+"x ",1)]),_:1}),e.trade.funding_fees!==void 0?(r(),l(d,{key:0,description:"Funding fees"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.funding_fees)),1)]),_:1})):o("",!0),e.trade.interest_rate!==void 0?(r(),l(d,{key:1,description:"Interest rate"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.interest_rate)),1)]),_:1})):o("",!0),e.trade.liquidation_price!==void 0?(r(),l(d,{key:2,description:"Liquidation Price"},{default:s(()=>[n(a(("formatPrice"in t?t.formatPrice:i(f))(e.trade.liquidation_price)),1)]),_:1})):o("",!0)])):o("",!0),e.trade.orders?(r(),P("details",Y,[u("summary",null,"Orders "+a(e.trade.orders.length>1?`[${e.trade.orders.length}]`:""),1),(r(!0),P(w,null,F(e.trade.orders,(m,$)=>(r(),P("div",{key:$,class:"flex items-center gap-1 2",title:`${m.ft_order_side} ${m.order_type} order for ${("formatPriceCurrency"in t?t.formatPriceCurrency:i(h))(m.amount,e.trade.base_currency??"")} at ${("formatPriceCurrency"in t?t.formatPriceCurrency:i(h))(m.safe_price,e.trade.quote_currency??"")}, filled ${("formatPrice"in t?t.formatPrice:i(f))(m.filled)}`},[n(" (#"+a($+1)+") ",1),m.ft_order_side==="buy"?(r(),l(V,{key:0,class:"me-1 color-up",style:{"font-size":"0.6rem"}})):(r(),l(z,{key:1,class:"me-1 color-down",style:{"font-size":"0.6rem"}})),m.order_timestamp?(r(),l(T,{key:2,date:m.order_timestamp,"show-timezone":""},null,8,["date"])):o("",!0),u("b",{class:b(["ms-1",m.ft_order_side==="buy"?"color-up":"color-down"])},a(m.ft_order_side),3),c[3]||(c[3]=n(" for ")),u("b",null,a(("formatPrice"in t?t.formatPrice:i(f))(m.safe_price)),1),c[4]||(c[4]=n(" | ")),m.remaining&&m.remaining!==0?(r(),P("span",x,a(("formatPrice"in t?t.formatPrice:i(f))(m.remaining,8))+" / ",1)):o("",!0),u("span",p,a(("formatPrice"in t?t.formatPrice:i(f))(m.filled??0,8)),1),m.ft_order_tag?(r(),P(w,{key:4},[n(" | "+a(m.ft_order_tag??""),1)],64)):o("",!0)],8,_))),128))])):o("",!0)])])}}}),re=L(ee,[["__scopeId","data-v-b0409db1"]]);export{re as _,Z as a};
//# sourceMappingURL=TradeDetail-BE1KyuAM.js.map
