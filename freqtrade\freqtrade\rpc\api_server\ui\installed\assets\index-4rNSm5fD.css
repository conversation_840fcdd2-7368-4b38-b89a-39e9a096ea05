#app[data-v-88008810]{font-family:Avenir,Helvetica,Arial,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-align:center}.vue-grid-item{box-sizing:border-box;touch-action:none;background-color:#f2f2f2;transition:all .2s ease;transition-property:left,top,right}.vue-grid-item.no-touch{touch-action:none}.vue-grid-item.css-transforms{right:auto;left:0;transition-property:transform}.vue-grid-item.resizing{z-index:3;opacity:.6}.vue-grid-item.vue-draggable-dragging{z-index:3;transition:none}.vue-grid-item.vue-grid-placeholder{z-index:2;-webkit-user-select:none;user-select:none;background:red;opacity:.2;transition-duration:.1s}.vue-grid-item>.vue-resizable-handle{position:absolute;right:0;bottom:0;z-index:20;box-sizing:border-box;width:20px;height:20px;padding:0 3px 3px 0;cursor:se-resize;background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pg08c3ZnIGlkPSJVbnRpdGxlZC1QYWdlJTIwMSIgdmlld0JveD0iMCAwIDYgNiIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6I2ZmZmZmZjAwIiB2ZXJzaW9uPSIxLjEiDQl4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4bWw6c3BhY2U9InByZXNlcnZlIg0JeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ig0+DQk8ZyBvcGFjaXR5PSIwLjMwMiI+DQkJPHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDQuMiBMIDQgNC4yIEwgNC4yIDQuMiBMIDQuMiAwIEwgNiAwIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz4NCTwvZz4NPC9zdmc+);background-repeat:no-repeat;background-position:bottom right;background-origin:content-box}.vue-grid-item.disable-user-select{-webkit-user-select:none;user-select:none}.vue-grid-layout{position:relative;transition:height .2s ease}/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-duration:initial}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-500:oklch(63.7% .237 25.331);--color-amber-500:oklch(76.9% .188 70.08);--color-yellow-300:oklch(90.5% .182 98.111);--color-yellow-500:oklch(79.5% .184 86.047);--color-green-500:oklch(72.3% .219 149.579);--color-green-800:oklch(44.8% .119 151.328);--color-emerald-500:oklch(69.6% .17 162.48);--color-blue-500:oklch(62.3% .214 259.815);--color-slate-200:oklch(92.9% .013 255.508);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-500:oklch(55.1% .027 264.364);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xl:36rem;--container-4xl:56rem;--container-7xl:80rem;--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height: 1.2 ;--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--radius-sm:.25rem;--radius-md:.375rem;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*,:after,:before,::backdrop{border-color:var(--color-gray-200,currentColor)}::file-selector-button{border-color:var(--color-gray-200,currentColor)}}@layer components;@layer utilities{.collapse{visibility:collapse}.invisible{visibility:hidden}.visible{visibility:visible}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.top-2{top:calc(var(--spacing)*2)}.right-2{right:calc(var(--spacing)*2)}.z-2{z-index:2}.col-6{grid-column:6}.col-auto{grid-column:auto}.col-span-2{grid-column:span 2/span 2}.col-span-3{grid-column:span 3/span 3}.float-end{float:inline-end}.float-right{float:right}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.m-0{margin:calc(var(--spacing)*0)}.m-1{margin:calc(var(--spacing)*1)}.m-2{margin:calc(var(--spacing)*2)}.m-auto{margin:auto}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-inline:auto}.my-0{margin-block:calc(var(--spacing)*0)}.my-0\.5{margin-block:calc(var(--spacing)*.5)}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-5{margin-block:calc(var(--spacing)*5)}.my-auto{margin-block:auto}.ms-0{margin-inline-start:calc(var(--spacing)*0)}.ms-1{margin-inline-start:calc(var(--spacing)*1)}.ms-2{margin-inline-start:calc(var(--spacing)*2)}.ms-3{margin-inline-start:calc(var(--spacing)*3)}.ms-5{margin-inline-start:calc(var(--spacing)*5)}.ms-auto{margin-inline-start:auto}.me-0{margin-inline-end:calc(var(--spacing)*0)}.me-1{margin-inline-end:calc(var(--spacing)*1)}.me-2{margin-inline-end:calc(var(--spacing)*2)}.me-3{margin-inline-end:calc(var(--spacing)*3)}.me-4{margin-inline-end:calc(var(--spacing)*4)}.me-5{margin-inline-end:calc(var(--spacing)*5)}.me-auto{margin-inline-end:auto}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-5{margin-top:calc(var(--spacing)*5)}.mt-auto{margin-top:auto}.mr-2{margin-right:calc(var(--spacing)*2)}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.-ml-2{margin-left:calc(var(--spacing)*-2)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.h-5{height:calc(var(--spacing)*5)}.h-8{height:calc(var(--spacing)*8)}.h-30{height:calc(var(--spacing)*30)}.h-\[30px\]{height:30px}.h-\[150px\]{height:150px}.h-dvh{height:100dvh}.h-full{height:100%}.min-h-\[250px\]{min-height:250px}.w-2\/5{width:40%}.w-4\/12{width:33.3333%}.w-5{width:calc(var(--spacing)*5)}.w-5\/12{width:41.6667%}.w-6\/12{width:50%}.w-7\/12{width:58.3333%}.w-8{width:calc(var(--spacing)*8)}.w-8\/12{width:66.6667%}.w-16{width:calc(var(--spacing)*16)}.w-25{width:calc(var(--spacing)*25)}.w-56{width:calc(var(--spacing)*56)}.w-64{width:calc(var(--spacing)*64)}.w-96\!{width:calc(var(--spacing)*96)!important}.w-\[150px\]{width:150px}.w-\[1000px\]{width:1000px}.w-full{width:100%}.max-w-4xl{max-width:var(--container-4xl)}.max-w-\[500px\]{max-width:500px}.max-w-xl{max-width:var(--container-xl)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-30{min-width:calc(var(--spacing)*30)}.min-w-44{min-width:calc(var(--spacing)*44)}.min-w-52{min-width:calc(var(--spacing)*52)}.min-w-56{min-width:calc(var(--spacing)*56)}.flex-shrink-1,.shrink{flex-shrink:1}.flex-grow-1,.grow{flex-grow:1}.basis-full{flex-basis:100%}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.cursor-pointer{cursor:pointer}.resize{resize:both}.list-inside{list-style-position:inside}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-row{flex-direction:row}.flex-nowrap{flex-wrap:nowrap}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start\!{justify-content:flex-start!important}.gap-1{gap:calc(var(--spacing)*1)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}.gap-10{gap:calc(var(--spacing)*10)}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}.gap-y-2{row-gap:calc(var(--spacing)*2)}:where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.divide-solid>:not(:last-child)){--tw-border-style:solid;border-style:solid}:where(.divide-surface-300>:not(:last-child)){border-color:var(--p-surface-300)}@supports (color:color-mix(in lab,red,red)){:where(.divide-surface-300>:not(:last-child)){border-color:color-mix(in srgb,var(--p-surface-300) 100%,transparent)}}:where(.divide-surface-500>:not(:last-child)){border-color:var(--p-surface-500)}@supports (color:color-mix(in lab,red,red)){:where(.divide-surface-500>:not(:last-child)){border-color:color-mix(in srgb,var(--p-surface-500) 100%,transparent)}}.self-start{align-self:flex-start}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-hidden{overflow-x:hidden}.overflow-x-visible{overflow-x:visible}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.border{border-style:var(--tw-border-style);border-width:1px}.border-x{border-inline-style:var(--tw-border-style);border-inline-width:1px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-primary{border-color:var(--p-primary-color)}@supports (color:color-mix(in lab,red,red)){.border-primary{border-color:color-mix(in srgb,var(--p-primary-color) 100%,transparent)}}.border-surface-200{border-color:var(--p-surface-200)}@supports (color:color-mix(in lab,red,red)){.border-surface-200{border-color:color-mix(in srgb,var(--p-surface-200) 100%,transparent)}}.border-surface-300{border-color:var(--p-surface-300)}@supports (color:color-mix(in lab,red,red)){.border-surface-300{border-color:color-mix(in srgb,var(--p-surface-300) 100%,transparent)}}.border-surface-400{border-color:var(--p-surface-400)}@supports (color:color-mix(in lab,red,red)){.border-surface-400{border-color:color-mix(in srgb,var(--p-surface-400) 100%,transparent)}}.border-surface-500{border-color:var(--p-surface-500)}@supports (color:color-mix(in lab,red,red)){.border-surface-500{border-color:color-mix(in srgb,var(--p-surface-500) 100%,transparent)}}.border-white{border-color:var(--color-white)}.bg-amber-500{background-color:var(--color-amber-500)}.bg-black{background-color:var(--color-black)}.bg-emerald-500{background-color:var(--color-emerald-500)}.bg-green-800{background-color:var(--color-green-800)}.bg-primary{background-color:var(--p-primary-color)}@supports (color:color-mix(in lab,red,red)){.bg-primary{background-color:color-mix(in srgb,var(--p-primary-color) 100%,transparent)}}.bg-primary-100{background-color:var(--p-primary-100)}@supports (color:color-mix(in lab,red,red)){.bg-primary-100{background-color:color-mix(in srgb,var(--p-primary-100) 100%,transparent)}}.bg-primary-500{background-color:var(--p-primary-500)}@supports (color:color-mix(in lab,red,red)){.bg-primary-500{background-color:color-mix(in srgb,var(--p-primary-500) 100%,transparent)}}.bg-surface-50{background-color:var(--p-surface-50)}@supports (color:color-mix(in lab,red,red)){.bg-surface-50{background-color:color-mix(in srgb,var(--p-surface-50) 100%,transparent)}}.bg-surface-100{background-color:var(--p-surface-100)}@supports (color:color-mix(in lab,red,red)){.bg-surface-100{background-color:color-mix(in srgb,var(--p-surface-100) 100%,transparent)}}.bg-surface-200{background-color:var(--p-surface-200)}@supports (color:color-mix(in lab,red,red)){.bg-surface-200{background-color:color-mix(in srgb,var(--p-surface-200) 100%,transparent)}}.bg-white{background-color:var(--color-white)}.p-0{padding:calc(var(--spacing)*0)}.p-0\!{padding:calc(var(--spacing)*0)!important}.p-1{padding:calc(var(--spacing)*1)}.p-2{padding:calc(var(--spacing)*2)}.p-4{padding:calc(var(--spacing)*4)}.px-0{padding-inline:calc(var(--spacing)*0)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-5{padding-inline:calc(var(--spacing)*5)}.py-0{padding-block:calc(var(--spacing)*0)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.ps-1{padding-inline-start:calc(var(--spacing)*1)}.ps-2{padding-inline-start:calc(var(--spacing)*2)}.pe-1{padding-inline-end:calc(var(--spacing)*1)}.pe-2{padding-inline-end:calc(var(--spacing)*2)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pb-1{padding-bottom:calc(var(--spacing)*1)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-5{padding-bottom:calc(var(--spacing)*5)}.text-center{text-align:center}.text-end{text-align:end}.text-left{text-align:left}.text-start{text-align:start}.align-middle{vertical-align:middle}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.text-nowrap{text-wrap:nowrap}.text-ellipsis{text-overflow:ellipsis}.whitespace-pre-line{white-space:pre-line}.text-black{color:var(--color-black)}.text-blue-500{color:var(--color-blue-500)}.text-gray-500{color:var(--color-gray-500)}.text-green-500{color:var(--color-green-500)}.text-muted-color{color:var(--p-text-muted-color)}.text-primary{color:var(--p-primary-color)}@supports (color:color-mix(in lab,red,red)){.text-primary{color:color-mix(in srgb,var(--p-primary-color) 100%,transparent)}}.text-primary-contrast{color:var(--p-primary-contrast-color)}@supports (color:color-mix(in lab,red,red)){.text-primary-contrast{color:color-mix(in srgb,var(--p-primary-contrast-color) 100%,transparent)}}.text-red-500{color:var(--color-red-500)}.text-slate-200{color:var(--color-slate-200)}.text-surface-200{color:var(--p-surface-200)}@supports (color:color-mix(in lab,red,red)){.text-surface-200{color:color-mix(in srgb,var(--p-surface-200) 100%,transparent)}}.text-surface-300{color:var(--p-surface-300)}@supports (color:color-mix(in lab,red,red)){.text-surface-300{color:color-mix(in srgb,var(--p-surface-300) 100%,transparent)}}.text-surface-500{color:var(--p-surface-500)}@supports (color:color-mix(in lab,red,red)){.text-surface-500{color:color-mix(in srgb,var(--p-surface-500) 100%,transparent)}}.text-surface-600{color:var(--p-surface-600)}@supports (color:color-mix(in lab,red,red)){.text-surface-600{color:color-mix(in srgb,var(--p-surface-600) 100%,transparent)}}.text-white{color:var(--color-white)}.text-yellow-300{color:var(--color-yellow-300)}.text-yellow-500{color:var(--color-yellow-500)}.italic{font-style:italic}.underline{text-decoration-line:underline}.opacity-0{opacity:0}.opacity-100{opacity:1}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.outline-primary{outline-color:var(--p-primary-color)}@supports (color:color-mix(in lab,red,red)){.outline-primary{outline-color:color-mix(in srgb,var(--p-primary-color) 100%,transparent)}}.invert{--tw-invert:invert(100%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-300{--tw-duration:.3s;transition-duration:.3s}@media (hover:hover){.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}}.last\:rounded-b:last-child{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}@media (hover:hover){.hover\:cursor-default:hover{cursor:default}.hover\:cursor-grab:hover{cursor:grab}.hover\:cursor-pointer:hover{cursor:pointer}}@media (min-width:40rem){.sm\:h-\[250px\]{height:250px}.sm\:w-\[250px\]{width:250px}.sm\:flex-row{flex-direction:row}.sm\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}}@media (min-width:48rem){.md\:col-span-2{grid-column:span 2/span 2}.md\:mx-2{margin-inline:calc(var(--spacing)*2)}.md\:mx-3{margin-inline:calc(var(--spacing)*3)}.md\:ms-2{margin-inline-start:calc(var(--spacing)*2)}.md\:me-1{margin-inline-end:calc(var(--spacing)*1)}.md\:flex{display:flex}.md\:hidden{display:none}.md\:min-w-\[32rem\]{min-width:32rem}.md\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.md\:flex-row{flex-direction:row}.md\:flex-nowrap{flex-wrap:nowrap}.md\:flex-wrap{flex-wrap:wrap}.md\:justify-center{justify-content:center}.md\:gap-2{gap:calc(var(--spacing)*2)}.md\:p-4{padding:calc(var(--spacing)*4)}.md\:pe-2{padding-inline-end:calc(var(--spacing)*2)}}@media (min-width:64rem){.lg\:mt-0{margin-top:calc(var(--spacing)*0)}.lg\:inline{display:inline}.lg\:w-3\/12{width:25%}.lg\:max-w-7xl{max-width:var(--container-7xl)}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:flex-nowrap{flex-wrap:nowrap}}@media (min-width:80rem){.xl\:flex-row{flex-direction:row}.xl\:px-0{padding-inline:calc(var(--spacing)*0)}.xl\:ps-1{padding-inline-start:calc(var(--spacing)*1)}.xl\:pt-0{padding-top:calc(var(--spacing)*0)}}:where(.dark\:divide-surface-700:where(.dark,.dark *)>:not(:last-child)){border-color:var(--p-surface-700)}@supports (color:color-mix(in lab,red,red)){:where(.dark\:divide-surface-700:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in srgb,var(--p-surface-700) 100%,transparent)}}.dark\:border-primary:where(.dark,.dark *){border-color:var(--p-primary-color)}@supports (color:color-mix(in lab,red,red)){.dark\:border-primary:where(.dark,.dark *){border-color:color-mix(in srgb,var(--p-primary-color) 100%,transparent)}}.dark\:border-surface-600:where(.dark,.dark *){border-color:var(--p-surface-600)}@supports (color:color-mix(in lab,red,red)){.dark\:border-surface-600:where(.dark,.dark *){border-color:color-mix(in srgb,var(--p-surface-600) 100%,transparent)}}.dark\:border-surface-700:where(.dark,.dark *){border-color:var(--p-surface-700)}@supports (color:color-mix(in lab,red,red)){.dark\:border-surface-700:where(.dark,.dark *){border-color:color-mix(in srgb,var(--p-surface-700) 100%,transparent)}}.dark\:border-surface-800:where(.dark,.dark *){border-color:var(--p-surface-800)}@supports (color:color-mix(in lab,red,red)){.dark\:border-surface-800:where(.dark,.dark *){border-color:color-mix(in srgb,var(--p-surface-800) 100%,transparent)}}.dark\:bg-\[\#010101\]:where(.dark,.dark *){background-color:#010101}.dark\:bg-primary-800:where(.dark,.dark *){background-color:var(--p-primary-800)}@supports (color:color-mix(in lab,red,red)){.dark\:bg-primary-800:where(.dark,.dark *){background-color:color-mix(in srgb,var(--p-primary-800) 100%,transparent)}}.dark\:bg-surface-700:where(.dark,.dark *){background-color:var(--p-surface-700)}@supports (color:color-mix(in lab,red,red)){.dark\:bg-surface-700:where(.dark,.dark *){background-color:color-mix(in srgb,var(--p-surface-700) 100%,transparent)}}.dark\:bg-surface-800:where(.dark,.dark *){background-color:var(--p-surface-800)}@supports (color:color-mix(in lab,red,red)){.dark\:bg-surface-800:where(.dark,.dark *){background-color:color-mix(in srgb,var(--p-surface-800) 100%,transparent)}}.dark\:bg-surface-900:where(.dark,.dark *){background-color:var(--p-surface-900)}@supports (color:color-mix(in lab,red,red)){.dark\:bg-surface-900:where(.dark,.dark *){background-color:color-mix(in srgb,var(--p-surface-900) 100%,transparent)}}.dark\:bg-white:where(.dark,.dark *){background-color:var(--color-white)}.dark\:text-surface-200:where(.dark,.dark *){color:var(--p-surface-200)}@supports (color:color-mix(in lab,red,red)){.dark\:text-surface-200:where(.dark,.dark *){color:color-mix(in srgb,var(--p-surface-200) 100%,transparent)}}.dark\:text-surface-300:where(.dark,.dark *){color:var(--p-surface-300)}@supports (color:color-mix(in lab,red,red)){.dark\:text-surface-300:where(.dark,.dark *){color:color-mix(in srgb,var(--p-surface-300) 100%,transparent)}}.dark\:text-surface-400:where(.dark,.dark *){color:var(--p-surface-400)}@supports (color:color-mix(in lab,red,red)){.dark\:text-surface-400:where(.dark,.dark *){color:color-mix(in srgb,var(--p-surface-400) 100%,transparent)}}.dark\:text-white:where(.dark,.dark *){color:var(--color-white)}}@layer frequi{:root{--p-primary-50:#dbfaff;--p-primary-100:#b8f4ff;--p-primary-200:#75eaff;--p-primary-300:#2ee0ff;--p-primary-400:#00c3e6;--p-primary-500:#0089a1;--p-primary-600:#006c80;--p-primary-700:#005261;--p-primary-800:#003842;--p-primary-900:#001a1f;--p-primary-950:#000d0f;--p-primary-color:var(--p-primary-600);--p-listbox-option-padding:.15rem .75rem;--p-button-secondary-background:var(--p-surface-200);--p-button-secondary-hover-background:var(--p-surface-300);--color-profit:#12bb7b;--color-loss:#ef5350;--p-tabs-tabpanel-padding:unset;--p-togglebutton-content-checked-background:var(--p-primary-500);--p-togglebutton-sm-padding:0;--p-selectbutton-border-radius:var(--radius-sm);--p-togglebutton-content-border-radius:0px}.ft-dark-theme{--p-content-background:var(--p-surface-950);--p-menu-background:var(--p-surface-900);--p-primary-contrast-color:var(--p-text-200);--p-button-secondary-background:var(--p-surface-700);--p-button-secondary-hover-background:var(--p-surface-600)}.p-button-icon-only.p-button-sm{--p-button-sm-icon-only-height:2.25rem;padding:.1rem .4rem}.p-button-icon-only{padding:unset;aspect-ratio:1}.p-selectbutton{border-color:var(--p-primary-500);border:1px solid var(--p-primary-500)}.p-selectbutton>.p-togglebutton{border:none}}@layer primevue{:root{--p-datatable-row-background:unset}}body.ft-theme-transition,body.ft-theme-transition *,body.ft-theme-transition :before,body.ft-theme-transition :after{transition:background .75s ease-in-out,border-color .75s ease-in-out,background-color .75s ease-in-out,all;transition-delay:0!important}html{font-size:14px}.vue-grid-item{background-color:unset!important}@keyframes enter{0%{opacity:var(--p-enter-opacity,1);transform:translate3d(var(--p-enter-translate-x,0),var(--p-enter-translate-y,0),0)scale3d(var(--p-enter-scale,1),var(--p-enter-scale,1),var(--p-enter-scale,1))rotate(var(--p-enter-rotate,0))}}@keyframes leave{to{opacity:var(--p-leave-opacity,1);transform:translate3d(var(--p-leave-translate-x,0),var(--p-leave-translate-y,0),0)scale3d(var(--p-leave-scale,1),var(--p-leave-scale,1),var(--p-leave-scale,1))rotate(var(--p-leave-rotate,0))}}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}
