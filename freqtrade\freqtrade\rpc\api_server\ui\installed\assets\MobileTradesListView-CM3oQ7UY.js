import{a as P,_ as F}from"./TradeDetail-BE1KyuAM.js";import{H as S,c as n,a as r,e as i,d as h,b as o,l as f,k as c,z as u,h as m,x as v,f as a,D as C,t as I,u as $,r as b,j as g,F as j,m as q,i as D,M as z,g as O}from"./index-Cwqm8wBn.js";import{s as R}from"./index-ULt6J10p.js";import{_ as E,a as M}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";import"./index-CONYmxgd.js";const A={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function H(t,e){return r(),n("svg",A,e[0]||(e[0]=[i("path",{fill:"currentColor",d:"M20 11v2H8l5.5 5.5l-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5L8 11z"},null,-1)]))}const U=S({name:"mdi-arrow-left",render:H}),Z={class:"flex items-center"},G={class:"px-1 flex w-7/12 flex-col text-start justify-between"},J={class:"me-1 font-bold"},K={class:"text-surface-500"},_="w-6/12 text-surface-500 text-sm",Q=h({__name:"CustomTradeListEntry",props:{trade:{type:Object,required:!0},stakeCurrencyDecimals:{type:Number,required:!0},showDetails:{type:Boolean,default:!1}},setup(t){return(e,k)=>{const s=P,l=E;return r(),n("div",Z,[i("div",G,[i("span",null,[i("span",J,u(t.trade.pair),1),i("small",K,"(#"+u(t.trade.trade_id)+")",1)]),o(s,{description:"Amount","class-label":_},{default:m(()=>[v(u(t.trade.amount),1)]),_:1}),o(s,{description:"Open Rate","class-label":_},{default:m(()=>[v(u(("formatPrice"in e?e.formatPrice:a(C))(t.trade.open_rate)),1)]),_:1}),t.trade.is_open&&t.trade.current_rate?(r(),f(s,{key:0,description:"Current Rate","class-label":_},{default:m(()=>[v(u(("formatPrice"in e?e.formatPrice:a(C))(t.trade.current_rate)),1)]),_:1})):c("",!0),o(s,{description:"Open date","class-label":_},{default:m(()=>[o(l,{date:t.trade.open_timestamp,"date-only":!0},null,8,["date"])]),_:1}),t.trade.close_timestamp?(r(),f(s,{key:1,description:"Close date","class-label":_},{default:m(()=>[o(l,{date:t.trade.close_timestamp,"date-only":!0},null,8,["date"])]),_:1})):c("",!0)]),o(M,{class:"w-5/12",trade:t.trade},null,8,["trade"])])}}}),W=I(Q,[["__scopeId","data-v-f820adf3"]]),X={class:"h-full overflow-auto p-1"},Y={id:"tradeList"},ee=["onClick"],te={key:0,class:"mt-5"},ae={class:"w-full flex justify-content-between mt-1"},se=h({__name:"CustomTradeList",props:{trades:{required:!0,type:Array},title:{default:"Trades",type:String},stakeCurrency:{required:!1,default:"",type:String},activeTrades:{default:!1,type:Boolean},showFilter:{default:!1,type:Boolean},multiBotView:{default:!1,type:Boolean},emptyText:{default:"No Trades to show.",type:String},stakeCurrencyDecimals:{default:3,type:Number}},setup(t){const e=t,k=$(),s=b(1),l=b(""),y=e.activeTrades?200:25,B=g(()=>e.trades.length),T=g(()=>e.trades.slice((s.value-1)*y,s.value*y)),x=w=>{k.activeBot.setDetailTrade(w)};return(w,p)=>{const V=W,L=R,N=z("BFormInput");return r(),n("div",X,[i("div",Y,[(r(!0),n(j,null,q(a(T),d=>(r(),n("div",{key:d.trade_id,class:"border border-surface-500 rounded-sm my-0.5 px-1 py-2",onClick:oe=>x(d)},[o(V,{trade:d,"stake-currency-decimals":t.stakeCurrencyDecimals},null,8,["trade","stake-currency-decimals"])],8,ee))),128))]),t.trades.length==0?(r(),n("span",te,u(t.emptyText),1)):c("",!0),i("div",ae,[t.activeTrades?c("",!0):(r(),f(L,{key:0,modelValue:a(s),"onUpdate:modelValue":p[0]||(p[0]=d=>D(s)?s.value=d:null),"total-records":a(B),rows:a(y),"aria-controls":"tradeList"},null,8,["modelValue","total-records","rows"])),t.showFilter?(r(),f(N,{key:1,modelValue:a(l),"onUpdate:modelValue":p[1]||(p[1]=d=>D(l)?l.value=d:null),type:"text",placeholder:"Filter",size:"sm",style:{width:"unset"}},null,8,["modelValue"])):c("",!0)])])}}}),re={key:2,class:"flex flex-col"},ue=h({__name:"MobileTradesListView",props:{history:{default:!1,type:Boolean}},setup(t){const e=$();return(k,s)=>{const l=se,y=U,B=O,T=F;return r(),n("div",null,[!t.history&&!a(e).activeBot.detailTradeId?(r(),f(l,{key:0,trades:a(e).activeBot.openTrades,title:"Open trades","active-trades":!0,"stake-currency-decimals":a(e).activeBot.stakeCurrencyDecimals,"empty-text":"No open Trades."},null,8,["trades","stake-currency-decimals"])):c("",!0),t.history&&!a(e).activeBot.detailTradeId?(r(),f(l,{key:1,trades:a(e).activeBot.closedTrades,title:"Trade history","stake-currency-decimals":a(e).activeBot.stakeCurrencyDecimals,"empty-text":"No closed trades so far."},null,8,["trades","stake-currency-decimals"])):c("",!0),a(e).activeBot.detailTradeId&&a(e).activeBot.tradeDetail?(r(),n("div",re,[o(B,{size:"small",severity:"secondary",class:"self-start my-1 ms-1",onClick:s[0]||(s[0]=x=>a(e).activeBot.setDetailTrade(null))},{default:m(()=>[o(y),s[1]||(s[1]=v(" Back"))]),_:1,__:[1]}),o(T,{trade:a(e).activeBot.tradeDetail,"stake-currency":a(e).activeBot.stakeCurrency},null,8,["trade","stake-currency"])])):c("",!0)])}}});export{ue as default};
//# sourceMappingURL=MobileTradesListView-CM3oQ7UY.js.map
