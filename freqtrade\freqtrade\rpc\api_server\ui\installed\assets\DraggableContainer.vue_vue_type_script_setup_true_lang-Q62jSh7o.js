import{d as s,c as o,a as d,e as r,a2 as a,x as t,z as l,N as c}from"./index-Cwqm8wBn.js";const f={class:"flex flex-col h-full w-full border dark:border-surface-800 border-surface-200 rounded-sm"},n={class:"drag-header py-1 px-2 dark:bg-surface-800 bg-surface-100 border-b border-surface-300 dark:border-surface-700"},b=s({inheritAttrs:!1,__name:"DraggableContainer",props:{header:{default:""}},setup(u){return(e,p)=>(d(),o("div",f,[r("div",n,[a(e.$slots,"header",{},()=>[t(l(e.header),1)])]),r("div",c({class:"p-0 h-full w-full overflow-auto"},e.$attrs),[a(e.$slots,"default")],16)]))}});export{b as _};
//# sourceMappingURL=DraggableContainer.vue_vue_type_script_setup_true_lang-Q62jSh7o.js.map
