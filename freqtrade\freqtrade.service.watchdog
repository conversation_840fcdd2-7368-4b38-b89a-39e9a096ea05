[Unit]
Description=Freqtrade Daemon
After=network.target

[Service]
# Set WorkingDirectory and ExecStart to your file paths accordingly
# NOTE: %h will be resolved to /home/<USER>
WorkingDirectory=%h/freqtrade
ExecStart=/usr/bin/freqtrade trade --sd-notify

Restart=always
#Restart=on-failure

# Note that we use Type=notify here
Type=notify

# Currently required if Type=notify
NotifyAccess=all

StartLimitInterval=1min
StartLimitBurst=5

TimeoutStartSec=1min

# Use here (process_throttle_secs * 2) or longer time interval
WatchdogSec=20

[Install]
WantedBy=default.target
