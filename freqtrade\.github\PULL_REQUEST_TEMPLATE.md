<!-- Thank you for sending your pull request. But first, have you included
unit tests, and is your code PEP8 conformant? [More details](https://github.com/freqtrade/freqtrade/blob/develop/CONTRIBUTING.md)

Did you use AI to create your changes?
If so, please state it clearly in the PR description (failing to do so may result in your PR being closed).

Also, please do a self review of the changes made before submitting the PR to make sure only relevant changes are included.
-->
## Summary

<!-- Explain in one sentence the goal of this PR -->

Solve the issue: #___

## Quick changelog

- <change log 1>
- <change log 1>

## What's new?

<!-- Explain in details what this PR solve or improve. You can include visuals. -->
