#!/usr/bin/env python3
"""
简化的量化交易机器人
基于OKX交易所，适合200美金预算
"""

import ccxt
import pandas as pd
import time
import json
from datetime import datetime

class SimpleTradingBot:
    def __init__(self, config_file='config.json'):
        """初始化交易机器人"""
        self.config = self.load_config(config_file)
        self.exchange = None
        self.setup_exchange()
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "exchange": {
                "name": "okx",
                "api_key": "",
                "secret": "",
                "password": "",
                "sandbox": True  # 默认使用沙盒模式
            },
            "trading": {
                "symbol": "BTC/USDT",
                "amount": 10,  # 每次交易金额(USDT)
                "strategy": "simple_grid"
            },
            "risk": {
                "max_position": 100,  # 最大持仓金额
                "stop_loss": 0.05,    # 5%止损
                "take_profit": 0.03   # 3%止盈
            }
        }
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，创建默认配置...")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            return default_config
    
    def setup_exchange(self):
        """设置交易所连接"""
        try:
            exchange_config = self.config['exchange']
            
            if exchange_config['name'] == 'okx':
                self.exchange = ccxt.okx({
                    'apiKey': exchange_config['api_key'],
                    'secret': exchange_config['secret'],
                    'password': exchange_config['password'],
                    'sandbox': exchange_config['sandbox'],
                    'enableRateLimit': True,
                })
            
            # 测试连接
            if exchange_config['api_key']:  # 只有配置了API密钥才测试
                balance = self.exchange.fetch_balance()
                print(f"✅ 交易所连接成功！USDT余额: {balance.get('USDT', {}).get('free', 0)}")
            else:
                print("⚠️  未配置API密钥，将使用模拟模式")
                
        except Exception as e:
            print(f"❌ 交易所连接失败: {e}")
            self.exchange = None
    
    def get_market_data(self, symbol, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            if not self.exchange:
                print("❌ 交易所未连接")
                return None
                
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None
    
    def simple_grid_strategy(self, df):
        """简单网格策略"""
        if df is None or len(df) < 20:
            return None
            
        current_price = df['close'].iloc[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        
        # 简单的买卖信号
        if current_price < sma_20 * 0.98:  # 价格低于20日均线2%
            return 'buy'
        elif current_price > sma_20 * 1.02:  # 价格高于20日均线2%
            return 'sell'
        else:
            return 'hold'
    
    def execute_trade(self, signal, symbol, amount):
        """执行交易（模拟）"""
        if not signal or signal == 'hold':
            return
            
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if self.config['exchange']['api_key']:
            # 实盘交易逻辑（需要实现）
            print(f"🔄 [{current_time}] 实盘交易信号: {signal} {symbol} 数量: {amount}")
        else:
            # 模拟交易
            print(f"📊 [{current_time}] 模拟交易信号: {signal} {symbol} 数量: {amount}")
    
    def run(self):
        """运行交易机器人"""
        print("🤖 简化交易机器人启动...")
        print(f"📈 交易对: {self.config['trading']['symbol']}")
        print(f"💰 每次交易金额: {self.config['trading']['amount']} USDT")
        print("=" * 50)
        
        while True:
            try:
                # 获取市场数据
                symbol = self.config['trading']['symbol']
                df = self.get_market_data(symbol)
                
                if df is not None:
                    # 执行策略
                    signal = self.simple_grid_strategy(df)
                    
                    # 执行交易
                    if signal:
                        self.execute_trade(signal, symbol, self.config['trading']['amount'])
                    
                    # 显示当前价格
                    current_price = df['close'].iloc[-1]
                    print(f"💹 [{datetime.now().strftime('%H:%M:%S')}] {symbol} 当前价格: ${current_price:.2f}")
                
                # 等待下一次检查（5分钟）
                time.sleep(300)
                
            except KeyboardInterrupt:
                print("\n👋 交易机器人已停止")
                break
            except Exception as e:
                print(f"❌ 运行错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

def main():
    """主函数"""
    print("🚀 启动简化量化交易机器人")
    print("适用于OKX交易所，200美金预算")
    print("=" * 50)
    
    # 创建并运行机器人
    bot = SimpleTradingBot()
    bot.run()

if __name__ == "__main__":
    main()
