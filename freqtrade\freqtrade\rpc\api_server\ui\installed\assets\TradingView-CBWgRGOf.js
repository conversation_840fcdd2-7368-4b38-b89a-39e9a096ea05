import{_ as Bt}from"./CandleChartContainer-G8jE_PHU.js";import{_ as bt}from"./TradeDetail-BE1KyuAM.js";import{_ as xt,a as kt,b as wt,c as $t}from"./TradeList.vue_vue_type_script_setup_true_lang--83SPrIm.js";import{_ as St}from"./DraggableContainer.vue_vue_type_script_setup_true_lang-Q62jSh7o.js";import{s as Tt,a as Ct,b as Lt,c as Pt,d as zt,_ as Vt}from"./index-D1-At5Up.js";import{d as V,u as O,c,a as r,e as s,b as o,g as j,f as e,h as i,y as Q,x as y,z as u,A as I,B as Dt,C as At,r as q,j as b,D as Mt,o as Nt,l as f,k as x,i as X,F as Y,m as Rt,E as G,G as z,v as Ft,H as T,I as Et,J as Ht,K as Gt,L as F,T as E,M as K,N as H,O as Ot,P as qt}from"./index-Cwqm8wBn.js";import{s as W,a as Z}from"./index-DhBpwJns.js";import Ut from"./PairListLive-H8pRoGeA.js";import{_ as It}from"./PeriodBreakdown.vue_vue_type_script_setup_true_lang-BFChaFcs.js";import{_ as jt}from"./BotBalance.vue_vue_type_script_setup_true_lang-DkCExAyF.js";import{s as Wt}from"./index-D6LFPO4a.js";import{_ as tt}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";import"./index-CONYmxgd.js";import"./index-Dt2Q_yjR.js";import"./check-olqpNIE9.js";import"./plus-box-outline-CDxaZbJP.js";import"./installCanvasRenderer-SA1tPojE.js";import"./chartZoom-DB0tK3Do.js";import"./index-BL9ilpkx.js";import"./index-ULt6J10p.js";import"./index-xjUaB_r9.js";import"./index-Bpiivi0c.js";import"./check-circle-C_wO5mMc.js";import"./install-_krYdrE6.js";const Zt={class:"mb-2"},Jt=V({__name:"PairLockList",setup(g){const t=O();function l(a){console.log(a),a.id!==void 0?t.activeBot.deleteLock(a.id):At("This Freqtrade version does not support deleting locks.")}return(a,h)=>{const B=Q,k=j,w=Z,m=Dt,d=W;return r(),c("div",null,[s("div",Zt,[h[0]||(h[0]=s("label",{class:"me-auto text-xl"},"Pair Locks",-1)),o(k,{class:"float-end",severity:"secondary",onClick:e(t).activeBot.getLocks},{icon:i(()=>[o(B)]),_:1},8,["onClick"])]),s("div",null,[o(d,{size:"small",items:e(t).activeBot.activeLocks},{default:i(()=>[o(w,{field:"pair",header:"Pair"}),o(w,{field:"lock_end_timestamp",header:"Until"},{body:i(({data:n,field:p})=>[y(u(("timestampms"in a?a.timestampms:e(I))(n[p])),1)]),_:1}),o(w,{field:"reason",header:"Reason"}),o(w,{field:"actions",header:"Actions"},{body:i(({data:n})=>[o(k,{class:"btn-xs ms-1",size:"small",severity:"secondary",title:"Delete Lock",onClick:p=>l(n)},{default:i(()=>[o(m)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["items"])])])}}}),Kt={class:"mb-2"},Qt=V({__name:"BotPerformance",setup(g){const t=O(),l=q("performance");function a(d,n){return d.length>n?d.substring(0,n)+"...":d}const h=b(()=>{var p;return[{performance:{key:"pair",label:"Pair"},entryStats:{key:"enter_tag",label:"Enter tag",formatter:_=>a(_,17)},exitStats:{key:"exit_reason",label:"Exit Reason",formatter:_=>a(_,17)},mixTagStats:{key:"mix_tag",label:"Mix Tag",formatter:_=>a(_,17)}}[l.value],{key:"profit",label:"Profit %"},{key:"profit_abs",label:`Profit ${(p=t.activeBot.botState)==null?void 0:p.stake_currency}`,formatter:_=>Mt(_,5)},{key:"count",label:"Count"}]}),B=b(()=>l.value==="performance"?t.activeBot.performanceStats:l.value==="entryStats"?t.activeBot.entryStats:l.value==="exitStats"?t.activeBot.exitStats:l.value==="mixTagStats"?t.activeBot.mixTagStats:[]),k=b(()=>t.activeBot.botApiVersion>=2.34),w=[{value:"performance",text:"Performance"},{value:"entryStats",text:"Entries"},{value:"exitStats",text:"Exits"},{value:"mixTagStats",text:"Mix Tag"}];function m(){l.value==="performance"&&t.activeBot.getPerformance(),l.value==="entryStats"&&t.activeBot.getEntryStats(),l.value==="exitStats"&&t.activeBot.getExitStats(),l.value==="mixTagStats"&&t.activeBot.getMixTagStats()}return Nt(()=>{m()}),(d,n)=>{const p=Q,_=j,S=Wt,D=Z,A=W;return r(),c("div",null,[s("div",Kt,[n[1]||(n[1]=s("h3",{class:"me-auto text-2xl inline"},"Performance",-1)),o(_,{class:"float-end",severity:"secondary",onClick:m},{icon:i(()=>[o(p)]),_:1})]),e(k)?(r(),f(S,{key:0,id:"order-direction",modelValue:e(l),"onUpdate:modelValue":n[0]||(n[0]=v=>X(l)?l.value=v:null),options:w,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",onChange:m},null,8,["modelValue"])):x("",!0),o(A,{size:"small",class:"text-center",value:e(B)},{default:i(()=>[(r(!0),c(Y,null,Rt(e(h),v=>(r(),f(D,{key:v.key,field:v.key,header:v.label},{body:i(C=>[y(u(v.formatter?v.formatter(C.data[v.key]):C.data[v.key]),1)]),_:2},1032,["field","header"]))),128))]),_:1},8,["value"])])}}}),Xt=V({__name:"BotProfit",props:{profit:{required:!0,type:Object},stakeCurrency:{required:!0,type:String},stakeCurrencyDecimals:{required:!0,type:Number}},setup(g){const t=g,l=b(()=>t.profit?[{metric:"ROI closed trades",value:t.profit.profit_closed_coin?`${G(t.profit.profit_closed_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${z(t.profit.profit_closed_ratio_mean,2)})`:"N/A"},{metric:"ROI all trades",value:t.profit.profit_all_coin?`${G(t.profit.profit_all_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${z(t.profit.profit_all_ratio_mean,2)})`:"N/A"},{metric:"Total Trade count",value:`${t.profit.trade_count??0}`},{metric:"Bot started",value:t.profit.bot_start_timestamp,isTs:!0},{metric:"First Trade opened",value:t.profit.first_trade_timestamp,isTs:!0},{metric:"Latest Trade opened",value:t.profit.latest_trade_timestamp,isTs:!0},{metric:"Win / Loss",value:`${t.profit.winning_trades??0} / ${t.profit.losing_trades??0}`},{metric:"Winrate",value:`${t.profit.winrate?z(t.profit.winrate):"N/A"}`},{metric:"Expectancy (ratio)",value:`${t.profit.expectancy?t.profit.expectancy.toFixed(2):"N/A"} (${t.profit.expectancy_ratio?t.profit.expectancy_ratio.toFixed(2):"N/A"})`},{metric:"Avg. Duration",value:`${t.profit.avg_duration??"N/A"}`},{metric:"Best performing",value:t.profit.best_pair?`${t.profit.best_pair}: ${z(t.profit.best_pair_profit_ratio,2)}`:"N/A"},{metric:"Trading volume",value:`${G(t.profit.trading_volume??0,t.stakeCurrency,t.stakeCurrencyDecimals)}`},{metric:"Profit factor",value:`${t.profit.profit_factor?t.profit.profit_factor.toFixed(2):"N/A"}`},{metric:"Max Drawdown",value:`${t.profit.max_drawdown?z(t.profit.max_drawdown,2):"N/A"} (${t.profit.max_drawdown_abs?G(t.profit.max_drawdown_abs,t.stakeCurrency,t.stakeCurrencyDecimals):"N/A"}) ${t.profit.max_drawdown_start_timestamp&&t.profit.max_drawdown_end_timestamp?"from "+I(t.profit.max_drawdown_start_timestamp)+" to "+I(t.profit.max_drawdown_end_timestamp):""}`}]:[]);return(a,h)=>{const B=Z,k=tt,w=W;return r(),f(w,{class:"text-start",small:"",borderless:"",value:e(l)},{default:i(()=>[o(B,{field:"metric",header:"Metric"}),o(B,{field:"value",header:"Value"},{body:i(({data:m})=>[m.isTs?(r(),f(k,{key:0,date:m.value,"show-timezone":""},null,8,["date"])):(r(),c(Y,{key:1},[y(u(m.value),1)],64))]),_:1})]),_:1},8,["value"])}}}),Yt={key:0,class:"p-4"},te={class:"mb-4"},ee={class:"mb-4"},oe={key:0,class:"mb-4"},ae={class:"mb-4"},ne={class:"mb-4"},re={key:1,class:"mb-4"},ie={key:0,class:"block"},se={class:"block"},le={class:"block"},ce={key:0,class:"block"},de={key:1,class:"block mb-4"},_e=V({__name:"BotStatus",setup(g){const t=O();return(l,a)=>{const h=Ft,B=tt,k=Xt;return e(t).activeBot.botState?(r(),c("div",Yt,[s("p",te,[a[0]||(a[0]=y(" Running Freqtrade ")),s("strong",null,u(e(t).activeBot.version),1)]),s("p",ee,[a[1]||(a[1]=y(" Running with ")),s("strong",null,u(e(t).activeBot.botState.max_open_trades)+"x"+u(e(t).activeBot.botState.stake_amount)+" "+u(e(t).activeBot.botState.stake_currency),1),a[2]||(a[2]=y(" on ")),s("strong",null,u(e(t).activeBot.botState.exchange),1),a[3]||(a[3]=y(" in ")),s("strong",null,u(e(t).activeBot.botState.trading_mode||"spot"),1),a[4]||(a[4]=y(" markets, with Strategy ")),s("strong",null,u(e(t).activeBot.botState.strategy),1),a[5]||(a[5]=y(". "))]),"stoploss_on_exchange"in e(t).activeBot.botState?(r(),c("p",oe,[a[6]||(a[6]=y(" Stoploss on exchange is ")),s("strong",null,u(e(t).activeBot.botState.stoploss_on_exchange?"enabled":"disabled"),1),a[7]||(a[7]=y(". "))])):x("",!0),s("p",ae,[a[8]||(a[8]=y(" Currently ")),s("strong",null,u(e(t).activeBot.botState.state),1),a[9]||(a[9]=y(", ")),s("strong",null,"force entry: "+u(e(t).activeBot.botState.force_entry_enable),1)]),s("p",null,[s("strong",null,u(e(t).activeBot.botState.dry_run?"Dry-Run":"Live"),1)]),o(h),s("p",ne," Avg Profit "+u(("formatPercent"in l?l.formatPercent:e(z))(e(t).activeBot.profit.profit_all_ratio_mean))+" (∑ "+u(("formatPercent"in l?l.formatPercent:e(z))(e(t).activeBot.profit.profit_all_ratio_sum))+") in "+u(e(t).activeBot.profit.trade_count)+" Trades, with an average duration of "+u(e(t).activeBot.profit.avg_duration)+". Best pair: "+u(e(t).activeBot.profit.best_pair)+". ",1),e(t).activeBot.profit.first_trade_timestamp?(r(),c("p",re,[e(t).activeBot.profit.bot_start_timestamp?(r(),c("span",ie,[a[10]||(a[10]=y(" Bot start date: ")),s("strong",null,[o(B,{date:e(t).activeBot.profit.bot_start_timestamp,"show-timezone":""},null,8,["date"])])])):x("",!0),s("span",se,[a[11]||(a[11]=y(" First trade opened: ")),s("strong",null,[o(B,{date:e(t).activeBot.profit.first_trade_timestamp,"show-timezone":""},null,8,["date"])])]),s("span",le,[a[12]||(a[12]=y(" Last trade opened: ")),s("strong",null,[o(B,{date:e(t).activeBot.profit.latest_trade_timestamp,"show-timezone":""},null,8,["date"])])])])):x("",!0),s("p",null,[e(t).activeBot.profit.profit_factor?(r(),c("span",ce," Profit factor: "+u(e(t).activeBot.profit.profit_factor.toFixed(2)),1)):x("",!0),e(t).activeBot.profit.trading_volume?(r(),c("span",de," Trading volume: "+u(("formatPriceCurrency"in l?l.formatPriceCurrency:e(G))(e(t).activeBot.profit.trading_volume,e(t).activeBot.botState.stake_currency,e(t).activeBot.botState.stake_currency_decimals??3)),1)):x("",!0)]),o(k,{class:"mx-1",profit:e(t).activeBot.profit,"stake-currency":e(t).activeBot.botState.stake_currency??"USDT","stake-currency-decimals":e(t).activeBot.botState.stake_currency_decimals??3},null,8,["profit","stake-currency","stake-currency-decimals"])])):x("",!0)}}}),ue={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function me(g,t){return r(),c("svg",ue,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M10 17c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2m6-9c1.1 0 2 .9 2 2v10c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V10c0-1.1.9-2 2-2h1V6c0-2.8 2.2-5 5-5s5 2.2 5 5v2zm-6-5C8.3 3 7 4.3 7 6v2h6V6c0-1.7-1.3-3-3-3m12 10h-2V7h2zm0 4h-2v-2h2z"},null,-1)]))}const pe=T({name:"mdi-lock-alert",render:me}),fe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ve(g,t){return r(),c("svg",fe,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M5 5v14h2v2H3V3h4v2zm15 2H7v2h13zm0 4H7v2h13zm0 4H7v2h13z"},null,-1)]))}const ye=T({name:"mdi-format-list-group",render:ve}),ge={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function he(g,t){return r(),c("svg",ge,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M3 4c-1.11 0-2 .89-2 2v12a2 2 0 0 0 2 2h7.26c1.31 1.88 3.45 3 5.74 3a7 7 0 0 0 7-7c0-1.83-.72-3.58-2-4.89V8a2 2 0 0 0-2-2h-8L9 4zm13 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m-1 1v5l3.61 2.16l.75-1.22l-2.86-1.69V12z"},null,-1)]))}const Be=T({name:"mdi-folder-clock",render:he}),be={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function xe(g,t){return r(),c("svg",be,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"m16 11.78l4.24-7.33l1.73 1l-5.23 9.05l-6.51-3.75L5.46 19H22v2H2V3h2v14.54L9.5 8z"},null,-1)]))}const ke=T({name:"mdi-chart-line",render:xe}),we={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function $e(g,t){return r(),c("svg",we,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M13 9h-2V7h2m0 10h-2v-6h2m-1-9A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2"},null,-1)]))}const Se=T({name:"mdi-information",render:$e}),Te={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ce(g,t){return r(),c("svg",Te,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M2 12a9 9 0 0 0 9 9c2.39 0 4.68-.94 6.4-2.6l-1.5-1.5A6.7 6.7 0 0 1 11 19c-6.24 0-9.36-7.54-4.95-11.95S18 5.77 18 12h-3l4 4h.1l3.9-4h-3a9 9 0 0 0-18 0"},null,-1)]))}const Le=T({name:"mdi-reload",render:Ce}),Pe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ze(g,t){return r(),c("svg",Pe,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M14 19h4V5h-4M6 19h4V5H6z"},null,-1)]))}const Ve=T({name:"mdi-pause",render:ze}),De={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ae(g,t){return r(),c("svg",De,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M18 18H6V6h12z"},null,-1)]))}const Me=T({name:"mdi-stop",render:Ae}),Ne={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Re(g,t){return r(),c("svg",Ne,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M8 5.14v14l11-7z"},null,-1)]))}const Fe=T({name:"mdi-play",render:Re}),Ee={class:"flex flex-row gap-1"},He=V({__name:"BotControls",setup(g){const t=O(),l=q(!1),a=q(),h=b(()=>{var d;return((d=t.activeBot.botState)==null?void 0:d.state)==="running"}),B=()=>{var n;const d={title:"Stop Bot",message:"Stop the bot loop from running?",accept:()=>{t.activeBot.stopBot()}};(n=a.value)==null||n.show(d)},k=()=>{var n;const d={title:"Pause - Stop Entering",message:"Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.",accept:()=>{t.activeBot.stopBuy()}};(n=a.value)==null||n.show(d)},w=()=>{var n;const d={title:"Reload",message:"Reload configuration (including strategy)?",accept:()=>{console.log("reload..."),t.activeBot.reloadConfig()}};(n=a.value)==null||n.show(d)},m=()=>{var n;const d={title:"ForceExit all",message:"Really forceexit ALL trades?",accept:()=>{const p={tradeid:"all"};t.activeBot.forceexit(p)}};(n=a.value)==null||n.show(d)};return(d,n)=>{const p=Fe,_=j,S=Me,D=Ve,A=Le,v=xt,C=kt,U=Et;return r(),c("div",Ee,[o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||e(h),title:"Start Trading",onClick:n[0]||(n[0]=$=>e(t).activeBot.startBot())},{icon:i(()=>[o(p)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(h),title:"Stop Trading - Also stops handling open trades.",onClick:n[1]||(n[1]=$=>B())},{icon:i(()=>[o(S)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(h),title:"Pause (StopBuy) - Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.",onClick:n[2]||(n[2]=$=>k())},{icon:i(()=>[o(D)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading,title:"Reload Config - reloads configuration including strategy, resetting all settings changed on the fly.",onClick:n[3]||(n[3]=$=>w())},{icon:i(()=>[o(A)]),_:1},8,["disabled"]),o(_,{severity:"secondary",size:"large",disabled:!e(t).activeBot.isTrading,title:"Force exit all",onClick:n[4]||(n[4]=$=>m())},{icon:i(()=>[o(v)]),_:1},8,["disabled"]),e(t).activeBot.botState&&e(t).activeBot.botState.force_entry_enable?(r(),f(_,{key:0,size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(h),title:"Force enter - Immediately enter a trade at an optional price. Exits are then handled according to strategy rules.",onClick:n[5]||(n[5]=$=>l.value=!0)},{icon:i(()=>[o(C)]),_:1},8,["disabled"])):x("",!0),(e(t).activeBot.isWebserverMode,x("",!0)),o(wt,{modelValue:e(l),"onUpdate:modelValue":n[7]||(n[7]=$=>X(l)?l.value=$:null),pair:e(t).activeBot.selectedPair},null,8,["modelValue","pair"]),o(U,{ref_key:"msgBox",ref:a},null,512)])}}}),Ge={class:"mt-1 flex justify-center"},Oe={title:"Pairs combined"},qe={key:0,class:"ms-1"},Ue={title:"General"},Ie={key:0,class:"ms-1"},je={title:"Performance"},We={key:0,class:"ms-1"},Ze={title:"Balance"},Je={key:0,class:"ms-1"},Ke={title:"Time Breakdown"},Qe={key:0,class:"ms-1"},Xe={title:"Pairlist"},Ye={key:0,class:"ms-1"},to={title:"Pair Locks"},eo={key:0,class:"ms-1"},To=V({__name:"TradingView",setup(g){const t=O(),l=Ht(),a=Gt(),h=q(""),B=v=>{h.value=v},k=b(()=>["","sm","md","lg","xl"].includes(h.value)),w=b(()=>l.layoutLocked||!k.value),m=b(()=>k.value?l.tradingLayout:[...l.getTradingLayoutSm]),d=b(()=>F(m.value,E.multiPane)),n=b(()=>F(m.value,E.openTrades)),p=b(()=>F(m.value,E.tradeHistory)),_=b(()=>F(m.value,E.tradeDetail)),S=b(()=>F(m.value,E.chartView)),D=b(()=>({sm:l.getTradingLayoutSm}));function A(v,C){t.activeBot.getPairCandles({pair:v,timeframe:t.activeBot.timeframe,columns:C})}return(v,C)=>{const U=He,$=Ot,L=Lt,et=Se,ot=ke,at=qt,nt=Be,rt=ye,it=pe,st=Ct,lt=Vt,P=zt,ct=_e,dt=Qt,_t=jt,ut=It,mt=Ut,pt=Jt,ft=Pt,vt=Tt,M=St,N=K("GridItem"),J=$t,yt=bt,gt=Bt,ht=K("GridLayout");return r(),f(ht,{class:"h-full w-full",style:{padding:"1px"},"row-height":50,layout:e(m),"vertical-compact":!1,margin:[1,1],"responsive-layouts":e(D),"is-resizable":!e(w),"is-draggable":!e(w),responsive:!0,cols:{lg:12,md:12,sm:12,xs:4,xxs:2},"col-num":12,"onUpdate:breakpoint":B},{default:i(({gridItemProps:R})=>[e(d).h!=0?(r(),f(N,H({key:0},R,{i:e(d).i,x:e(d).x,y:e(d).y,w:e(d).w,h:e(d).h,"drag-allow-from":".drag-header"}),{default:i(()=>[o(M,{header:"Multi Pane"},{default:i(()=>[s("div",Ge,[o(U,{class:"mt-1 mb-2"})]),o(vt,{value:"0",scrollable:"",lazy:""},{default:i(()=>[o(st,null,{default:i(()=>[o(L,{value:"0",severity:"secondary"},{default:i(()=>[s("div",Oe,[e(a).multiPaneButtonsShowText?(r(),c("span",qe,"Pairs combined")):(r(),f($,{key:1}))])]),_:1}),o(L,{value:"1",severity:"secondary"},{default:i(()=>[s("div",Ue,[e(a).multiPaneButtonsShowText?(r(),c("span",Ie,"General")):(r(),f(et,{key:1}))])]),_:1}),o(L,{value:"2",severity:"secondary"},{default:i(()=>[s("div",je,[e(a).multiPaneButtonsShowText?(r(),c("span",We,"Performance")):(r(),f(ot,{key:1}))])]),_:1}),o(L,{value:"3",severity:"secondary"},{default:i(()=>[s("div",Ze,[e(a).multiPaneButtonsShowText?(r(),c("span",Je,"Balance")):(r(),f(at,{key:1}))])]),_:1}),o(L,{value:"4",severity:"secondary"},{default:i(()=>[s("div",Ke,[e(a).multiPaneButtonsShowText?(r(),c("span",Qe,"Time Breakdown")):(r(),f(nt,{key:1}))])]),_:1}),o(L,{value:"5",severity:"secondary"},{default:i(()=>[s("div",Xe,[e(a).multiPaneButtonsShowText?(r(),c("span",Ye,"Pairlist")):(r(),f(rt,{key:1}))])]),_:1}),o(L,{value:"6",severity:"secondary"},{default:i(()=>[s("div",to,[e(a).multiPaneButtonsShowText?(r(),c("span",eo,"Pair Locks")):(r(),f(it,{key:1}))])]),_:1})]),_:1}),o(ft,null,{default:i(()=>[o(P,{value:"0"},{default:i(()=>[o(lt,{pairlist:e(t).activeBot.whitelist,"current-locks":e(t).activeBot.activeLocks,trades:e(t).activeBot.openTrades},null,8,["pairlist","current-locks","trades"])]),_:1}),o(P,{value:"1"},{default:i(()=>[o(ct)]),_:1}),o(P,{value:"2",lazy:""},{default:i(()=>[o(dt)]),_:1}),o(P,{value:"3",lazy:""},{default:i(()=>[o(_t)]),_:1}),o(P,{value:"4",lazy:""},{default:i(()=>[o(ut)]),_:1}),o(P,{value:"5",lazy:""},{default:i(()=>[o(mt)]),_:1}),o(P,{value:"6",lazy:""},{default:i(()=>[o(pt)]),_:1})]),_:1})]),_:1})]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(n).h!=0?(r(),f(N,H({key:1},R,{i:e(n).i,x:e(n).x,y:e(n).y,w:e(n).w,h:e(n).h,"drag-allow-from":".drag-header"}),{default:i(()=>[o(M,{header:"Open Trades"},{default:i(()=>[o(J,{class:"open-trades",trades:e(t).activeBot.openTrades,title:"Open trades","active-trades":!0,"empty-text":"Currently no open trades."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(p).h!=0?(r(),f(N,H({key:2},R,{i:e(p).i,x:e(p).x,y:e(p).y,w:e(p).w,h:e(p).h,"drag-allow-from":".drag-header"}),{default:i(()=>[o(M,{header:"Closed Trades"},{default:i(()=>[o(J,{class:"trade-history",trades:e(t).activeBot.closedTrades,title:"Trade history","show-filter":!0,"empty-text":"No closed trades so far."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(t).activeBot.detailTradeId&&e(t).activeBot.tradeDetail&&e(_).h!=0?(r(),f(N,H({key:3},R,{i:e(_).i,x:e(_).x,y:e(_).y,w:e(_).w,h:e(_).h,"min-h":4,"drag-allow-from":".drag-header"}),{default:i(()=>[o(M,{header:"Trade Detail"},{default:i(()=>[o(yt,{trade:e(t).activeBot.tradeDetail,"stake-currency":e(t).activeBot.stakeCurrency},null,8,["trade","stake-currency"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(_).h!=0?(r(),f(N,H({key:4},R,{i:e(S).i,x:e(S).x,y:e(S).y,w:e(S).w,h:e(S).h,"min-h":6,"drag-allow-from":".drag-header"}),{default:i(()=>[o(M,{header:"Chart"},{default:i(()=>[o(gt,{"available-pairs":e(t).activeBot.whitelist,"historic-view":!1,timeframe:e(t).activeBot.timeframe,trades:e(t).activeBot.allTrades,onRefreshData:A},null,8,["available-pairs","timeframe","trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0)]),_:1},8,["layout","responsive-layouts","is-resizable","is-draggable"])}}});export{To as default};
//# sourceMappingURL=TradingView-CBWgRGOf.js.map
