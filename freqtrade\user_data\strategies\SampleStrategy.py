from freqtrade.strategy import IStrategy
from pandas import DataFrame
import numpy as np

class SampleStrategy(IStrategy):
    """
    简单示例策略 - 适合200美金预算
    """
    
    # 策略参数
    INTERFACE_VERSION = 3
    
    # 最小收益率设置
    minimal_roi = {
        "60": 0.01,   # 60分钟后1%收益就卖出
        "30": 0.02,   # 30分钟后2%收益就卖出
        "0": 0.04     # 立即4%收益就卖出
    }
    
    # 止损设置
    stoploss = -0.10  # 10%止损
    
    # 时间框架
    timeframe = '5m'
    
    # 启动模式
    startup_candle_count: int = 30
    
    # 订单类型
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """添加技术指标"""
        
        # 简单移动平均线
        dataframe['sma_fast'] = dataframe['close'].rolling(window=10).mean()
        dataframe['sma_slow'] = dataframe['close'].rolling(window=20).mean()
        
        # RSI
        delta = dataframe['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        dataframe['rsi'] = 100 - (100 / (1 + rs))
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """入场信号"""
        
        dataframe.loc[
            (
                (dataframe['sma_fast'] > dataframe['sma_slow']) &  # 快线上穿慢线
                (dataframe['rsi'] < 70) &  # RSI不超买
                (dataframe['volume'] > 0)  # 有交易量
            ),
            'enter_long'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """出场信号"""
        
        dataframe.loc[
            (
                (dataframe['sma_fast'] < dataframe['sma_slow']) &  # 快线下穿慢线
                (dataframe['rsi'] > 30)  # RSI不超卖
            ),
            'exit_long'] = 1
        
        return dataframe
