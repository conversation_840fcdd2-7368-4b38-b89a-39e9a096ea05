import{H as o,c as t,a,e as n}from"./index-Cwqm8wBn.js";const r={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function s(l,e){return a(),t("svg",r,e[0]||(e[0]=[n("path",{fill:"currentColor",d:"M19 19V5H5v14zm0-16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-8 4h2v4h4v2h-4v4h-2v-4H7v-2h4z"},null,-1)]))}const i=o({name:"mdi-plus-box-outline",render:s});export{i as _};
//# sourceMappingURL=plus-box-outline-CDxaZbJP.js.map
