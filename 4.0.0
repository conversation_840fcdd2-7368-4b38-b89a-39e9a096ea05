Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/
Requirement already satisfied: ccxt in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (4.4.96)
Requirement already satisfied: setuptools>=60.9.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (70.2.0)
Requirement already satisfied: certifi>=2018.1.18 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (2025.7.14)
Requirement already satisfied: requests>=2.18.4 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (2.32.4)
Requirement already satisfied: cryptography>=2.6.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (45.0.5)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (4.12.2)
Requirement already satisfied: aiohttp>=3.10.11 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (3.12.14)
Requirement already satisfied: aiodns>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (3.5.0)
Requirement already satisfied: yarl>=1.7.2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from ccxt) (1.20.1)
Requirement already satisfied: pycares>=4.9.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiodns>=1.1.1->ccxt) (4.9.0)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (2.6.1)
Requirement already satisfied: aiosignal>=1.4.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (1.4.0)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (6.6.3)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp>=3.10.11->ccxt) (0.3.2)
Requirement already satisfied: idna>=2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from yarl>=1.7.2->ccxt) (3.10)
Requirement already satisfied: cffi>=1.14 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from cryptography>=2.6.1->ccxt) (1.17.1)
Requirement already satisfied: pycparser in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from cffi>=1.14->cryptography>=2.6.1->ccxt) (2.22)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests>=2.18.4->ccxt) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests>=2.18.4->ccxt) (2.5.0)
