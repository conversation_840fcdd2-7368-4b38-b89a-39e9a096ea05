import{a as F,s as N}from"./index-BmkrPNT-.js";import{Z as A,$ as I,ab as O,c as m,a as u,k as y,e as s,N as c,a2 as v,H as $,d as G,K as H,a$ as Z,J as q,bU as x,bV as g,l as W,h as i,z as U,f as t,b as n,x as d,W as K,g as J,v as j,a6 as Q,m as X,F as Y,bW as f,C as _}from"./index-Cwqm8wBn.js";import{s as ee}from"./index-xjUaB_r9.js";var te=A`
    .p-card {
        background: dt('card.background');
        color: dt('card.color');
        box-shadow: dt('card.shadow');
        border-radius: dt('card.border.radius');
        display: flex;
        flex-direction: column;
    }

    .p-card-caption {
        display: flex;
        flex-direction: column;
        gap: dt('card.caption.gap');
    }

    .p-card-body {
        padding: dt('card.body.padding');
        display: flex;
        flex-direction: column;
        gap: dt('card.body.gap');
    }

    .p-card-title {
        font-size: dt('card.title.font.size');
        font-weight: dt('card.title.font.weight');
    }

    .p-card-subtitle {
        color: dt('card.subtitle.color');
    }
`,se={root:"p-card p-component",header:"p-card-header",body:"p-card-body",caption:"p-card-caption",title:"p-card-title",subtitle:"p-card-subtitle",content:"p-card-content",footer:"p-card-footer"},oe=I.extend({name:"card",style:te,classes:se}),le={name:"BaseCard",extends:O,style:oe,provide:function(){return{$pcCard:this,$parentInstance:this}}},T={name:"Card",extends:le,inheritAttrs:!1};function ae(a,l,p,b,k,w){return u(),m("div",c({class:a.cx("root")},a.ptmi("root")),[a.$slots.header?(u(),m("div",c({key:0,class:a.cx("header")},a.ptm("header")),[v(a.$slots,"header")],16)):y("",!0),s("div",c({class:a.cx("body")},a.ptm("body")),[a.$slots.title||a.$slots.subtitle?(u(),m("div",c({key:0,class:a.cx("caption")},a.ptm("caption")),[a.$slots.title?(u(),m("div",c({key:0,class:a.cx("title")},a.ptm("title")),[v(a.$slots,"title")],16)):y("",!0),a.$slots.subtitle?(u(),m("div",c({key:1,class:a.cx("subtitle")},a.ptm("subtitle")),[v(a.$slots,"subtitle")],16)):y("",!0)],16)):y("",!0),s("div",c({class:a.cx("content")},a.ptm("content")),[v(a.$slots,"content")],16),a.$slots.footer?(u(),m("div",c({key:1,class:a.cx("footer")},a.ptm("footer")),[v(a.$slots,"footer")],16)):y("",!0)],16)],16)}T.render=ae;const ne={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ie(a,l){return u(),m("svg",ne,l[0]||(l[0]=[s("path",{fill:"currentColor",d:"M7.03 13.92h4V5l2.01-.03v8.95h3.99l-5 5Z"},null,-1)]))}const de=$({name:"mdi-arrow-down-thin",render:ie}),re={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ue(a,l){return u(),m("svg",re,l[0]||(l[0]=[s("path",{fill:"currentColor",d:"M7.03 9.97h4v8.92l2.01.03V9.97h3.99l-5-5Z"},null,-1)]))}const me=$({name:"mdi-arrow-up-thin",render:ue}),pe={class:"flex flex-col gap-4 text-start dark:text-surface-300"},ce={class:"text-left"},fe={class:"border border-surface-400 rounded-sm p-4 space-y-4"},be={class:"flex flex-row items-center gap-2 space-y-2"},ye={class:"space-y-1"},ve={class:"space-y-1"},ge={class:"border border-surface-400 rounded-sm p-4 space-y-4"},Ve={class:"space-y-1"},xe={class:"flex gap-4"},ke={class:"flex items-center"},we={class:"flex items-center"},Ce={class:"space-y-1"},Se={class:"flex flex-row gap-5 items-center"},Ue=["for"],$e={class:"mr-2"},Te={class:"border rounded-sm p-4 space-y-4"},Be={class:"space-y-2"},Pe={class:"border rounded-sm p-4 space-y-4"},De=G({__name:"SettingsView",setup(a){const l=H(),p=Z(),b=q(),k=["UTC",Intl.DateTimeFormat().resolvedOptions().timeZone],w=[{value:x.showPill,text:"Show pill in icon"},{value:x.asTitle,text:"Show in title"},{value:x.noOpenTrades,text:"Don't show open trades in header"}],B=[{value:g.GREEN_UP,text:"Green Up/Red Down"},{value:g.RED_UP,text:"Green Down/Red Up"}],P=()=>{b.resetTradingLayout(),b.resetDashboardLayout(),_("Layouts have been reset.")};return(C,e)=>{const r=K,z=J,L=j,S=Q,V=ee,h=me,D=de,E=N,M=T;return u(),W(M,{class:"mx-auto mt-3 p-4 max-w-4xl"},{title:i(()=>e[16]||(e[16]=[d("FreqUI Settings")])),content:i(()=>[s("div",pe,[s("p",ce,"UI Version: "+U(t(l).uiVersion),1),s("div",fe,[e[31]||(e[31]=s("h4",{class:"text-xl font-semibold"},"UI settings",-1)),n(r,{modelValue:t(b).layoutLocked,"onUpdate:modelValue":e[0]||(e[0]=o=>t(b).layoutLocked=o),class:"space-y-1"},{hint:i(()=>e[17]||(e[17]=[d(" Lock dynamic layouts, so they cannot move anymore. Can also be set from the navbar at the top. ")])),default:i(()=>[e[18]||(e[18]=d(" Lock dynamic layouts "))]),_:1,__:[18]},8,["modelValue"]),s("div",be,[n(z,{severity:"secondary",size:"small",class:"mb-0",onClick:P},{default:i(()=>e[19]||(e[19]=[d("Reset layout")])),_:1,__:[19]}),e[20]||(e[20]=s("small",{class:"block text-surface-600 dark:text-surface-400"},"Reset dynamic layouts to how they were.",-1))]),n(L),s("div",ye,[e[21]||(e[21]=s("label",{class:"block text-sm"},"Show open trades in header",-1)),n(S,{modelValue:t(l).openTradesInTitle,"onUpdate:modelValue":e[1]||(e[1]=o=>t(l).openTradesInTitle=o),options:w,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"]),e[22]||(e[22]=s("small",{class:"text-surface-600 dark:text-surface-400"},"Decide if open trades should be visualized",-1))]),s("div",ve,[e[23]||(e[23]=s("label",{class:"block text-sm"},"UTC Timezone",-1)),n(S,{modelValue:t(l).timezone,"onUpdate:modelValue":e[2]||(e[2]=o=>t(l).timezone=o),options:k,class:"w-full",size:"small"},null,8,["modelValue"]),e[24]||(e[24]=s("small",{class:"text-surface-600 dark:text-surface-400"},"Select timezone (UTC is recommended as exchanges usually work in UTC)",-1))]),n(r,{modelValue:t(l).backgroundSync,"onUpdate:modelValue":e[3]||(e[3]=o=>t(l).backgroundSync=o),class:"space-y-1"},{hint:i(()=>e[25]||(e[25]=[d(" Keep background sync running while other bots are selected. ")])),default:i(()=>[e[26]||(e[26]=d(" Background sync "))]),_:1,__:[26]},8,["modelValue"]),n(r,{modelValue:t(l).confirmDialog,"onUpdate:modelValue":e[4]||(e[4]=o=>t(l).confirmDialog=o),class:"space-y-1"},{hint:i(()=>e[27]||(e[27]=[d("Use confirmation dialogs when force-exiting a trade.")])),default:i(()=>[e[28]||(e[28]=d(" Show Confirm Dialog for Trade Exits "))]),_:1,__:[28]},8,["modelValue"]),n(r,{modelValue:t(l).multiPaneButtonsShowText,"onUpdate:modelValue":e[5]||(e[5]=o=>t(l).multiPaneButtonsShowText=o),class:"space-y-1"},{hint:i(()=>e[29]||(e[29]=[d("Show text on multi pane buttons. If disabled, only shows images.")])),default:i(()=>[e[30]||(e[30]=d(" Show Text on Multi Pane Buttons "))]),_:1,__:[30]},8,["modelValue"])]),s("div",ge,[e[41]||(e[41]=s("h4",{class:"text-lg font-semibold"},"Chart settings",-1)),s("div",Ve,[e[34]||(e[34]=s("label",{class:"block text-sm"},"Chart scale Side",-1)),s("div",xe,[s("div",ke,[n(V,{modelValue:t(l).chartLabelSide,"onUpdate:modelValue":e[6]||(e[6]=o=>t(l).chartLabelSide=o),value:"left",size:"small"},null,8,["modelValue"]),e[32]||(e[32]=s("label",{class:"ml-2"},"Left",-1))]),s("div",we,[n(V,{modelValue:t(l).chartLabelSide,"onUpdate:modelValue":e[7]||(e[7]=o=>t(l).chartLabelSide=o),value:"right",size:"small"},null,8,["modelValue"]),e[33]||(e[33]=s("label",{class:"ml-2"},"Right",-1))])]),e[35]||(e[35]=s("small",{class:"text-surface-600 dark:text-surface-400"},"Should the scale be displayed on the right or left?",-1))]),n(r,{modelValue:t(l).useHeikinAshiCandles,"onUpdate:modelValue":e[8]||(e[8]=o=>t(l).useHeikinAshiCandles=o),class:"space-y-1"},{hint:i(()=>e[36]||(e[36]=[d("Use Heikin Ashi candles in your charts")])),default:i(()=>[e[37]||(e[37]=d(" Use Heikin Ashi candles "))]),_:1,__:[37]},8,["modelValue"]),n(r,{modelValue:t(l).useReducedPairCalls,"onUpdate:modelValue":e[9]||(e[9]=o=>t(l).useReducedPairCalls=o),class:"space-y-1"},{hint:i(()=>e[38]||(e[38]=[d("Can reduce the transfer size for large dataframes. May require additional calls if the plot config changes.")])),default:i(()=>[e[39]||(e[39]=d(" Only request necessary columns "))]),_:1,__:[39]},8,["modelValue"]),s("div",Ce,[e[40]||(e[40]=s("label",{class:"block text-sm"},"Candle Color Preference",-1)),s("div",Se,[(u(),m(Y,null,X(B,o=>s("div",{key:o.value,class:"flex items-center"},[n(V,{modelValue:t(p).colorPreference,"onUpdate:modelValue":e[10]||(e[10]=R=>t(p).colorPreference=R),value:o.value,"input-id":`input-id${o.value}`,size:"small",onChange:t(p).updateProfitLossColor},null,8,["modelValue","value","input-id","onChange"]),s("label",{for:`input-id${o.value}`,class:"ml-2 flex items-center"},[s("span",$e,U(o.text),1),n(h,{color:o.value===t(g).GREEN_UP?t(p).colorProfit:t(p).colorLoss,class:"-ml-2"},null,8,["color"]),n(D,{color:o.value===t(g).GREEN_UP?t(p).colorLoss:t(p).colorProfit,class:"-ml-2"},null,8,["color"])],8,Ue)])),64))])])]),s("div",Te,[e[46]||(e[46]=s("h4",{class:"text-lg font-semibold"},"Notification Settings",-1)),s("div",Be,[n(r,{modelValue:t(l).notifications[t(f).entryFill],"onUpdate:modelValue":e[11]||(e[11]=o=>t(l).notifications[t(f).entryFill]=o)},{default:i(()=>e[42]||(e[42]=[d(" Entry notifications ")])),_:1,__:[42]},8,["modelValue"]),n(r,{modelValue:t(l).notifications[t(f).exitFill],"onUpdate:modelValue":e[12]||(e[12]=o=>t(l).notifications[t(f).exitFill]=o)},{default:i(()=>e[43]||(e[43]=[d(" Exit notifications ")])),_:1,__:[43]},8,["modelValue"]),n(r,{modelValue:t(l).notifications[t(f).entryCancel],"onUpdate:modelValue":e[13]||(e[13]=o=>t(l).notifications[t(f).entryCancel]=o)},{default:i(()=>e[44]||(e[44]=[d(" Entry Cancel notifications ")])),_:1,__:[44]},8,["modelValue"]),n(r,{modelValue:t(l).notifications[t(f).exitCancel],"onUpdate:modelValue":e[14]||(e[14]=o=>t(l).notifications[t(f).exitCancel]=o)},{default:i(()=>e[45]||(e[45]=[d(" Exit Cancel notifications ")])),_:1,__:[45]},8,["modelValue"])])]),s("div",Pe,[e[49]||(e[49]=s("h4",{class:"text-lg font-semibold"},"Backtesting settings",-1)),s("div",null,[e[47]||(e[47]=s("label",{for:"backtestMetrics",class:"block text-sm"},"Backtesting metrics",-1)),n(E,{id:"backtestMetrics",modelValue:t(l).backtestAdditionalMetrics,"onUpdate:modelValue":e[15]||(e[15]=o=>t(l).backtestAdditionalMetrics=o),options:"availableBacktestMetrics"in C?C.availableBacktestMetrics:t(F),"option-label":"header","option-value":"field",class:"w-full",size:"small",display:"chip"},null,8,["modelValue","options"]),e[48]||(e[48]=s("small",{class:"text-surface-600 dark:text-surface-400"},"Select which metrics should be shown on a per pair / tag basis.",-1))])])])]),_:1})}}});export{De as default};
//# sourceMappingURL=SettingsView-CGzQAwWg.js.map
