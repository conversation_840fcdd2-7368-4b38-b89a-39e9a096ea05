name: Build Documentation

on:
  push:
    branches:
      - develop
  release:
    types: [published]


# disable permissions for all of the available permissions
permissions: {}


jobs:
  build-docs:
    permissions:
      contents: write  #  for mike to push
    name: Deploy Docs through mike
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: true

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r docs/requirements-docs.txt

      - name: Fetch gh-pages branch
        run: |
          git fetch origin gh-pages --depth=1

      - name: Configure Git user
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

      - name: Build and push Mike
        if: ${{ github.event_name == 'push' }}
        run: |
          mike deploy ${REF_NAME} latest --push --update-aliases
        env:
          REF_NAME: ${{ github.ref_name }}

      - name: Build and push Mike - Release
        if: ${{ github.event_name == 'release' }}
        run: |
          mike deploy ${REF_NAME} stable --push --update-aliases
        env:
          REF_NAME: ${{ github.ref_name }}

      - name: Show mike versions
        run: |
          mike list
