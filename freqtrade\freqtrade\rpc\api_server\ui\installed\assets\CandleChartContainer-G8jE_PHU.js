import{Z as Ne,$ as Ge,as as Lt,ag as ft,N as P,c as _,a as y,d as re,r as A,o as Ze,U as N,e as S,b as v,z as K,a6 as Oe,f as a,i as X,g as Se,h as L,_ as ht,H as Pt,at as Vt,a0 as mt,au as At,av as Tt,aw as Dt,ax as Je,ay as $t,az as Et,aA as Bt,aB as De,M as ne,k as D,Y as bt,j as $,aC as ee,aD as Ht,l as Z,s as gt,aE as Ft,aF as Rt,aG as zt,aH as Kt,aI as Ut,aJ as jt,af as Nt,aK as ke,aL as Qe,aj as Gt,ac as et,aM as _e,aN as Zt,aO as tt,ap as $e,aP as qt,aQ as ue,ao as Xt,a2 as oe,n as it,aR as Yt,aS as Wt,F as Ce,m as vt,V as yt,x as Y,aT as Jt,aU as ze,aV as Qt,a5 as ei,aW as Me,aX as ti,u as xt,v as ii,C as ot,t as qe,aY as oi,aZ as ni,G as Ot,E as Ke,a_ as nt,K as li,a$ as si,b0 as de,y as ai,W as ri,b1 as ui,X as di,b2 as ci}from"./index-Cwqm8wBn.js";import{s as pi}from"./index-CONYmxgd.js";import{s as fi,a as hi,_ as St,b as mi}from"./index-Dt2Q_yjR.js";import{_ as bi}from"./check-olqpNIE9.js";import{u as gi,E as vi,e as yi,a as xi,b as Oi,c as Si,d as Ii,i as wi}from"./installCanvasRenderer-SA1tPojE.js";import{a as ki,c as _i,b as Ci,h as Mi,j as Li,f as Pi,k as Vi,l as Ai,m as Ti,n as Di,o as $i,i as Ei,g as Bi,p as Hi,d as Fi}from"./chartZoom-DB0tK3Do.js";var Ri=Ne`
    .p-textarea {
        font-family: inherit;
        font-feature-settings: inherit;
        font-size: 1rem;
        color: dt('textarea.color');
        background: dt('textarea.background');
        padding-block: dt('textarea.padding.y');
        padding-inline: dt('textarea.padding.x');
        border: 1px solid dt('textarea.border.color');
        transition:
            background dt('textarea.transition.duration'),
            color dt('textarea.transition.duration'),
            border-color dt('textarea.transition.duration'),
            outline-color dt('textarea.transition.duration'),
            box-shadow dt('textarea.transition.duration');
        appearance: none;
        border-radius: dt('textarea.border.radius');
        outline-color: transparent;
        box-shadow: dt('textarea.shadow');
    }

    .p-textarea:enabled:hover {
        border-color: dt('textarea.hover.border.color');
    }

    .p-textarea:enabled:focus {
        border-color: dt('textarea.focus.border.color');
        box-shadow: dt('textarea.focus.ring.shadow');
        outline: dt('textarea.focus.ring.width') dt('textarea.focus.ring.style') dt('textarea.focus.ring.color');
        outline-offset: dt('textarea.focus.ring.offset');
    }

    .p-textarea.p-invalid {
        border-color: dt('textarea.invalid.border.color');
    }

    .p-textarea.p-variant-filled {
        background: dt('textarea.filled.background');
    }

    .p-textarea.p-variant-filled:enabled:hover {
        background: dt('textarea.filled.hover.background');
    }

    .p-textarea.p-variant-filled:enabled:focus {
        background: dt('textarea.filled.focus.background');
    }

    .p-textarea:disabled {
        opacity: 1;
        background: dt('textarea.disabled.background');
        color: dt('textarea.disabled.color');
    }

    .p-textarea::placeholder {
        color: dt('textarea.placeholder.color');
    }

    .p-textarea.p-invalid::placeholder {
        color: dt('textarea.invalid.placeholder.color');
    }

    .p-textarea-fluid {
        width: 100%;
    }

    .p-textarea-resizable {
        overflow: hidden;
        resize: none;
    }

    .p-textarea-sm {
        font-size: dt('textarea.sm.font.size');
        padding-block: dt('textarea.sm.padding.y');
        padding-inline: dt('textarea.sm.padding.x');
    }

    .p-textarea-lg {
        font-size: dt('textarea.lg.font.size');
        padding-block: dt('textarea.lg.padding.y');
        padding-inline: dt('textarea.lg.padding.x');
    }
`,zi={root:function(e){var i=e.instance,n=e.props;return["p-textarea p-component",{"p-filled":i.$filled,"p-textarea-resizable ":n.autoResize,"p-textarea-sm p-inputfield-sm":n.size==="small","p-textarea-lg p-inputfield-lg":n.size==="large","p-invalid":i.$invalid,"p-variant-filled":i.$variant==="filled","p-textarea-fluid":i.$fluid}]}},Ki=Ge.extend({name:"textarea",style:Ri,classes:zi}),Ui={name:"BaseTextarea",extends:Lt,props:{autoResize:Boolean},style:Ki,provide:function(){return{$pcTextarea:this,$parentInstance:this}}};function ye(t){"@babel/helpers - typeof";return ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ye(t)}function ji(t,e,i){return(e=Ni(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Ni(t){var e=Gi(t,"string");return ye(e)=="symbol"?e:e+""}function Gi(t,e){if(ye(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var n=i.call(t,e);if(ye(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var It={name:"Textarea",extends:Ui,inheritAttrs:!1,observer:null,mounted:function(){var e=this;this.autoResize&&(this.observer=new ResizeObserver(function(){requestAnimationFrame(function(){e.resize()})}),this.observer.observe(this.$el))},updated:function(){this.autoResize&&this.resize()},beforeUnmount:function(){this.observer&&this.observer.disconnect()},methods:{resize:function(){this.$el.offsetParent&&(this.$el.style.height="auto",this.$el.style.height=this.$el.scrollHeight+"px",parseFloat(this.$el.style.height)>=parseFloat(this.$el.style.maxHeight)?(this.$el.style.overflowY="scroll",this.$el.style.height=this.$el.style.maxHeight):this.$el.style.overflow="hidden")},onInput:function(e){this.autoResize&&this.resize(),this.writeValue(e.target.value,e)}},computed:{attrs:function(){return P(this.ptmi("root",{context:{filled:this.$filled,disabled:this.disabled}}),this.formField)},dataP:function(){return ft(ji({invalid:this.$invalid,fluid:this.$fluid,filled:this.$variant==="filled"},this.size,this.size))}}},Zi=["value","name","disabled","aria-invalid","data-p"];function qi(t,e,i,n,l,o){return y(),_("textarea",P({class:t.cx("root"),value:t.d_value,name:t.name,disabled:t.disabled,"aria-invalid":t.invalid||void 0,"data-p":o.dataP,onInput:e[0]||(e[0]=function(){return o.onInput&&o.onInput.apply(o,arguments)})},o.attrs),null,16,Zi)}It.render=qi;const Xi={class:"flex flex-row"},Yi={class:"flex flex-col grow"},Wi={for:"selAvailableIndicator",class:"form-label"},wt=re({__name:"PlotIndicatorSelect",props:{modelValue:{required:!1,default:"",type:String},columns:{required:!0,type:Array},label:{required:!0,type:String}},emits:["update:modelValue","indicatorSelected"],setup(t,{emit:e}){const i=t,n=e,l=A("");function o(){n("indicatorSelected",l.value),n("update:modelValue",l.value)}function s(){l.value="",o()}return Ze(()=>{l.value=i.modelValue}),N(l,()=>{o()}),N(()=>i.modelValue,p=>{l.value=p}),(p,c)=>{const d=Oe,r=ht,I=Se;return y(),_("div",Xi,[S("div",Yi,[S("label",Wi,K(t.label),1),v(d,{modelValue:a(l),"onUpdate:modelValue":c[0]||(c[0]=C=>X(l)?l.value=C:null),options:t.columns,size:"small",clearable:!1,filter:"","auto-filter-focus":""},null,8,["modelValue","options"])]),v(I,{size:"small",title:"Abort",class:"ms-1 mt-auto",severity:"secondary",onClick:s},{icon:L(()=>[v(r)]),_:1})])}}}),Ji={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Qi(t,e){return y(),_("svg",Ji,e[0]||(e[0]=[S("path",{fill:"currentColor",d:"M19.78 3h-8.56C10.55 3 10 3.55 10 4.22V8h6v6h3.78c.67 0 1.22-.55 1.22-1.22V4.22C21 3.55 20.45 3 19.78 3m-7.34 3.67a1.23 1.23 0 1 1 0-2.46a1.23 1.23 0 0 1 1.23 1.23c0 .68-.55 1.23-1.23 1.23m6.12 6.11a1.23 1.23 0 1 1-.02-2.46c.68-.01 1.23.54 1.24 1.24c-.01.67-.55 1.21-1.22 1.22m0-6.11a1.23 1.23 0 1 1-.02-2.46a1.231 1.231 0 0 1 .02 2.46M4.22 10h8.56A1.22 1.22 0 0 1 14 11.22v8.56c0 .67-.55 1.22-1.22 1.22H4.22C3.55 21 3 20.45 3 19.78v-8.56c0-.67.55-1.22 1.22-1.22m4.28 4.28c-.67 0-1.22.55-1.22 1.22s.55 1.22 1.22 1.22s1.22-.55 1.22-1.22a1.22 1.22 0 0 0-1.22-1.22m-3.06-3.06c-.67 0-1.22.55-1.22 1.22a1.22 1.22 0 0 0 1.22 1.22c.67 0 1.22-.55 1.22-1.22s-.55-1.22-1.22-1.22m6.11 6.11c-.67 0-1.22.55-1.22 1.22s.55 1.22 1.22 1.22a1.22 1.22 0 0 0 1.22-1.22c0-.67-.54-1.21-1.21-1.22z"},null,-1)]))}const eo=Pt({name:"mdi-dice-multiple",render:Qi});var to=Ne`
    .p-colorpicker {
        display: inline-block;
        position: relative;
    }

    .p-colorpicker-dragging {
        cursor: pointer;
    }

    .p-colorpicker-preview {
        width: dt('colorpicker.preview.width');
        height: dt('colorpicker.preview.height');
        padding: 0;
        border: 0 none;
        border-radius: dt('colorpicker.preview.border.radius');
        transition:
            background dt('colorpicker.transition.duration'),
            color dt('colorpicker.transition.duration'),
            border-color dt('colorpicker.transition.duration'),
            outline-color dt('colorpicker.transition.duration'),
            box-shadow dt('colorpicker.transition.duration');
        outline-color: transparent;
        cursor: pointer;
    }

    .p-colorpicker-preview:enabled:focus-visible {
        border-color: dt('colorpicker.preview.focus.border.color');
        box-shadow: dt('colorpicker.preview.focus.ring.shadow');
        outline: dt('colorpicker.preview.focus.ring.width') dt('colorpicker.preview.focus.ring.style') dt('colorpicker.preview.focus.ring.color');
        outline-offset: dt('colorpicker.preview.focus.ring.offset');
    }

    .p-colorpicker-panel {
        background: dt('colorpicker.panel.background');
        border: 1px solid dt('colorpicker.panel.border.color');
        border-radius: dt('colorpicker.panel.border.radius');
        box-shadow: dt('colorpicker.panel.shadow');
        width: 193px;
        height: 166px;
        position: absolute;
        top: 0;
        left: 0;
    }

    .p-colorpicker-panel-inline {
        box-shadow: none;
        position: static;
    }

    .p-colorpicker-content {
        position: relative;
    }

    .p-colorpicker-color-selector {
        width: 150px;
        height: 150px;
        inset-block-start: 8px;
        inset-inline-start: 8px;
        position: absolute;
    }

    .p-colorpicker-color-background {
        width: 100%;
        height: 100%;
        background: linear-gradient(to top, #000 0%, rgba(0, 0, 0, 0) 100%), linear-gradient(to right, #fff 0%, rgba(255, 255, 255, 0) 100%);
    }

    .p-colorpicker-color-handle {
        position: absolute;
        inset-block-start: 0px;
        inset-inline-start: 150px;
        border-radius: 100%;
        width: 10px;
        height: 10px;
        border-width: 1px;
        border-style: solid;
        margin: -5px 0 0 -5px;
        cursor: pointer;
        opacity: 0.85;
        border-color: dt('colorpicker.handle.color');
    }

    .p-colorpicker-hue {
        width: 17px;
        height: 150px;
        inset-block-start: 8px;
        inset-inline-start: 167px;
        position: absolute;
        opacity: 0.85;
        background: linear-gradient(0deg, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red);
    }

    .p-colorpicker-hue-handle {
        position: absolute;
        inset-block-start: 150px;
        inset-inline-start: 0px;
        width: 21px;
        margin-inline-start: -2px;
        margin-block-start: -5px;
        height: 10px;
        border-width: 2px;
        border-style: solid;
        opacity: 0.85;
        cursor: pointer;
        border-color: dt('colorpicker.handle.color');
    }
`,io={root:"p-colorpicker p-component",preview:function(e){var i=e.props;return["p-colorpicker-preview",{"p-disabled":i.disabled}]},panel:function(e){var i=e.instance,n=e.props;return["p-colorpicker-panel",{"p-colorpicker-panel-inline":n.inline,"p-disabled":n.disabled,"p-invalid":i.$invalid}]},colorSelector:"p-colorpicker-color-selector",colorBackground:"p-colorpicker-color-background",colorHandle:"p-colorpicker-color-handle",hue:"p-colorpicker-hue",hueHandle:"p-colorpicker-hue-handle"},oo=Ge.extend({name:"colorpicker",style:to,classes:io}),no={name:"BaseColorPicker",extends:mt,props:{defaultColor:{type:null,default:"ff0000"},inline:{type:Boolean,default:!1},format:{type:String,default:"hex"},tabindex:{type:String,default:null},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},appendTo:{type:[String,Object],default:"body"},inputId:{type:String,default:null},panelClass:null,overlayClass:null},style:oo,provide:function(){return{$pcColorPicker:this,$parentInstance:this}}},kt={name:"ColorPicker",extends:no,inheritAttrs:!1,emits:["change","show","hide"],data:function(){return{overlayVisible:!1}},hsbValue:null,localHue:null,outsideClickListener:null,documentMouseMoveListener:null,documentMouseUpListener:null,scrollHandler:null,resizeListener:null,hueDragging:null,colorDragging:null,selfUpdate:null,picker:null,colorSelector:null,colorHandle:null,hueView:null,hueHandle:null,watch:{modelValue:{immediate:!0,handler:function(e){this.hsbValue=this.toHSB(e),this.selfUpdate?this.selfUpdate=!1:this.updateUI()}}},beforeUnmount:function(){this.unbindOutsideClickListener(),this.unbindDragListeners(),this.unbindResizeListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.picker&&this.autoZIndex&&De.clear(this.picker),this.clearRefs()},mounted:function(){this.updateUI()},methods:{pickColor:function(e){var i=this.colorSelector.getBoundingClientRect(),n=i.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),l=i.left+document.body.scrollLeft,o=Math.floor(100*Math.max(0,Math.min(150,(e.pageX||e.changedTouches[0].pageX)-l))/150),s=Math.floor(100*(150-Math.max(0,Math.min(150,(e.pageY||e.changedTouches[0].pageY)-n)))/150);this.hsbValue=this.validateHSB({h:this.localHue,s:o,b:s}),this.selfUpdate=!0,this.updateColorHandle(),this.updateInput(),this.updateModel(e)},pickHue:function(e){var i=this.hueView.getBoundingClientRect().top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0);this.localHue=Math.floor(360*(150-Math.max(0,Math.min(150,(e.pageY||e.changedTouches[0].pageY)-i)))/150),this.hsbValue=this.validateHSB({h:this.localHue,s:100,b:100}),this.selfUpdate=!0,this.updateColorSelector(),this.updateHue(),this.updateModel(e),this.updateInput()},updateModel:function(e){var i=this.d_value;switch(this.format){case"hex":i=this.HSBtoHEX(this.hsbValue);break;case"rgb":i=this.HSBtoRGB(this.hsbValue);break;case"hsb":i=this.hsbValue;break}this.writeValue(i,e),this.$emit("change",{event:e,value:i})},updateColorSelector:function(){if(this.colorSelector){var e=this.validateHSB({h:this.hsbValue.h,s:100,b:100});this.colorSelector.style.backgroundColor="#"+this.HSBtoHEX(e)}},updateColorHandle:function(){this.colorHandle&&(this.colorHandle.style.left=Math.floor(150*this.hsbValue.s/100)+"px",this.colorHandle.style.top=Math.floor(150*(100-this.hsbValue.b)/100)+"px")},updateHue:function(){this.hueHandle&&(this.hueHandle.style.top=Math.floor(150-150*this.hsbValue.h/360)+"px")},updateInput:function(){this.$refs.input&&(this.$refs.input.style.backgroundColor="#"+this.HSBtoHEX(this.hsbValue))},updateUI:function(){this.updateHue(),this.updateColorHandle(),this.updateInput(),this.updateColorSelector()},validateHSB:function(e){return{h:Math.min(360,Math.max(0,e.h)),s:Math.min(100,Math.max(0,e.s)),b:Math.min(100,Math.max(0,e.b))}},validateRGB:function(e){return{r:Math.min(255,Math.max(0,e.r)),g:Math.min(255,Math.max(0,e.g)),b:Math.min(255,Math.max(0,e.b))}},validateHEX:function(e){var i=6-e.length;if(i>0){for(var n=[],l=0;l<i;l++)n.push("0");n.push(e),e=n.join("")}return e},HEXtoRGB:function(e){var i=parseInt(e.indexOf("#")>-1?e.substring(1):e,16);return{r:i>>16,g:(i&65280)>>8,b:i&255}},HEXtoHSB:function(e){return this.RGBtoHSB(this.HEXtoRGB(e))},RGBtoHSB:function(e){var i={h:0,s:0,b:0},n=Math.min(e.r,e.g,e.b),l=Math.max(e.r,e.g,e.b),o=l-n;return i.b=l,i.s=l!==0?255*o/l:0,i.s!==0?e.r===l?i.h=(e.g-e.b)/o:e.g===l?i.h=2+(e.b-e.r)/o:i.h=4+(e.r-e.g)/o:i.h=-1,i.h*=60,i.h<0&&(i.h+=360),i.s*=100/255,i.b*=100/255,i},HSBtoRGB:function(e){var i={r:null,g:null,b:null},n=Math.round(e.h),l=Math.round(e.s*255/100),o=Math.round(e.b*255/100);if(l===0)i={r:o,g:o,b:o};else{var s=o,p=(255-l)*o/255,c=(s-p)*(n%60)/60;n===360&&(n=0),n<60?(i.r=s,i.b=p,i.g=p+c):n<120?(i.g=s,i.b=p,i.r=s-c):n<180?(i.g=s,i.r=p,i.b=p+c):n<240?(i.b=s,i.r=p,i.g=s-c):n<300?(i.b=s,i.g=p,i.r=p+c):n<360?(i.r=s,i.g=p,i.b=s-c):(i.r=0,i.g=0,i.b=0)}return{r:Math.round(i.r),g:Math.round(i.g),b:Math.round(i.b)}},RGBtoHEX:function(e){var i=[e.r.toString(16),e.g.toString(16),e.b.toString(16)];for(var n in i)i[n].length===1&&(i[n]="0"+i[n]);return i.join("")},HSBtoHEX:function(e){return this.RGBtoHEX(this.HSBtoRGB(e))},toHSB:function(e){var i;if(e)switch(this.format){case"hex":i=this.HEXtoHSB(e);break;case"rgb":i=this.RGBtoHSB(e);break;case"hsb":i=e;break}else i=this.HEXtoHSB(this.defaultColor);return this.localHue==null||!this.overlayVisible?this.localHue=i.h:i.h=this.localHue,i},onOverlayEnter:function(e){this.updateUI(),this.alignOverlay(),this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.autoZIndex&&De.set("overlay",e,this.baseZIndex,this.$primevue.config.zIndex.overlay),this.$attrSelector&&e.setAttribute(this.$attrSelector,""),this.$emit("show")},onOverlayLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.clearRefs(),this.$emit("hide")},onOverlayAfterLeave:function(e){this.autoZIndex&&De.clear(e)},alignOverlay:function(){this.appendTo==="self"?Et(this.picker,this.$refs.input):Bt(this.picker,this.$refs.input)},onInputClick:function(){this.disabled||(this.overlayVisible=!this.overlayVisible)},onInputKeydown:function(e){switch(e.code){case"Space":this.overlayVisible=!this.overlayVisible,e.preventDefault();break;case"Escape":case"Tab":this.overlayVisible=!1;break}},onInputBlur:function(e){var i,n;(i=(n=this.formField).onBlur)===null||i===void 0||i.call(n)},onColorMousedown:function(e){this.disabled||(this.bindDragListeners(),this.onColorDragStart(e))},onColorDragStart:function(e){this.disabled||(this.colorDragging=!0,this.pickColor(e),this.$el.setAttribute("p-colorpicker-dragging","true"),!this.isUnstyled&&Je(this.$el,"p-colorpicker-dragging"),e.preventDefault())},onDrag:function(e){this.colorDragging&&(this.pickColor(e),e.preventDefault()),this.hueDragging&&(this.pickHue(e),e.preventDefault())},onDragEnd:function(){this.colorDragging=!1,this.hueDragging=!1,this.$el.setAttribute("p-colorpicker-dragging","false"),!this.isUnstyled&&$t(this.$el,"p-colorpicker-dragging"),this.unbindDragListeners()},onHueMousedown:function(e){this.disabled||(this.bindDragListeners(),this.onHueDragStart(e))},onHueDragStart:function(e){this.disabled||(this.hueDragging=!0,this.pickHue(e),!this.isUnstyled&&Je(this.$el,"p-colorpicker-dragging"),e.preventDefault())},isInputClicked:function(e){return this.$refs.input&&this.$refs.input.isSameNode(e.target)},bindDragListeners:function(){this.bindDocumentMouseMoveListener(),this.bindDocumentMouseUpListener()},unbindDragListeners:function(){this.unbindDocumentMouseMoveListener(),this.unbindDocumentMouseUpListener()},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){e.overlayVisible&&e.picker&&!e.picker.contains(i.target)&&!e.isInputClicked(i)&&(e.overlayVisible=!1)},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new Dt(this.$refs.container,function(){e.overlayVisible&&(e.overlayVisible=!1)})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(){e.overlayVisible&&!Tt()&&(e.overlayVisible=!1)},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},bindDocumentMouseMoveListener:function(){this.documentMouseMoveListener||(this.documentMouseMoveListener=this.onDrag.bind(this),document.addEventListener("mousemove",this.documentMouseMoveListener))},unbindDocumentMouseMoveListener:function(){this.documentMouseMoveListener&&(document.removeEventListener("mousemove",this.documentMouseMoveListener),this.documentMouseMoveListener=null)},bindDocumentMouseUpListener:function(){this.documentMouseUpListener||(this.documentMouseUpListener=this.onDragEnd.bind(this),document.addEventListener("mouseup",this.documentMouseUpListener))},unbindDocumentMouseUpListener:function(){this.documentMouseUpListener&&(document.removeEventListener("mouseup",this.documentMouseUpListener),this.documentMouseUpListener=null)},pickerRef:function(e){this.picker=e},colorSelectorRef:function(e){this.colorSelector=e},colorHandleRef:function(e){this.colorHandle=e},hueViewRef:function(e){this.hueView=e},hueHandleRef:function(e){this.hueHandle=e},clearRefs:function(){this.picker=null,this.colorSelector=null,this.colorHandle=null,this.hueView=null,this.hueHandle=null},onOverlayClick:function(e){At.emit("overlay-click",{originalEvent:e,target:this.$el})}},components:{Portal:Vt}};function xe(t){"@babel/helpers - typeof";return xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xe(t)}function lt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(l){return Object.getOwnPropertyDescriptor(t,l).enumerable})),i.push.apply(i,n)}return i}function st(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?lt(Object(i),!0).forEach(function(n){lo(t,n,i[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):lt(Object(i)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(i,n))})}return t}function lo(t,e,i){return(e=so(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function so(t){var e=ao(t,"string");return xe(e)=="symbol"?e:e+""}function ao(t,e){if(xe(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var n=i.call(t,e);if(xe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ro=["id","tabindex","disabled"];function uo(t,e,i,n,l,o){var s=ne("Portal");return y(),_("div",P({ref:"container",class:t.cx("root")},t.ptmi("root")),[t.inline?D("",!0):(y(),_("input",P({key:0,ref:"input",id:t.inputId,type:"text",class:t.cx("preview"),readonly:"",tabindex:t.tabindex,disabled:t.disabled,onClick:e[0]||(e[0]=function(){return o.onInputClick&&o.onInputClick.apply(o,arguments)}),onKeydown:e[1]||(e[1]=function(){return o.onInputKeydown&&o.onInputKeydown.apply(o,arguments)}),onBlur:e[2]||(e[2]=function(){return o.onInputBlur&&o.onInputBlur.apply(o,arguments)})},t.ptm("preview")),null,16,ro)),v(s,{appendTo:t.appendTo,disabled:t.inline},{default:L(function(){return[v(bt,P({name:"p-connected-overlay",onEnter:o.onOverlayEnter,onLeave:o.onOverlayLeave,onAfterLeave:o.onOverlayAfterLeave},t.ptm("transition")),{default:L(function(){return[t.inline||l.overlayVisible?(y(),_("div",P({key:0,ref:o.pickerRef,class:[t.cx("panel"),t.panelClass,t.overlayClass],onClick:e[11]||(e[11]=function(){return o.onOverlayClick&&o.onOverlayClick.apply(o,arguments)})},st(st({},t.ptm("panel")),t.ptm("overlay"))),[S("div",P({class:t.cx("content")},t.ptm("content")),[S("div",P({ref:o.colorSelectorRef,class:t.cx("colorSelector"),onMousedown:e[3]||(e[3]=function(p){return o.onColorMousedown(p)}),onTouchstart:e[4]||(e[4]=function(p){return o.onColorDragStart(p)}),onTouchmove:e[5]||(e[5]=function(p){return o.onDrag(p)}),onTouchend:e[6]||(e[6]=function(p){return o.onDragEnd()})},t.ptm("colorSelector")),[S("div",P({class:t.cx("colorBackground")},t.ptm("colorBackground")),[S("div",P({ref:o.colorHandleRef,class:t.cx("colorHandle")},t.ptm("colorHandle")),null,16)],16)],16),S("div",P({ref:o.hueViewRef,class:t.cx("hue"),onMousedown:e[7]||(e[7]=function(p){return o.onHueMousedown(p)}),onTouchstart:e[8]||(e[8]=function(p){return o.onHueDragStart(p)}),onTouchmove:e[9]||(e[9]=function(p){return o.onDrag(p)}),onTouchend:e[10]||(e[10]=function(p){return o.onDragEnd()})},t.ptm("hue")),[S("div",P({ref:o.hueHandleRef,class:t.cx("hueHandle")},t.ptm("hueHandle")),null,16)],16)],16)],16)):D("",!0)]}),_:1},16,["onEnter","onLeave","onAfterLeave"])]}),_:1},8,["appendTo","disabled"])],16)}kt.render=uo;function ve(){return`#${Math.floor(Math.random()*16777215).toString(16).padStart(6,"0")}`}const co={class:"flex flex-col lg:flex-row justify-between mt-1"},po={class:"flex flex-col w-full"},fo={class:"flex flex-col w-full"},ho={key:1,class:"flex flex-col mt-2 gap-1 items-center"},mo=re({__name:"PlotIndicator",props:{modelValue:{required:!0,type:Object},columns:{required:!0,type:Array}},emits:["update:modelValue"],setup(t,{emit:e}){const i=t,n=e,l=A(ve()),o=$({get:()=>l.value,set:k=>{!k.startsWith("#")&&(k.length===3||k.length===6)&&/^[0-9a-fA-F]+$/.test(k)&&(k=`#${k}`),l.value=k}}),s=A(ee.line),p=A(Object.keys(ee)),c=A(""),d=A(!1),r=A(""),I=A(3);function C(){o.value=ve()}const H=$(()=>{if(d.value||!c.value)return{};const k={color:o.value,type:s.value};return r.value&&s.value===ee.line&&(k.fill_to=r.value),s.value==ee.scatter&&(k.scatterSymbolSize=I.value),{[c.value]:k}});function V(){n("update:modelValue",H.value)}return N(()=>i.modelValue,()=>{if([c.value]=Object.keys(i.modelValue),d.value=!1,c.value&&i.modelValue){const k=i.modelValue[c.value];o.value=k.color||ve(),s.value=k.type||ee.line,r.value=k.fill_to||""}},{immediate:!0}),Ht([o,s,r,I],()=>{V()},{debounce:200}),(k,w)=>{const h=Oe,u=kt,g=hi,x=gt,f=eo,m=Se,T=fi,B=wt,G=pi;return y(),_("div",null,[S("div",co,[S("div",po,[w[5]||(w[5]=S("label",{for:"plotTypeSelector",class:"form-label"},"Type",-1)),v(h,{id:"plotTypeSelector",modelValue:a(s),"onUpdate:modelValue":w[0]||(w[0]=E=>X(s)?s.value=E:null),class:"text-left",size:"small",options:a(p)},null,8,["modelValue","options"])]),S("div",fo,[w[6]||(w[6]=S("label",{for:"selAvailableIndicator",class:"colsel"},"Color",-1)),v(T,null,{default:L(()=>[v(g,{class:"p-0!"},{default:L(()=>[v(u,{modelValue:a(o),"onUpdate:modelValue":w[1]||(w[1]=E=>X(o)?o.value=E:null),type:"color",class:"m-auto"},null,8,["modelValue"])]),_:1}),v(x,{id:"colsel",modelValue:a(o),"onUpdate:modelValue":w[2]||(w[2]=E=>X(o)?o.value=E:null),size:"small",class:"grow"},null,8,["modelValue"]),v(g,null,{default:L(()=>[v(m,{severity:"primary",size:"small",onClick:C},{icon:L(()=>[v(f)]),_:1})]),_:1})]),_:1})])]),a(s)===a(ee).line?(y(),Z(B,{key:0,modelValue:a(r),"onUpdate:modelValue":w[3]||(w[3]=E=>X(r)?r.value=E:null),columns:t.columns,class:"mt-1",label:"Area chart - Fill to (leave empty for line chart)"},null,8,["modelValue","columns"])):D("",!0),a(s)===a(ee).scatter?(y(),_("div",ho,[w[7]||(w[7]=S("label",{for:"scatterSymbolSize",class:"text-nowrap"},"Scatter symbol size",-1)),v(G,{id:"scatterSymbolSize",modelValue:a(I),"onUpdate:modelValue":w[4]||(w[4]=E=>X(I)?I.value=E:null),min:0,"show-buttons":"",size:"small","button-layout":"horizontal",class:"text-center w-full"},null,8,["modelValue"])])):D("",!0)])}}});var bo=Ne`
    .p-listbox {
        background: dt('listbox.background');
        color: dt('listbox.color');
        border: 1px solid dt('listbox.border.color');
        border-radius: dt('listbox.border.radius');
        transition:
            background dt('listbox.transition.duration'),
            color dt('listbox.transition.duration'),
            border-color dt('listbox.transition.duration'),
            box-shadow dt('listbox.transition.duration'),
            outline-color dt('listbox.transition.duration');
        outline-color: transparent;
        box-shadow: dt('listbox.shadow');
    }

    .p-listbox.p-disabled {
        opacity: 1;
        background: dt('listbox.disabled.background');
        color: dt('listbox.disabled.color');
    }

    .p-listbox.p-disabled .p-listbox-option {
        color: dt('listbox.disabled.color');
    }

    .p-listbox.p-invalid {
        border-color: dt('listbox.invalid.border.color');
    }

    .p-listbox-header {
        padding: dt('listbox.list.header.padding');
    }

    .p-listbox-filter {
        width: 100%;
    }

    .p-listbox-list-container {
        overflow: auto;
    }

    .p-listbox-list {
        list-style-type: none;
        margin: 0;
        padding: dt('listbox.list.padding');
        outline: 0 none;
        display: flex;
        flex-direction: column;
        gap: dt('listbox.list.gap');
    }

    .p-listbox-option {
        display: flex;
        align-items: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        padding: dt('listbox.option.padding');
        border: 0 none;
        border-radius: dt('listbox.option.border.radius');
        color: dt('listbox.option.color');
        transition:
            background dt('listbox.transition.duration'),
            color dt('listbox.transition.duration'),
            border-color dt('listbox.transition.duration'),
            box-shadow dt('listbox.transition.duration'),
            outline-color dt('listbox.transition.duration');
    }

    .p-listbox-striped li:nth-child(even of .p-listbox-option) {
        background: dt('listbox.option.striped.background');
    }

    .p-listbox .p-listbox-list .p-listbox-option.p-listbox-option-selected {
        background: dt('listbox.option.selected.background');
        color: dt('listbox.option.selected.color');
    }

    .p-listbox:not(.p-disabled) .p-listbox-option.p-listbox-option-selected.p-focus {
        background: dt('listbox.option.selected.focus.background');
        color: dt('listbox.option.selected.focus.color');
    }

    .p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled).p-focus {
        background: dt('listbox.option.focus.background');
        color: dt('listbox.option.focus.color');
    }

    .p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled):hover {
        background: dt('listbox.option.focus.background');
        color: dt('listbox.option.focus.color');
    }

    .p-listbox-option-blank-icon {
        flex-shrink: 0;
    }

    .p-listbox-option-check-icon {
        position: relative;
        flex-shrink: 0;
        margin-inline-start: dt('listbox.checkmark.gutter.start');
        margin-inline-end: dt('listbox.checkmark.gutter.end');
        color: dt('listbox.checkmark.color');
    }

    .p-listbox-option-group {
        margin: 0;
        padding: dt('listbox.option.group.padding');
        color: dt('listbox.option.group.color');
        background: dt('listbox.option.group.background');
        font-weight: dt('listbox.option.group.font.weight');
    }

    .p-listbox-empty-message {
        padding: dt('listbox.empty.message.padding');
    }
`,go={root:function(e){var i=e.instance,n=e.props;return["p-listbox p-component",{"p-listbox-striped":n.striped,"p-disabled":n.disabled,"p-invalid":i.$invalid}]},header:"p-listbox-header",pcFilter:"p-listbox-filter",listContainer:"p-listbox-list-container",list:"p-listbox-list",optionGroup:"p-listbox-option-group",option:function(e){var i=e.instance,n=e.props,l=e.option,o=e.index,s=e.getItemOptions;return["p-listbox-option",{"p-listbox-option-selected":i.isSelected(l)&&n.highlightOnSelect,"p-focus":i.focusedOptionIndex===i.getOptionIndex(o,s),"p-disabled":i.isOptionDisabled(l)}]},optionCheckIcon:"p-listbox-option-check-icon",optionBlankIcon:"p-listbox-option-blank-icon",emptyMessage:"p-listbox-empty-message"},vo=Ge.extend({name:"listbox",style:bo,classes:go}),yo={name:"BaseListbox",extends:mt,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,optionGroupLabel:null,optionGroupChildren:null,listStyle:null,scrollHeight:{type:String,default:"14rem"},dataKey:null,multiple:{type:Boolean,default:!1},metaKeySelection:{type:Boolean,default:!1},filter:Boolean,filterPlaceholder:String,filterLocale:String,filterMatchMode:{type:String,default:"contains"},filterFields:{type:Array,default:null},virtualScrollerOptions:{type:Object,default:null},autoOptionFocus:{type:Boolean,default:!0},selectOnFocus:{type:Boolean,default:!1},focusOnHover:{type:Boolean,default:!0},highlightOnSelect:{type:Boolean,default:!0},checkmark:{type:Boolean,default:!1},filterMessage:{type:String,default:null},selectionMessage:{type:String,default:null},emptySelectionMessage:{type:String,default:null},emptyFilterMessage:{type:String,default:null},emptyMessage:{type:String,default:null},filterIcon:{type:String,default:void 0},striped:{type:Boolean,default:!1},tabindex:{type:Number,default:0},ariaLabel:{type:String,default:null},ariaLabelledby:{type:String,default:null}},style:vo,provide:function(){return{$pcListbox:this,$parentInstance:this}}};function Ee(t){return Io(t)||So(t)||Oo(t)||xo()}function xo(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Oo(t,e){if(t){if(typeof t=="string")return Ue(t,e);var i={}.toString.call(t).slice(8,-1);return i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set"?Array.from(t):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Ue(t,e):void 0}}function So(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Io(t){if(Array.isArray(t))return Ue(t)}function Ue(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}var Xe={name:"Listbox",extends:yo,inheritAttrs:!1,emits:["change","focus","blur","filter","item-dblclick","option-dblclick"],list:null,virtualScroller:null,optionTouched:!1,startRangeIndex:-1,searchTimeout:null,searchValue:"",data:function(){return{filterValue:null,focused:!1,focusedOptionIndex:-1}},watch:{options:function(){this.autoUpdateModel()}},mounted:function(){this.autoUpdateModel()},methods:{getOptionIndex:function(e,i){return this.virtualScrollerDisabled?e:i&&i(e).index},getOptionLabel:function(e){return this.optionLabel?ue(e,this.optionLabel):typeof e=="string"?e:null},getOptionValue:function(e){return this.optionValue?ue(e,this.optionValue):e},getOptionRenderKey:function(e,i){return(this.dataKey?ue(e,this.dataKey):this.getOptionLabel(e))+"_"+i},getPTOptions:function(e,i,n,l){return this.ptm(l,{context:{selected:this.isSelected(e),focused:this.focusedOptionIndex===this.getOptionIndex(n,i),disabled:this.isOptionDisabled(e)}})},isOptionDisabled:function(e){return this.optionDisabled?ue(e,this.optionDisabled):!1},isOptionGroup:function(e){return this.optionGroupLabel&&e.optionGroup&&e.group},getOptionGroupLabel:function(e){return ue(e,this.optionGroupLabel)},getOptionGroupChildren:function(e){return ue(e,this.optionGroupChildren)},getAriaPosInset:function(e){var i=this;return(this.optionGroupLabel?e-this.visibleOptions.slice(0,e).filter(function(n){return i.isOptionGroup(n)}).length:e)+1},onFirstHiddenFocus:function(){$e(this.list);var e=tt(this.$el,':not([data-p-hidden-focusable="true"])');this.$refs.lastHiddenFocusableElement.tabIndex=qt(e)?void 0:-1,this.$refs.firstHiddenFocusableElement.tabIndex=-1},onLastHiddenFocus:function(e){var i=e.relatedTarget;if(i===this.list){var n=tt(this.$el,':not([data-p-hidden-focusable="true"])');$e(n),this.$refs.firstHiddenFocusableElement.tabIndex=void 0}else $e(this.$refs.firstHiddenFocusableElement);this.$refs.lastHiddenFocusableElement.tabIndex=-1},onFocusout:function(e){!this.$el.contains(e.relatedTarget)&&this.$refs.lastHiddenFocusableElement&&this.$refs.firstHiddenFocusableElement&&(this.$refs.lastHiddenFocusableElement.tabIndex=this.$refs.firstHiddenFocusableElement.tabIndex=void 0)},onListFocus:function(e){this.focused=!0,this.focusedOptionIndex=this.focusedOptionIndex!==-1?this.focusedOptionIndex:this.autoOptionFocus?this.findFirstFocusedOptionIndex():this.findSelectedOptionIndex(),this.autoUpdateModel(),this.scrollInView(this.focusedOptionIndex),this.$emit("focus",e)},onListBlur:function(e){this.focused=!1,this.focusedOptionIndex=this.startRangeIndex=-1,this.searchValue="",this.$emit("blur",e)},onListKeyDown:function(e){var i=this,n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"PageDown":this.onPageDownKey(e);break;case"PageUp":this.onPageUpKey(e);break;case"Enter":case"NumpadEnter":case"Space":this.onSpaceKey(e);break;case"Tab":break;case"ShiftLeft":case"ShiftRight":this.onShiftKey(e);break;default:if(this.multiple&&e.code==="KeyA"&&n){var l=this.visibleOptions.filter(function(o){return i.isValidOption(o)}).map(function(o){return i.getOptionValue(o)});this.updateModel(e,l),e.preventDefault();break}!n&&Zt(e.key)&&(this.searchOptions(e,e.key),e.preventDefault());break}},onOptionSelect:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-1;this.disabled||this.isOptionDisabled(i)||(this.multiple?this.onOptionSelectMultiple(e,i):this.onOptionSelectSingle(e,i),this.optionTouched=!1,n!==-1&&(this.focusedOptionIndex=n))},onOptionMouseDown:function(e,i){this.changeFocusedOptionIndex(e,i)},onOptionMouseMove:function(e,i){this.focusOnHover&&this.focused&&this.changeFocusedOptionIndex(e,i)},onOptionTouchEnd:function(){this.disabled||(this.optionTouched=!0)},onOptionDblClick:function(e,i){this.$emit("item-dblclick",{originalEvent:e,value:i}),this.$emit("option-dblclick",{originalEvent:e,value:i})},onOptionSelectSingle:function(e,i){var n=this.isSelected(i),l=!1,o=null,s=this.optionTouched?!1:this.metaKeySelection;if(s){var p=e&&(e.metaKey||e.ctrlKey);n?p&&(o=null,l=!0):(o=this.getOptionValue(i),l=!0)}else o=n?null:this.getOptionValue(i),l=!0;l&&this.updateModel(e,o)},onOptionSelectMultiple:function(e,i){var n=this.isSelected(i),l=null,o=this.optionTouched?!1:this.metaKeySelection;if(o){var s=e.metaKey||e.ctrlKey;n?l=s?this.removeOption(i):[this.getOptionValue(i)]:(l=s?this.d_value||[]:[],l=[].concat(Ee(l),[this.getOptionValue(i)]))}else l=n?this.removeOption(i):[].concat(Ee(this.d_value||[]),[this.getOptionValue(i)]);this.updateModel(e,l)},onOptionSelectRange:function(e){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:-1,l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-1;if(n===-1&&(n=this.findNearestSelectedOptionIndex(l,!0)),l===-1&&(l=this.findNearestSelectedOptionIndex(n)),n!==-1&&l!==-1){var o=Math.min(n,l),s=Math.max(n,l),p=this.visibleOptions.slice(o,s+1).filter(function(c){return i.isValidOption(c)}).map(function(c){return i.getOptionValue(c)});this.updateModel(e,p)}},onFilterChange:function(e){this.$emit("filter",{originalEvent:e,value:e.target.value,filterValue:this.visibleOptions}),this.focusedOptionIndex=this.startRangeIndex=-1},onFilterBlur:function(){this.focusedOptionIndex=this.startRangeIndex=-1},onFilterKeyDown:function(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"ArrowLeft":case"ArrowRight":this.onArrowLeftKey(e,!0);break;case"Home":this.onHomeKey(e,!0);break;case"End":this.onEndKey(e,!0);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"ShiftLeft":case"ShiftRight":this.onShiftKey(e);break}},onArrowDownKey:function(e){var i=this.focusedOptionIndex!==-1?this.findNextOptionIndex(this.focusedOptionIndex):this.findFirstFocusedOptionIndex();this.multiple&&e.shiftKey&&this.onOptionSelectRange(e,this.startRangeIndex,i),this.changeFocusedOptionIndex(e,i),e.preventDefault()},onArrowUpKey:function(e){var i=this.focusedOptionIndex!==-1?this.findPrevOptionIndex(this.focusedOptionIndex):this.findLastFocusedOptionIndex();this.multiple&&e.shiftKey&&this.onOptionSelectRange(e,i,this.startRangeIndex),this.changeFocusedOptionIndex(e,i),e.preventDefault()},onArrowLeftKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;i&&(this.focusedOptionIndex=-1)},onHomeKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(i){var n=e.currentTarget;e.shiftKey?n.setSelectionRange(0,e.target.selectionStart):(n.setSelectionRange(0,0),this.focusedOptionIndex=-1)}else{var l=e.metaKey||e.ctrlKey,o=this.findFirstOptionIndex();this.multiple&&e.shiftKey&&l&&this.onOptionSelectRange(e,o,this.startRangeIndex),this.changeFocusedOptionIndex(e,o)}e.preventDefault()},onEndKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(i){var n=e.currentTarget;if(e.shiftKey)n.setSelectionRange(e.target.selectionStart,n.value.length);else{var l=n.value.length;n.setSelectionRange(l,l),this.focusedOptionIndex=-1}}else{var o=e.metaKey||e.ctrlKey,s=this.findLastOptionIndex();this.multiple&&e.shiftKey&&o&&this.onOptionSelectRange(e,this.startRangeIndex,s),this.changeFocusedOptionIndex(e,s)}e.preventDefault()},onPageUpKey:function(e){this.scrollInView(0),e.preventDefault()},onPageDownKey:function(e){this.scrollInView(this.visibleOptions.length-1),e.preventDefault()},onEnterKey:function(e){this.focusedOptionIndex!==-1&&(this.multiple&&e.shiftKey?this.onOptionSelectRange(e,this.focusedOptionIndex):this.onOptionSelect(e,this.visibleOptions[this.focusedOptionIndex]))},onSpaceKey:function(e){e.preventDefault(),this.onEnterKey(e)},onShiftKey:function(){this.startRangeIndex=this.focusedOptionIndex},isOptionMatched:function(e){var i;return this.isValidOption(e)&&typeof this.getOptionLabel(e)=="string"&&((i=this.getOptionLabel(e))===null||i===void 0?void 0:i.toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)))},isValidOption:function(e){return ke(e)&&!(this.isOptionDisabled(e)||this.isOptionGroup(e))},isValidSelectedOption:function(e){return this.isValidOption(e)&&this.isSelected(e)},isEquals:function(e,i){return et(e,i,this.equalityKey)},isSelected:function(e){var i=this,n=this.getOptionValue(e);return this.multiple?(this.d_value||[]).some(function(l){return i.isEquals(l,n)}):this.isEquals(this.d_value,n)},findFirstOptionIndex:function(){var e=this;return this.visibleOptions.findIndex(function(i){return e.isValidOption(i)})},findLastOptionIndex:function(){var e=this;return _e(this.visibleOptions,function(i){return e.isValidOption(i)})},findNextOptionIndex:function(e){var i=this,n=e<this.visibleOptions.length-1?this.visibleOptions.slice(e+1).findIndex(function(l){return i.isValidOption(l)}):-1;return n>-1?n+e+1:e},findPrevOptionIndex:function(e){var i=this,n=e>0?_e(this.visibleOptions.slice(0,e),function(l){return i.isValidOption(l)}):-1;return n>-1?n:e},findSelectedOptionIndex:function(){var e=this;if(this.$filled)if(this.multiple){for(var i=function(){var s=e.d_value[l],p=e.visibleOptions.findIndex(function(c){return e.isValidSelectedOption(c)&&e.isEquals(s,e.getOptionValue(c))});if(p>-1)return{v:p}},n,l=this.d_value.length-1;l>=0;l--)if(n=i(),n)return n.v}else return this.visibleOptions.findIndex(function(o){return e.isValidSelectedOption(o)});return-1},findFirstSelectedOptionIndex:function(){var e=this;return this.$filled?this.visibleOptions.findIndex(function(i){return e.isValidSelectedOption(i)}):-1},findLastSelectedOptionIndex:function(){var e=this;return this.$filled?_e(this.visibleOptions,function(i){return e.isValidSelectedOption(i)}):-1},findNextSelectedOptionIndex:function(e){var i=this,n=this.$filled&&e<this.visibleOptions.length-1?this.visibleOptions.slice(e+1).findIndex(function(l){return i.isValidSelectedOption(l)}):-1;return n>-1?n+e+1:-1},findPrevSelectedOptionIndex:function(e){var i=this,n=this.$filled&&e>0?_e(this.visibleOptions.slice(0,e),function(l){return i.isValidSelectedOption(l)}):-1;return n>-1?n:-1},findNearestSelectedOptionIndex:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=-1;return this.$filled&&(i?(n=this.findPrevSelectedOptionIndex(e),n=n===-1?this.findNextSelectedOptionIndex(e):n):(n=this.findNextSelectedOptionIndex(e),n=n===-1?this.findPrevSelectedOptionIndex(e):n)),n>-1?n:e},findFirstFocusedOptionIndex:function(){var e=this.findFirstSelectedOptionIndex();return e<0?this.findFirstOptionIndex():e},findLastFocusedOptionIndex:function(){var e=this.findLastSelectedOptionIndex();return e<0?this.findLastOptionIndex():e},searchOptions:function(e,i){var n=this;this.searchValue=(this.searchValue||"")+i;var l=-1;ke(this.searchValue)&&(this.focusedOptionIndex!==-1?(l=this.visibleOptions.slice(this.focusedOptionIndex).findIndex(function(o){return n.isOptionMatched(o)}),l=l===-1?this.visibleOptions.slice(0,this.focusedOptionIndex).findIndex(function(o){return n.isOptionMatched(o)}):l+this.focusedOptionIndex):l=this.visibleOptions.findIndex(function(o){return n.isOptionMatched(o)}),l===-1&&this.focusedOptionIndex===-1&&(l=this.findFirstFocusedOptionIndex()),l!==-1&&this.changeFocusedOptionIndex(e,l)),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(function(){n.searchValue="",n.searchTimeout=null},500)},removeOption:function(e){var i=this;return this.d_value.filter(function(n){return!et(n,i.getOptionValue(e),i.equalityKey)})},changeFocusedOptionIndex:function(e,i){this.focusedOptionIndex!==i&&(this.focusedOptionIndex=i,this.scrollInView(),this.selectOnFocus&&!this.multiple&&this.onOptionSelect(e,this.visibleOptions[i]))},scrollInView:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:-1;this.$nextTick(function(){var n=i!==-1?"".concat(e.$id,"_").concat(i):e.focusedOptionId,l=Gt(e.list,'li[id="'.concat(n,'"]'));l?l.scrollIntoView&&l.scrollIntoView({block:"nearest",inline:"nearest",behavior:"smooth"}):e.virtualScrollerDisabled||e.virtualScroller&&e.virtualScroller.scrollToIndex(i!==-1?i:e.focusedOptionIndex)})},autoUpdateModel:function(){this.selectOnFocus&&this.autoOptionFocus&&!this.$filled&&!this.multiple&&this.focused&&(this.focusedOptionIndex=this.findFirstFocusedOptionIndex(),this.onOptionSelect(null,this.visibleOptions[this.focusedOptionIndex]))},updateModel:function(e,i){this.writeValue(i,e),this.$emit("change",{originalEvent:e,value:i})},listRef:function(e,i){this.list=e,i&&i(e)},virtualScrollerRef:function(e){this.virtualScroller=e}},computed:{optionsListFlat:function(){return this.filterValue?Qe.filter(this.options,this.searchFields,this.filterValue,this.filterMatchMode,this.filterLocale):this.options},optionsListGroup:function(){var e=this,i=[];return(this.options||[]).forEach(function(n){var l=e.getOptionGroupChildren(n)||[],o=e.filterValue?Qe.filter(l,e.searchFields,e.filterValue,e.filterMatchMode,e.filterLocale):l;o!=null&&o.length&&i.push.apply(i,[{optionGroup:n,group:!0}].concat(Ee(o)))}),i},visibleOptions:function(){return this.optionGroupLabel?this.optionsListGroup:this.optionsListFlat},hasSelectedOption:function(){return ke(this.d_value)},equalityKey:function(){return this.optionValue?null:this.dataKey},searchFields:function(){return this.filterFields||[this.optionLabel]},filterResultMessageText:function(){return ke(this.visibleOptions)?this.filterMessageText.replaceAll("{0}",this.visibleOptions.length):this.emptyFilterMessageText},filterMessageText:function(){return this.filterMessage||this.$primevue.config.locale.searchMessage||""},emptyFilterMessageText:function(){return this.emptyFilterMessage||this.$primevue.config.locale.emptySearchMessage||this.$primevue.config.locale.emptyFilterMessage||""},emptyMessageText:function(){return this.emptyMessage||this.$primevue.config.locale.emptyMessage||""},selectionMessageText:function(){return this.selectionMessage||this.$primevue.config.locale.selectionMessage||""},emptySelectionMessageText:function(){return this.emptySelectionMessage||this.$primevue.config.locale.emptySelectionMessage||""},selectedMessageText:function(){return this.$filled?this.selectionMessageText.replaceAll("{0}",this.multiple?this.d_value.length:"1"):this.emptySelectionMessageText},focusedOptionId:function(){return this.focusedOptionIndex!==-1?"".concat(this.$id,"_").concat(this.focusedOptionIndex):null},ariaSetSize:function(){var e=this;return this.visibleOptions.filter(function(i){return!e.isOptionGroup(i)}).length},virtualScrollerDisabled:function(){return!this.virtualScrollerOptions},containerDataP:function(){return ft({invalid:this.$invalid,disabled:this.disabled})}},directives:{ripple:Nt},components:{InputText:gt,VirtualScroller:jt,InputIcon:Ut,IconField:Kt,SearchIcon:zt,CheckIcon:Rt,BlankIcon:Ft}},wo=["id","data-p"],ko=["tabindex"],_o=["id","aria-multiselectable","aria-label","aria-labelledby","aria-activedescendant","aria-disabled"],Co=["id"],Mo=["id","aria-label","aria-selected","aria-disabled","aria-setsize","aria-posinset","onClick","onMousedown","onMousemove","onDblclick","data-p-selected","data-p-focused","data-p-disabled"],Lo=["tabindex"];function Po(t,e,i,n,l,o){var s=ne("InputText"),p=ne("SearchIcon"),c=ne("InputIcon"),d=ne("IconField"),r=ne("CheckIcon"),I=ne("BlankIcon"),C=ne("VirtualScroller"),H=Xt("ripple");return y(),_("div",P({id:t.$id,class:t.cx("root"),onFocusout:e[7]||(e[7]=function(){return o.onFocusout&&o.onFocusout.apply(o,arguments)}),"data-p":o.containerDataP},t.ptmi("root")),[S("span",P({ref:"firstHiddenFocusableElement",role:"presentation","aria-hidden":"true",class:"p-hidden-accessible p-hidden-focusable",tabindex:t.disabled?-1:t.tabindex,onFocus:e[0]||(e[0]=function(){return o.onFirstHiddenFocus&&o.onFirstHiddenFocus.apply(o,arguments)})},t.ptm("hiddenFirstFocusableEl"),{"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0}),null,16,ko),t.$slots.header?(y(),_("div",{key:0,class:it(t.cx("header"))},[oe(t.$slots,"header",{value:t.d_value,options:o.visibleOptions})],2)):D("",!0),t.filter?(y(),_("div",P({key:1,class:t.cx("header")},t.ptm("header")),[v(d,{unstyled:t.unstyled,pt:t.ptm("pcFilterContainer")},{default:L(function(){return[v(s,{modelValue:l.filterValue,"onUpdate:modelValue":e[1]||(e[1]=function(V){return l.filterValue=V}),type:"text",class:it(t.cx("pcFilter")),placeholder:t.filterPlaceholder,role:"searchbox",autocomplete:"off",disabled:t.disabled,unstyled:t.unstyled,"aria-owns":t.$id+"_list","aria-activedescendant":o.focusedOptionId,tabindex:!t.disabled&&!l.focused?t.tabindex:-1,onInput:o.onFilterChange,onBlur:o.onFilterBlur,onKeydown:o.onFilterKeyDown,pt:t.ptm("pcFilter")},null,8,["modelValue","class","placeholder","disabled","unstyled","aria-owns","aria-activedescendant","tabindex","onInput","onBlur","onKeydown","pt"]),v(c,{unstyled:t.unstyled,pt:t.ptm("pcFilterIconContainer")},{default:L(function(){return[oe(t.$slots,"filtericon",{},function(){return[t.filterIcon?(y(),_("span",P({key:0,class:t.filterIcon},t.ptm("filterIcon")),null,16)):(y(),Z(p,Yt(P({key:1},t.ptm("filterIcon"))),null,16))]})]}),_:3},8,["unstyled","pt"])]}),_:3},8,["unstyled","pt"]),S("span",P({role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenFilterResult"),{"data-p-hidden-accessible":!0}),K(o.filterResultMessageText),17)],16)):D("",!0),S("div",P({class:t.cx("listContainer"),style:[{"max-height":o.virtualScrollerDisabled?t.scrollHeight:""},t.listStyle]},t.ptm("listContainer")),[v(C,P({ref:o.virtualScrollerRef},t.virtualScrollerOptions,{items:o.visibleOptions,style:[{height:t.scrollHeight},t.listStyle],tabindex:-1,disabled:o.virtualScrollerDisabled,pt:t.ptm("virtualScroller")}),Wt({content:L(function(V){var k=V.styleClass,w=V.contentRef,h=V.items,u=V.getItemOptions,g=V.contentStyle,x=V.itemSize;return[S("ul",P({ref:function(m){return o.listRef(m,w)},id:t.$id+"_list",class:[t.cx("list"),k],style:g,tabindex:-1,role:"listbox","aria-multiselectable":t.multiple,"aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,"aria-activedescendant":l.focused?o.focusedOptionId:void 0,"aria-disabled":t.disabled,onFocus:e[3]||(e[3]=function(){return o.onListFocus&&o.onListFocus.apply(o,arguments)}),onBlur:e[4]||(e[4]=function(){return o.onListBlur&&o.onListBlur.apply(o,arguments)}),onKeydown:e[5]||(e[5]=function(){return o.onListKeyDown&&o.onListKeyDown.apply(o,arguments)})},t.ptm("list")),[(y(!0),_(Ce,null,vt(h,function(f,m){return y(),_(Ce,{key:o.getOptionRenderKey(f,o.getOptionIndex(m,u))},[o.isOptionGroup(f)?(y(),_("li",P({key:0,id:t.$id+"_"+o.getOptionIndex(m,u),style:{height:x?x+"px":void 0},class:t.cx("optionGroup"),role:"option"},{ref_for:!0},t.ptm("optionGroup")),[oe(t.$slots,"optiongroup",{option:f.optionGroup,index:o.getOptionIndex(m,u)},function(){return[Y(K(o.getOptionGroupLabel(f.optionGroup)),1)]})],16,Co)):yt((y(),_("li",P({key:1,id:t.$id+"_"+o.getOptionIndex(m,u),style:{height:x?x+"px":void 0},class:t.cx("option",{option:f,index:m,getItemOptions:u}),role:"option","aria-label":o.getOptionLabel(f),"aria-selected":o.isSelected(f),"aria-disabled":o.isOptionDisabled(f),"aria-setsize":o.ariaSetSize,"aria-posinset":o.getAriaPosInset(o.getOptionIndex(m,u)),onClick:function(B){return o.onOptionSelect(B,f,o.getOptionIndex(m,u))},onMousedown:function(B){return o.onOptionMouseDown(B,o.getOptionIndex(m,u))},onMousemove:function(B){return o.onOptionMouseMove(B,o.getOptionIndex(m,u))},onTouchend:e[2]||(e[2]=function(T){return o.onOptionTouchEnd()}),onDblclick:function(B){return o.onOptionDblClick(B,f)}},{ref_for:!0},o.getPTOptions(f,u,m,"option"),{"data-p-selected":!t.checkmark&&o.isSelected(f),"data-p-focused":l.focusedOptionIndex===o.getOptionIndex(m,u),"data-p-disabled":o.isOptionDisabled(f)}),[t.checkmark?(y(),_(Ce,{key:0},[o.isSelected(f)?(y(),Z(r,P({key:0,class:t.cx("optionCheckIcon")},{ref_for:!0},t.ptm("optionCheckIcon")),null,16,["class"])):(y(),Z(I,P({key:1,class:t.cx("optionBlankIcon")},{ref_for:!0},t.ptm("optionBlankIcon")),null,16,["class"]))],64)):D("",!0),oe(t.$slots,"option",{option:f,selected:o.isSelected(f),index:o.getOptionIndex(m,u)},function(){return[Y(K(o.getOptionLabel(f)),1)]})],16,Mo)),[[H]])],64)}),128)),l.filterValue&&(!h||h&&h.length===0)?(y(),_("li",P({key:0,class:t.cx("emptyMessage"),role:"option"},t.ptm("emptyMessage")),[oe(t.$slots,"emptyfilter",{},function(){return[Y(K(o.emptyFilterMessageText),1)]})],16)):!t.options||t.options&&t.options.length===0?(y(),_("li",P({key:1,class:t.cx("emptyMessage"),role:"option"},t.ptm("emptyMessage")),[oe(t.$slots,"empty",{},function(){return[Y(K(o.emptyMessageText),1)]})],16)):D("",!0)],16,_o)]}),_:2},[t.$slots.loader?{name:"loader",fn:L(function(V){var k=V.options;return[oe(t.$slots,"loader",{options:k})]}),key:"0"}:void 0]),1040,["items","style","disabled","pt"])],16),oe(t.$slots,"footer",{value:t.d_value,options:o.visibleOptions}),!t.options||t.options&&t.options.length===0?(y(),_("span",P({key:2,role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenEmptyMessage"),{"data-p-hidden-accessible":!0}),K(o.emptyMessageText),17)):D("",!0),S("span",P({role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenSelectedMessage"),{"data-p-hidden-accessible":!0}),K(o.selectedMessageText),17),S("span",P({ref:"lastHiddenFocusableElement",role:"presentation","aria-hidden":"true",class:"p-hidden-accessible p-hidden-focusable",tabindex:t.disabled?-1:t.tabindex,onFocus:e[6]||(e[6]=function(){return o.onLastHiddenFocus&&o.onLastHiddenFocus.apply(o,arguments)})},t.ptm("hiddenLastFocusableEl"),{"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0}),null,16,Lo)],16,wo)}Xe.render=Po;var Be,at;function Vo(){if(at)return Be;at=1;var t=function(u){return e(u)&&!i(u)};function e(h){return!!h&&typeof h=="object"}function i(h){var u=Object.prototype.toString.call(h);return u==="[object RegExp]"||u==="[object Date]"||o(h)}var n=typeof Symbol=="function"&&Symbol.for,l=n?Symbol.for("react.element"):60103;function o(h){return h.$$typeof===l}function s(h){return Array.isArray(h)?[]:{}}function p(h,u){return u.clone!==!1&&u.isMergeableObject(h)?k(s(h),h,u):h}function c(h,u,g){return h.concat(u).map(function(x){return p(x,g)})}function d(h,u){if(!u.customMerge)return k;var g=u.customMerge(h);return typeof g=="function"?g:k}function r(h){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(h).filter(function(u){return Object.propertyIsEnumerable.call(h,u)}):[]}function I(h){return Object.keys(h).concat(r(h))}function C(h,u){try{return u in h}catch{return!1}}function H(h,u){return C(h,u)&&!(Object.hasOwnProperty.call(h,u)&&Object.propertyIsEnumerable.call(h,u))}function V(h,u,g){var x={};return g.isMergeableObject(h)&&I(h).forEach(function(f){x[f]=p(h[f],g)}),I(u).forEach(function(f){H(h,f)||(C(h,f)&&g.isMergeableObject(u[f])?x[f]=d(f,g)(h[f],u[f],g):x[f]=p(u[f],g))}),x}function k(h,u,g){g=g||{},g.arrayMerge=g.arrayMerge||c,g.isMergeableObject=g.isMergeableObject||t,g.cloneUnlessOtherwiseSpecified=p;var x=Array.isArray(u),f=Array.isArray(h),m=x===f;return m?x?g.arrayMerge(h,u,g):V(h,u,g):p(u,g)}k.all=function(u,g){if(!Array.isArray(u))throw new Error("first argument should be an array");return u.reduce(function(x,f){return k(x,f,g)},{})};var w=k;return Be=w,Be}var Ao=Vo();const To=Jt(Ao),He=A({BollingerBands:{main_plot:{bb_upperband:{color:"#008af4",type:"line",fill_to:"bb_lowerband"},bb_lowerband:{color:"#008af4",type:"line"}}},RSI:{subplots:{RSI:{rsi:{color:"#ff8000",type:"line"}}}},MACD:{subplots:{MACD:{macdsignal:{color:"#ff8000",type:"line"},macd:{color:"#1370f4",type:"line"}}}}});function rt(t,e){const i=ze(t);if(!e)return i;const n={};for(const o in t.main_plot){const s=e[o]||o;n[s]=t.main_plot[o],n[s].fill_to!==void 0&&(n[s].fill_to=e[n[s].fill_to]||n[s].fill_to)}"main_plot"in t&&(i.main_plot=n);const l={};for(const o in t.subplots){const s={};for(const p in t.subplots[o]){const c=e[p]||p;s[c]=t.subplots[o][p],s[c].fill_to!==void 0&&(s[c].fill_to=e[s[c].fill_to]||s[c].fill_to)}l[o]=s}return"subplots"in t&&(i.subplots=l),i}function Do(){function t(i){return He.value[i]||{}}function e(i,n,l={}){const o=t(i);if(!o)return n;const s=rt(o,l);return To(n,s)}return{plotTemplates:He,applyPlotTemplate:e,getTemplateContent:t,replaceTemplateColumns:rt,plotTemplateNames:$(()=>Object.keys(He.value))}}const $o={key:0,class:"pt-1"},Eo={key:0,class:"w-full"},Bo={key:1},Ho=["for"],Fo={class:"mt-2 flex gap-1 justify-end"},Ro=re({__name:"PlotFromTemplate",props:Qt({columns:{}},{visible:{type:Boolean},visibleModifiers:{}}),emits:["update:visible"],setup(t){const e=ei(t,"visible"),{plotTemplateNames:i,applyPlotTemplate:n,getTemplateContent:l}=Do(),o=Me();function s(){c.value&&(o.editablePlotConfig={...n(c.value,o.editablePlotConfig,d.value)},e.value=!1)}function p(){r.value=!r.value,d.value=ti(l(c.value)).reduce((I,C)=>(I[C]=C,I),{})}const c=A("");N(()=>e.value,I=>{I&&(c.value="",r.value=!1)});const d=A({}),r=A(!1);return(I,C)=>{const H=Xe,V=Oe,k=ht,w=Se,h=bi;return e.value?(y(),_("div",$o,[a(r)?(y(),_("div",Bo,[C[3]||(C[3]=S("h5",{class:"mt-1 text-center text-md mb-1"},"Re-map indicators",-1)),(y(!0),_(Ce,null,vt(Object.keys(a(d)),u=>(y(),_("div",{key:u,class:"flex gap-2 align-center"},[S("label",{for:`indicator-${u}`,class:"grow w-full"},K(u),9,Ho),v(V,{id:`indicator-${u}`,modelValue:a(d)[u],"onUpdate:modelValue":g=>a(d)[u]=g,class:"grow w-full",size:"small",options:I.columns},null,8,["id","modelValue","onUpdate:modelValue","options"])]))),128))])):(y(),_("div",Eo,[C[2]||(C[2]=S("label",{for:"selectTemplate"},"Select Template",-1)),v(H,{id:"selectTemplate",modelValue:a(c),"onUpdate:modelValue":C[0]||(C[0]=u=>X(c)?c.value=u:null),options:a(i),"select-size":4},null,8,["modelValue","options"])])),S("div",Fo,[v(w,{size:"small",title:"Abort",severity:"secondary",onClick:C[1]||(C[1]=u=>e.value=!1)},{default:L(()=>[v(k)]),_:1}),a(r)?D("",!0):(y(),Z(w,{key:0,disabled:!a(c),size:"small",style:{width:"33%"},title:"Use template",label:" Use Template",severity:"primary",onClick:p},{icon:L(()=>[v(h,{class:"me-1"})]),_:1},8,["disabled"])),a(r)?(y(),Z(w,{key:1,disabled:!a(c),size:"small",style:{width:"33%"},title:"Apply template",severity:"primary",onClick:s},{icon:L(()=>[v(h,{class:"me-1"})]),default:L(()=>[C[4]||(C[4]=Y(" Apply Template "))]),_:1,__:[4]},8,["disabled"])):D("",!0)])])):D("",!0)}}}),_t=re({__name:"PlotConfigSelect",props:{allowEdit:{type:Boolean,default:!1},editableName:{type:String,default:"plot configuration"}},setup(t){const e=Me();return(i,n)=>{const l=Oe,o=St;return y(),Z(o,{modelValue:a(e).plotConfigName,"onUpdate:modelValue":n[1]||(n[1]=s=>a(e).plotConfigName=s),"allow-edit":t.allowEdit,"allow-add":t.allowEdit,"allow-duplicate":t.allowEdit,"editable-name":"plot configuration",onRename:a(e).renamePlotConfig,onDelete:a(e).deletePlotConfig,onNew:a(e).newPlotConfig,onDuplicate:a(e).duplicatePlotConfig},{default:L(()=>[v(l,{id:"plotConfigSelect",modelValue:a(e).plotConfigName,"onUpdate:modelValue":[n[0]||(n[0]=s=>a(e).plotConfigName=s),a(e).plotConfigChanged],options:a(e).availablePlotConfigNames,class:"w-full text-left",size:"small"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1},8,["modelValue","allow-edit","allow-add","allow-duplicate","onRename","onDelete","onNew","onDuplicate"])}}}),zo={key:0},Ko={class:"flex flex-row mt-1 gap-1"},Uo={class:"flex flex-row gap-1"},jo={key:3,class:"w-full ms-1 mt-2"},No=re({__name:"PlotConfigurator",props:{columns:{required:!0,type:Array},isVisible:{required:!1,default:!1,type:Boolean}},setup(t){const e=t,i=Me(),n=xt(),l=A("default"),o=A(""),s=A(!1),p=A(!1),c=A("main_plot"),d=A(),r=A(!0),I=$(()=>c.value==="main_plot"),C=$(()=>I.value?i.editablePlotConfig.main_plot:i.editablePlotConfig.subplots[c.value]),H=$(()=>["main_plot",...Object.keys(i.editablePlotConfig.subplots)]),V=$(()=>{let M=[];return I.value&&(M=Object.keys(i.editablePlotConfig.main_plot)),c.value in i.editablePlotConfig.subplots&&(M=Object.keys(i.editablePlotConfig.subplots[c.value])),M.map(b=>({value:b,text:e.columns.includes(b)?b:`${b} <-- not available in this chart`}))});function k(M){const b=Object.keys(M)[0],W=M[b];I.value?i.editablePlotConfig.main_plot[b]={...W}:i.editablePlotConfig.subplots[c.value][b]={...W},i.editablePlotConfig={...i.editablePlotConfig},s.value=!1}const w=$({get(){return s.value?{}:o.value?{[o.value]:C.value[o.value]}:{}},set(M){Object.keys(M)[0]&&M?k(M):s.value=!1}}),h=$({get(){return JSON.stringify(i.editablePlotConfig,null,2)},set(M){try{d.value=JSON.parse(M),r.value=!0}catch{r.value=!1}}});function u(){I.value?(console.log(`Removing ${o.value} from MainPlot`),delete i.editablePlotConfig.main_plot[o.value]):(console.log(`Removing ${o.value} from ${c.value}`),delete i.editablePlotConfig.subplots[c.value][o.value]),i.editablePlotConfig={...i.editablePlotConfig},o.value=""}function g(){s.value=!s.value,o.value=""}function x(M){i.editablePlotConfig.subplots={...i.editablePlotConfig.subplots,[M]:{}},c.value=M}function f(M){delete i.editablePlotConfig.subplots[M],i.editablePlotConfig={...i.editablePlotConfig},c.value=H.value[H.value.length-1]}function m(M,b){i.editablePlotConfig.subplots[b]=i.editablePlotConfig.subplots[M],delete i.editablePlotConfig.subplots[M],c.value=b}function T(){i.editablePlotConfig=ze(i.customPlotConfigs[i.plotConfigName])}function B(){d.value!==void 0&&r.value&&(i.editablePlotConfig=d.value)}async function G(){if(n.activeBot.isWebserverMode&&!n.activeBot.strategy.strategy){ot("No strategy selected, can't load plot config.");return}try{await n.activeBot.getStrategyPlotConfig(),n.activeBot.strategyPlotConfig&&(i.editablePlotConfig=n.activeBot.strategyPlotConfig)}catch{ot("Failed to load Plot configuration from Strategy.")}}function E(){i.saveCustomPlotConfig(l.value,i.editablePlotConfig)}function z(M){s.value=!1,M&&(k({[M]:{color:ve()}}),o.value=M)}N(c,()=>{o.value=""}),N(()=>i.plotConfigName,()=>{o.value="",l.value=i.plotConfigName}),N(()=>e.isVisible,()=>{e.isVisible?(i.editablePlotConfig=ze(i.plotConfig),i.isEditing=!0,l.value=i.plotConfigName):i.isEditing=!1},{immediate:!0});const R=A(!1);return(M,b)=>{const W=_t,U=ii,te=Xe,J=St,Q=Se,ce=wt,Ie=Ro,Le=mo,we=It;return t.columns?(y(),_("div",zo,[b[16]||(b[16]=S("label",{for:"idPlotConfigName"},"Plot config name",-1)),v(W,{"allow-edit":""}),v(U),b[17]||(b[17]=S("label",{for:"fieldSel",class:"mb"},"Target Plot",-1)),v(J,{modelValue:a(c),"onUpdate:modelValue":b[1]||(b[1]=j=>X(c)?c.value=j:null),"allow-edit":!a(I),"allow-add":"","editable-name":"plot configuration","align-vertical":"",onNew:x,onDelete:f,onRename:m},{default:L(()=>[v(te,{id:"fieldSel",modelValue:a(c),"onUpdate:modelValue":b[0]||(b[0]=j=>X(c)?c.value=j:null),options:a(H),size:"small",pt:{list:{class:"h-30"}}},null,8,["modelValue","options"])]),_:1},8,["modelValue","allow-edit"]),v(U),S("div",null,[b[8]||(b[8]=S("label",{for:"selectedIndicators"},"Indicators in this plot",-1)),v(te,{id:"selectedIndicators",modelValue:a(o),"onUpdate:modelValue":b[2]||(b[2]=j=>X(o)?o.value=j:null),size:"small","empty-message":"No indicators selected","option-label":"text","option-value":"value",disabled:a(s),options:a(V),pt:{list:{class:"h-30"}}},null,8,["modelValue","disabled","options"])]),S("div",Ko,[v(Q,{severity:"secondary",title:"Remove indicator to plot",size:"small",disabled:!a(o),class:"col",onClick:u},{default:L(()=>b[9]||(b[9]=[Y(" Remove indicator ")])),_:1,__:[9]},8,["disabled"]),v(Q,{severity:"secondary",title:"Load indicator config from template",size:"small",onClick:b[3]||(b[3]=j=>R.value=!a(R))},{default:L(()=>b[10]||(b[10]=[Y(" Indicator from template ")])),_:1,__:[10]}),v(Q,{severity:"primary",title:"Add indicator to plot",size:"small",class:"col",disabled:a(s),onClick:g},{default:L(()=>b[11]||(b[11]=[Y(" Add new indicator ")])),_:1,__:[11]},8,["disabled"])]),a(s)?(y(),Z(ce,{key:0,columns:t.columns,class:"mt-1",label:"Select indicator to add",onIndicatorSelected:z},null,8,["columns"])):D("",!0),v(Ie,{visible:a(R),"onUpdate:visible":b[4]||(b[4]=j=>X(R)?R.value=j:null),columns:t.columns},null,8,["visible","columns"]),a(o)&&!a(R)?(y(),Z(Le,{key:1,modelValue:a(w),"onUpdate:modelValue":b[5]||(b[5]=j=>X(w)?w.value=j:null),class:"mt-1",columns:t.columns},null,8,["modelValue","columns"])):D("",!0),v(U),S("div",Uo,[v(Q,{severity:"secondary",size:"small",disabled:a(s),title:"Reset to last saved configuration",onClick:T},{default:L(()=>b[12]||(b[12]=[Y("Reset")])),_:1,__:[12]},8,["disabled"]),v(Q,{disabled:a(n).activeBot.isWebserverMode&&a(n).activeBot.botApiVersion<2.23||!a(n).activeBot.isBotOnline||a(s),severity:"secondary",size:"small",onClick:G},{default:L(()=>b[13]||(b[13]=[Y(" From strategy ")])),_:1,__:[13]},8,["disabled"]),v(Q,{id:"showButton",severity:"secondary",size:"small",disabled:a(s),title:"Show configuration for easy transfer to a strategy",onClick:b[6]||(b[6]=j=>p.value=!a(p))},{default:L(()=>[Y(K(a(p)?"Hide":"Show"),1)]),_:1},8,["disabled"]),v(Q,{severity:"primary",size:"small","data-toggle":"tooltip",disabled:a(s),title:"Save configuration",onClick:E},{default:L(()=>b[14]||(b[14]=[Y("Save")])),_:1,__:[14]},8,["disabled"])]),a(p)?(y(),Z(Q,{key:2,class:"ms-1 mt-1",severity:"secondary",size:"small",title:"Load configuration from text box below",onClick:B},{default:L(()=>b[15]||(b[15]=[Y("Load from String")])),_:1,__:[15]})):D("",!0),a(p)?(y(),_("div",jo,[v(we,{id:"TextArea",modelValue:a(h),"onUpdate:modelValue":b[7]||(b[7]=j=>X(h)?h.value=j:null),class:"w-full min-h-[250px]",size:"small",state:a(r)},null,8,["modelValue","state"])])):D("",!0)])):D("",!0)}}}),Go=qe(No,[["__scopeId","data-v-ced5cfea"]]);function Zo(t,e,i,n){const l=t.indexOf(i),o=t.indexOf(n),s=l>0&&o>0;return s&&t.push(`${i}-${n}`),e.map(p=>{const c=p.slice();if(s){const d=c===null||c[o]===null||c[l]===null?null:c[o]-c[l];c.push(d)}return c})}function qo(t){const e=[];return"main_plot"in t&&Object.entries(t.main_plot).forEach(([i,n])=>{n.fill_to&&e.push([i,n.fill_to])}),"subplots"in t&&Object.values(t.subplots).forEach(i=>{Object.entries(i).forEach(([n,l])=>{l.fill_to&&e.push([n,l.fill_to])})}),e}const Xo=2,Yo=3,Ct=4;function ae(t,e,i=Yo){const n=e%t;let l=i===Xo;return i===Ct&&(l=n>t/2),e-n+(l?t:0)}function Wo(t,e,i){const n=oi("Shift",{events:["keydown","keyup"]}),l=$(()=>e.value==="dark"?"white":"black"),o=A({x:0,y:0}),s=144,p=A(!0),c=A(!1),d=A(!1),r=35,I=170;function C(u){return ae(i.value,u,Ct)}function H(u){p.value&&!d.value&&h(u.offsetX,u.offsetY)}function V(u){var g,x,f,m,T,B,G,E;if(c.value)d.value?(w(),d.value=!1,(B=(T=t.value)==null?void 0:T.chart)==null||B.getZr().off("mousemove",H),(E=(G=t.value)==null?void 0:G.chart)==null||E.getZr().off("mousedown",V)):d.value=!0;else{const z=((g=t.value)==null?void 0:g.convertFromPixel({seriesIndex:0},[u.offsetX,u.offsetY]))??[0,0];z[0]=C(Number(z[0]));const R=((x=t.value)==null?void 0:x.convertToPixel({seriesIndex:0},z))??[0,0];o.value={x:R[0],y:R[1]},(m=(f=t.value)==null?void 0:f.chart)==null||m.getZr().on("mousemove",H),k()}}function k(){var u;c.value=!0,(u=t.value)==null||u.setOption({dataZoom:[{disabled:!0}],graphic:[{type:"rect",shape:{x:o.value.x,width:0,y:o.value.y,height:0},style:{fill:l.value,opacity:.1}},{type:"rect",z:5,shape:{x:o.value.x,width:I,y:o.value.y,height:r,r:5},style:{fill:"#002fff",opacity:.8},textContent:{z:10,style:{text:"0 bars - 0%",font:"14px sans-serif",fill:"white"}},textConfig:{position:"inside"}}]})}function w(){var u;c.value=!1,(u=t.value)==null||u.setOption({dataZoom:[{disabled:!1}],graphic:[{$action:"remove"},{$action:"remove"}]})}function h(u,g){var b,W,U,te;const x=((b=t.value)==null?void 0:b.convertFromPixel({seriesIndex:0},[o.value.x,o.value.y]))??[0,0],f=((W=t.value)==null?void 0:W.convertFromPixel({seriesIndex:0},[u,g]))??[0,0],m=Number(x[1]),T=Number(f[1]),B=C(Number(x[0])),G=C(Number(f[0])),E=Math.abs(B-G),z=((U=t.value)==null?void 0:U.convertToPixel({seriesIndex:0},[G,0])[0])??0,R=ni(E,{units:["d","h","m"]}),M=Math.abs((m-T)/m*100).toFixed(2);(te=t.value)==null||te.setOption({graphic:[{shape:{width:z>o.value.x?-1*(o.value.x-z):z-o.value.x,height:g>o.value.y?-1*(o.value.y-g):g-o.value.y}},{shape:{x:o.value.x+(z-o.value.x)/2-I/2,y:g>o.value.y?g-(r+5):g+9},textContent:{style:{textAlign:u<o.value.x?"left":"right",text:`${E/i.value} bars (${m<T?M:"-"+M}%) 
 ${R}`}}}]}),p.value=!1,setTimeout(()=>{p.value=!0},1e3/s)}N(()=>n.value,()=>{var u,g;n.value&&!c.value&&((g=(u=t.value)==null?void 0:u.chart)==null||g.getZr().on("mousedown",V))})}function Jo(t,e){const i=t.indexOf("open"),n=t.indexOf("close"),l=t.indexOf("high"),o=t.indexOf("low");let s;return e.map((p,c)=>{const d=p.slice(),r=c===0?(d[i]+d[n])/2:(s[i]+s[n])/2,I=(d[i]+d[l]+d[o]+d[n])/4,C=Math.max(d[l],d[i],d[n]),H=Math.min(d[o],d[i],d[n]);return d[i]=r,d[n]=I,d[l]=C,d[o]=H,s=d.slice(),d})}function Qo(t){if(!t)return{baseCurrency:"",quoteCurrency:""};const[e,i]=t.split("/");if(i!==void 0){const n=i.split(":");return{baseCurrency:e,quoteCurrency:n[0]||i}}return{baseCurrency:i??"",quoteCurrency:e??""}}function Mt(t,e){return`${t.ft_order_side==="buy"?"+":"-"}${Ke("cost"in t?t.cost:t.amount*t.safe_price,e)}`}function ut(t,e,i,n){let l=`${t.is_short?"Short":"Long"} ${i}
  ${Ot(t.profit_ratio)} ${t.profit_abs?"("+Ke(t.profit_abs,n)+")":""}
  ${Mt(e,n)}
  Enter-tag: ${t.enter_tag??""}
  Order Price: ${Ke(e.safe_price,n)}`;return l+=`${"ft_order_tag"in e&&e.ft_order_tag&&t.enter_tag!=e.ft_order_tag?`
Order-Tag: `+e.ft_order_tag:""}`,l+=`${t.exit_reason?`
Exit-Tag: `+t.exit_reason:""}`,l}function en(t,e,i){let n=`${t.is_short?"Short":"Long"} adjustment
  ${Mt(e,i)}
  Enter-tag: ${t.enter_tag??""}`;return n+=`${"ft_order_tag"in e&&e.ft_order_tag?`
Order-Tag: `+e.ft_order_tag:""}`,n}const tn="path://m 52.444161,104.1909 8.386653,25.34314 8.386651,25.34313 -16.731501,0.0422 -16.731501,0.0422 8.344848,-25.38539 z m 0.08656,-48.368126 8.386652,25.343139 8.386652,25.343137 -16.731501,0.0422 -16.731502,0.0422 8.344848,-25.385389 z",dt="path://m 102.20764,19.885384 h 24.1454 v 41.928829 h -24.1454 z m 12.17344,36.423813 8.38665,25.343139 8.38666,25.343134 -16.7315,0.0422 -16.731507,0.0422 8.344847,-25.385386 z",Fe="#AD00FF",Re="#0066FF";function on(t,e){const i=[],n=t.data_stop_ts+t.timeframe_ms;for(let l=0,o=e.length;l<o;l+=1){const s=e[l],p=s.open_fill_timestamp??s.open_timestamp;if((ae(t.timeframe_ms??0,s.open_timestamp)<=n||!s.close_timestamp||s.close_timestamp&&s.close_timestamp>=t.data_start_ts)&&s.orders)for(let c=0;c<s.orders.length;c++){const d=s.orders[c],r=d.order_filled_timestamp??("order_timestamp"in d?d.order_timestamp:s.open_timestamp),{quoteCurrency:I}=Qo(s.quote_currency??s.pair??"");r&&ae(t.timeframe_ms??0,r)<=n&&r>t.data_start_ts&&(c===0?i.push([ae(t.timeframe_ms??0,p),d.safe_price,dt,d.ft_order_side=="sell"?180:0,s.is_short?Fe:Re,(s.is_short?"Short":"Long")+(d.order_filled_timestamp?"":" (open)"),ut(s,d,"entry",I)]):c===s.orders.length-1&&s.close_timestamp?ae(t.timeframe_ms??0,s.close_timestamp)<=n&&s.close_timestamp>t.data_start_ts&&s.is_open===!1&&i.push([ae(t.timeframe_ms??0,s.close_timestamp),d.safe_price,dt,s.is_short?0:180,s.is_short?Fe:Re,Ot(s.profit_ratio,2),ut(s,d,"exit",I)]):(d.ft_order_side!=="stoploss"||"filled"in d&&(d.filled??0)>0)&&i.push([ae(t.timeframe_ms??0,r),d.safe_price,tn,d.ft_order_side=="sell"?180:0,s.is_short?Fe:Re,"",en(s,d,I)]))}}return{tradeData:i}}function nn(t,e,i,n){const{tradeData:l}=on(i,n),o=n.find(p=>p.is_open),s={name:t,type:"scatter",xAxisIndex:0,yAxisIndex:0,encode:{x:0,y:1,label:5,tooltip:6},label:{show:!0,fontSize:12,backgroundColor:e!=="dark"?"#fff":"#000",padding:2,color:e==="dark"?"#fff":"#000",rotate:75,offset:[10,0],align:"left"},itemStyle:{color:p=>p.data?p.data[4]:"#000",opacity:.9},symbol:p=>p[2],symbolRotate:p=>p[3],symbolSize:13,data:l};if(o){const p=i.timeframe_ms*10;s.markLine={symbol:"none",label:{show:!0,position:"middle"},data:[[{name:"Stoploss",yAxis:o.stop_loss_abs,lineStyle:{color:"#ff0000AA",type:"solid"},xAxis:i.data_stop_ts-p>o.open_timestamp?o.open_timestamp:i.data_stop_ts-p},{lineStyle:{color:"#ff0000AA",type:"solid"},yAxis:o.stop_loss_abs,xAxis:o.close_timestamp??i.data_stop_ts+i.timeframe_ms}]]}}return s}function ln(t,e){return!t.annotations||!e?{}:{markArea:{label:{position:"insideTop"},data:t.annotations.filter(n=>n.type=="area").map(n=>[{xAxis:n.start,yAxis:n.y_start,itemStyle:{color:n.color},label:{formatter:n.label}},{xAxis:n.end,yAxis:n.y_end}])}}}function je(t,e,i,n,l=0){const o={name:i,type:n.type||"line",xAxisIndex:l,yAxisIndex:l,itemStyle:{color:n.color||ve()},encode:{x:t,y:e},showSymbol:!1};return n.type===ee.scatter&&(o.symbolSize=n.scatterSymbolSize??3,o.emphasis={disabled:!0}),o}function ct(t,e,i,n,l=0){const o={type:ee.line},s=je(t,e,i,o,l),p={stack:i,stackStrategy:"all",lineStyle:{opacity:0},showSymbol:!1,areaStyle:{color:n.color,opacity:.1},tooltip:{show:!1}};return Object.assign(s,p),s}const sn={class:"h-full w-full"},pt=55,ge=8,an="#00ff26",rn="#00ff26",un="#faba25",dn="#faba25",cn=re({__name:"CandleChart",props:{trades:{},dataset:{},heikinAshi:{type:Boolean},showMarkArea:{type:Boolean},useUTC:{type:Boolean},plotConfig:{},theme:{},sliderPosition:{},colorUp:{},colorDown:{},labelSide:{}},setup(t){gi([yi,ki,xi,_i,Ci,Oi,Mi,Si,Li,Ii,Pi,Vi,Ai,Ti,Di,$i,Ei,Bi,wi,Hi]);const e=t,i=$(()=>e.labelSide==="left"),n=i.value?"5.5%":"1%",l=i.value?"1%":"5.5%",o=e.colorUp,s=e.colorUp,p=e.colorDown,c=e.colorDown,d=A(),r=A({}),I=$(()=>e.dataset?e.dataset.strategy:""),C=$(()=>e.dataset?e.dataset.pair:""),H=$(()=>e.dataset?e.dataset.timeframe:""),V=$(()=>e.dataset!==null&&typeof e.dataset=="object"),k=$(()=>e.trades.filter(f=>f.pair===C.value)),w=$(()=>`${I.value} - ${C.value} - ${H.value}`),h=$(()=>qo(e.plotConfig));Wo(d,nt(()=>e.theme),nt(()=>e.dataset.timeframe_ms));function u(f=!1){var we,j,Ye,We;if(!V.value)return;(we=r.value)!=null&&we.title&&(r.value.title[0].text=w.value);const m=e.dataset.columns.slice(),T=m.findIndex(O=>O==="__date_ts"),B=m.findIndex(O=>O==="open"),G=m.findIndex(O=>O==="high"),E=m.findIndex(O=>O==="low"),z=m.findIndex(O=>O==="close"),R=m.findIndex(O=>O==="volume"),M=m.findIndex(O=>O==="_buy_signal_close"||O==="_enter_long_signal_close"),b=m.findIndex(O=>O==="_sell_signal_close"||O==="_exit_long_signal_close"),W=m.findIndex(O=>O==="_enter_short_signal_close"),U=m.findIndex(O=>O==="_exit_short_signal_close"),te="subplots"in e.plotConfig?Object.keys(e.plotConfig.subplots).length+1:1;if(Array.isArray((j=r.value)==null?void 0:j.dataZoom))if(f){const O=(1-250/e.dataset.length)*100;r.value.dataZoom.forEach((F,ie)=>{r.value&&r.value.dataZoom&&(r.value.dataZoom[ie].start=O)})}else r.value.dataZoom.forEach((O,F)=>{r.value&&r.value.dataZoom&&(delete r.value.dataZoom[F].start,delete r.value.dataZoom[F].end)});let J=e.heikinAshi?Jo(m,e.dataset.data):e.dataset.data.slice();h.value.forEach(([O,F])=>{J=Zo(m,J,O,F)});const Q=Array(J.length>0?J[J.length-2].length:0);Q[T]=J[J.length-1][T]+e.dataset.timeframe_ms*3,J.push(Q);const ce={dataset:{source:J},grid:[{left:n,right:l,bottom:`${te*ge+2}%`},{left:n,right:l,bottom:`${te*ge}%`,height:`${ge}%`}],series:[{name:"Candles",type:"candlestick",barWidth:"80%",itemStyle:{color:o,color0:p,borderColor:s,borderColor0:c},encode:{x:T,y:[B,z,E,G]},...ln(e.dataset,e.showMarkArea)},{name:"Volume",type:"bar",xAxisIndex:1,yAxisIndex:1,itemStyle:{color:"#777777"},large:!0,encode:{x:T,y:R}}]};if(Array.isArray(ce.series)){const O=[{colData:M,name:"Entry",symbol:"triangle",symbolSize:10,color:an,tooltipPrefix:"Long entry"},{colData:b,name:"Exit",symbol:"diamond",symbolSize:8,color:un,tooltipPrefix:"Long exit"},{colData:W,name:"Entry",symbol:"triangle",symbolSize:10,symbolRotate:180,color:rn,tooltipPrefix:"Short entry"},{colData:U,name:"Exit",symbol:"pin",symbolSize:8,color:dn,tooltipPrefix:"Short exit"}];for(const F of O)F.colData>=0&&ce.series.push({name:F.name,type:"scatter",symbol:F.symbol,symbolSize:F.symbolSize,symbolRotate:F.symbolRotate??0,xAxisIndex:0,yAxisIndex:0,itemStyle:{color:F.color},tooltip:{valueFormatter:ie=>ie?`${F.tooltipPrefix} ${ie}`:""},encode:{x:T,y:F.colData}})}if(Object.assign(r.value,ce),"main_plot"in e.plotConfig&&Object.entries(e.plotConfig.main_plot).forEach(([O,F])=>{var pe,q,le,fe,he,me,be;const ie=m.findIndex(se=>se===O);if(ie>0){if(!Array.isArray((pe=r.value)==null?void 0:pe.legend)&&((le=(q=r.value)==null?void 0:q.legend)!=null&&le.data)&&r.value.legend.data.push(O),Array.isArray((fe=r.value)==null?void 0:fe.series)){if((he=r.value)==null||he.series.push(je(T,ie,O,F)),F.fill_to){const se=`${O}-${F.fill_to}`,Pe=m.findIndex(Te=>Te===se),Ve={color:F.color,type:ee.line},Ae=ct(T,Pe,O,Ve,0);r.value.series[r.value.series.length-1].stack=O,r.value.series.push(Ae)}(be=r.value)==null||be.series.splice(((me=r.value)==null?void 0:me.series.length)-1,0)}}else console.log(`element ${O} for main plot not found in columns.`)}),"subplots"in e.plotConfig){let O=2;Object.entries(e.plotConfig.subplots).forEach(([F,ie])=>{const pe=O;Array.isArray(r.value.yAxis)&&r.value.yAxis.length<=O&&r.value.yAxis.push({scale:!0,gridIndex:pe,name:F,position:e.labelSide,nameLocation:"middle",nameGap:pt,axisLabel:{show:!0,hideOverlap:!0},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}}),Array.isArray(r.value.xAxis)&&r.value.xAxis.length<=O&&r.value.xAxis.push({type:"time",gridIndex:pe,axisLine:{onZero:!1},axisTick:{show:!1},axisLabel:{show:!1},axisPointer:{label:{show:!1}},splitLine:{show:!1},splitNumber:20}),Array.isArray(r.value.dataZoom)&&r.value.dataZoom.forEach(q=>q.xAxisIndex&&Array.isArray(q.xAxisIndex)?q.xAxisIndex.push(O):null),r.value.grid&&Array.isArray(r.value.grid)&&r.value.grid.push({left:n,right:l,bottom:`${(te-O+1)*ge}%`,height:`${ge}%`}),Object.entries(ie).forEach(([q,le])=>{var he,me,be;const fe=m.findIndex(se=>se===q);if(fe>0){if(!Array.isArray(r.value.legend)&&((he=r.value.legend)!=null&&he.data)&&r.value.legend.data.push(q),r.value.series&&Array.isArray(r.value.series)){if(r.value.series.push(je(T,fe,q,le,O)),le.fill_to){const se=`${q}-${le.fill_to}`,Pe=m.findIndex(Te=>Te===se),Ve={color:le.color,type:ee.line},Ae=ct(T,Pe,q,Ve,O);r.value.series[r.value.series.length-1].stack=q,r.value.series.push(Ae)}(be=r.value)==null||be.series.splice(((me=r.value)==null?void 0:me.series.length)-1,0)}}else console.log(`element ${q} was not found in the columns.`)}),O+=1})}Array.isArray(r.value.grid)&&(r.value.grid[r.value.grid.length-1].bottom="50px",delete r.value.grid[r.value.grid.length-1].top);const Ie="Trades";!Array.isArray(r.value.legend)&&((Ye=r.value.legend)!=null&&Ye.data)&&r.value.legend.data.splice(4,0,Ie);const Le=nn(Ie,e.theme,e.dataset,k.value);Array.isArray(r.value.series)&&r.value.series.push(Le),(We=d.value)==null||We.setOption(r.value,{replaceMerge:["series","grid","yAxis","xAxis","legend"],notMerge:f})}function g(){var f;(f=d.value)==null||f.setOption({},{notMerge:!0}),r.value={title:[{show:!1}],backgroundColor:"rgba(0, 0, 0, 0)",useUTC:e.useUTC,animation:!1,legend:{data:["Candles","Volume","Entry","Exit"],right:"1%",type:"scroll",pageTextStyle:{color:e.theme==="dark"?"#dedede":"#333"},pageIconColor:e.theme==="dark"?"#aaa":"#2f4554",pageIconInactiveColor:e.theme==="dark"?"#2f4554":"#aaa"},tooltip:{show:!0,trigger:"axis",renderMode:"richText",backgroundColor:"rgba(80,80,80,0.7)",borderWidth:0,textStyle:{color:"#fff"},axisPointer:{type:"cross",lineStyle:{color:"#cccccc",width:1,opacity:1}},position(m,T,B,G,E){const z={top:60},R=m[0]<E.viewSize[0]/2;return z[["left","right"][+R]]=R?5:60,z}},axisPointer:{link:[{xAxisIndex:"all"}],label:{backgroundColor:"#777"}},xAxis:[{type:"time",axisLine:{onZero:!1},axisTick:{show:!0},axisLabel:{show:!0},axisPointer:{label:{show:!1}},position:"top",splitLine:{show:!1},splitNumber:20,min:"dataMin",max:"dataMax"},{type:"time",gridIndex:1,axisLine:{onZero:!1},axisTick:{show:!1},axisLabel:{show:!1},axisPointer:{label:{show:!1}},splitLine:{show:!1},splitNumber:20,min:"dataMin",max:"dataMax"}],yAxis:[{scale:!0,max:m=>m.max+(m.max-m.min)*.02,min:m=>m.min-(m.max-m.min)*.04,position:e.labelSide},{scale:!0,gridIndex:1,splitNumber:2,name:"volume",nameLocation:"middle",position:e.labelSide,nameGap:pt,axisLabel:{show:!1},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}}],dataZoom:[{type:"inside",xAxisIndex:[0,1],start:80,end:100},{xAxisIndex:[0,1],bottom:10,start:80,end:100,...Fi}]},console.log("Initialized"),u(!0)}function x(){if(!e.sliderPosition)return;const f=e.sliderPosition.startValue-e.dataset.timeframe_ms*40,m=e.sliderPosition.endValue?e.sliderPosition.endValue+e.dataset.timeframe_ms*40:e.sliderPosition.startValue+e.dataset.timeframe_ms*80;d.value&&d.value.dispatchAction({type:"dataZoom",dataZoomIndex:0,startValue:f,endValue:m})}return Ze(()=>{g()}),N([()=>e.useUTC,()=>e.theme,()=>e.plotConfig],()=>g()),N([()=>e.dataset,()=>e.heikinAshi,()=>e.showMarkArea],()=>u()),N(()=>e.sliderPosition,()=>x()),(f,m)=>(y(),_("div",sn,[a(V)?(y(),Z(a(vi),{key:0,ref_key:"candleChart",ref:d,theme:f.theme,autoresize:"","manual-update":""},null,8,["theme"])):D("",!0)]))}}),pn=qe(cn,[["__scopeId","data-v-bad10481"]]),fn={class:"flex h-full"},hn={class:"flex-fill w-full flex-col align-items-stretch flex h-full"},mn={class:"flex me-0"},bn={class:"ms-1 md:ms-2 flex flex-wrap md:flex-nowrap items-center gap-1"},gn={class:"md:ms-2 text-nowrap"},vn={class:"flex flex-col"},yn={class:"flex flex-row flex-wrap"},xn={key:0,class:"ms-2 text-sm text-nowrap",title:"Long entry signals"},On={key:1,class:"ms-2 text-sm text-nowrap",title:"Long exit signals"},Sn={class:"flex flex-row flex-wrap"},In={key:0,class:"ms-2 text-sm text-nowrap"},wn={key:1,class:"ms-2 text-sm text-nowrap"},kn={class:"ms-auto flex items-center gap-2"},_n={class:"me-0 md:me-1"},Cn={class:"h-full flex"},Mn={class:"min-w-0 w-full shrink"},Ln={key:1,class:"m-auto"},Pn={key:1,class:"text-lg"},Vn={key:2},An={key:0,class:"grow border rounded-md ps-1 border-surface-300 dark:border-surface-700"},Tn=re({__name:"CandleChartContainer",props:{trades:{default:()=>[]},availablePairs:{},timeframe:{},historicView:{type:Boolean,default:!1},reloadDataOnSwitch:{type:Boolean,default:!1},plotConfigModal:{type:Boolean,default:!0},strategy:{default:""},sliderPosition:{default:void 0}},emits:["refreshData"],setup(t,{emit:e}){const i=t,n=e,l=li(),o=si(),s=xt(),p=Me(),c=A(),d=$(()=>{var x,f;return i.historicView?(x=s.activeBot.history[`${s.activeBot.plotPair}__${i.timeframe}`])==null?void 0:x.data:(f=s.activeBot.candleData[`${s.activeBot.plotPair}__${i.timeframe}`])==null?void 0:f.data}),r=$(()=>{var x;return i.strategy||((x=d.value)==null?void 0:x.strategy)||""}),I=$(()=>d.value?d.value.all_columns??d.value.columns:[]),C=$(()=>d.value?d.value.columns??d.value.all_columns:[]),H=$(()=>d.value&&d.value.data.length>0),V=$(()=>i.historicView?s.activeBot.historyStatus===de.loading:s.activeBot.candleDataStatus===de.loading),k=$(()=>{switch(i.historicView?s.activeBot.historyStatus:s.activeBot.candleDataStatus){case de.not_loaded:return"Not loaded yet.";case de.loading:return"Loading...";case de.success:return"No data available";case de.error:return"Failed to load data";default:return"Unknown"}}),w=A(!1);function h(){i.plotConfigModal?w.value=!w.value:c.value=!c.value}function u(){n("refreshData",s.activeBot.plotPair,p.usedColumns)}function g(){H.value||u()}return N(()=>i.availablePairs,()=>{i.availablePairs.find(x=>x===s.activeBot.plotPair)||([s.activeBot.plotPair]=i.availablePairs,u())}),N(()=>s.activeBot.selectedPair,()=>{s.activeBot.plotPair=s.activeBot.selectedPair}),N(()=>s.activeBot.plotPair,()=>{i.historicView?i.reloadDataOnSwitch&&g():u()}),N(()=>p.plotConfig,()=>{const x=p.usedColumns.some(f=>I.value.includes(f)&&!C.value.includes(f));l.useReducedPairCalls&&x&&u()}),N(()=>i.timeframe,()=>{g()}),Ze(()=>{c.value=i.plotConfigModal,s.activeBot.selectedPair?s.activeBot.plotPair=s.activeBot.selectedPair:i.availablePairs.length>0&&([s.activeBot.plotPair]=i.availablePairs),p.plotConfigChanged(),i.historicView||g()}),(x,f)=>{const m=Oe,T=ai,B=Se,G=mi,E=ri,z=_t,R=ui,M=pn,b=Go,W=ci;return y(),_("div",fn,[S("div",hn,[S("div",mn,[S("div",bn,[S("span",gn,K(a(r))+" | "+K(x.timeframe||""),1),v(m,{modelValue:a(s).activeBot.plotPair,"onUpdate:modelValue":f[0]||(f[0]=U=>a(s).activeBot.plotPair=U),class:"md:ms-2",options:x.availablePairs,size:"small",clearable:!1,onInput:u},null,8,["modelValue","options"]),v(B,{title:"Refresh chart",severity:"secondary",disabled:!a(s).activeBot.plotPair||a(V),size:"small",onClick:u},{default:L(()=>[v(T)]),_:1},8,["disabled"]),a(V)?(y(),Z(G,{key:0,class:"w-8 h-8","stroke-width":"8",small:"",label:"Spinning"})):D("",!0),S("div",vn,[S("div",yn,[a(d)?(y(),_("small",xn,"Long entries: "+K(a(d).enter_long_signals||a(d).buy_signals),1)):D("",!0),a(d)?(y(),_("small",On,"Long exit: "+K(a(d).exit_long_signals||a(d).sell_signals),1)):D("",!0)]),S("div",Sn,[a(d)&&a(d).enter_short_signals?(y(),_("small",In,"Short entries: "+K(a(d).enter_short_signals),1)):D("",!0),a(d)&&a(d).exit_short_signals?(y(),_("small",wn,"Short exits: "+K(a(d).exit_short_signals),1)):D("",!0)])])]),S("div",kn,[v(E,{modelValue:a(l).showMarkArea,"onUpdate:modelValue":f[1]||(f[1]=U=>a(l).showMarkArea=U)},{default:L(()=>f[4]||(f[4]=[S("span",{class:"text-nowrap"},"Show Chart Areas",-1)])),_:1,__:[4]},8,["modelValue"]),v(E,{modelValue:a(l).useHeikinAshiCandles,"onUpdate:modelValue":f[2]||(f[2]=U=>a(l).useHeikinAshiCandles=U)},{default:L(()=>f[5]||(f[5]=[S("span",{class:"text-nowrap"},"Heikin Ashi",-1)])),_:1,__:[5]},8,["modelValue"]),v(z),S("div",_n,[v(B,{size:"small",title:"Plot configurator",severity:"secondary",onClick:h},{icon:L(()=>[v(R,{width:"12",height:"12"})]),_:1})])])]),S("div",Cn,[S("div",Mn,[a(H)?(y(),Z(M,{key:0,dataset:a(d),trades:x.trades,"plot-config":a(p).plotConfig,"heikin-ashi":a(l).useHeikinAshiCandles,"show-mark-area":a(l).showMarkArea,"use-u-t-c":a(l).timezone==="UTC",theme:a(l).chartTheme,"slider-position":x.sliderPosition,"color-up":a(o).colorUp,"color-down":a(o).colorDown,"label-side":a(l).chartLabelSide},null,8,["dataset","trades","plot-config","heikin-ashi","show-mark-area","use-u-t-c","theme","slider-position","color-up","color-down","label-side"])):(y(),_("div",Ln,[a(V)?(y(),Z(G,{key:0,class:"w-5 h-5",label:"Spinning"})):(y(),_("div",Pn,K(a(k)),1)),a(s).activeBot.historyTakesLonger?(y(),_("p",Vn," This is taking longer than expected ... Hold on ... ")):D("",!0)]))]),v(bt,{name:"fade"},{default:L(()=>[x.plotConfigModal?D("",!0):yt((y(),_("div",An,[v(b,{columns:a(I),"is-visible":a(c)??!1},null,8,["columns","is-visible"])],512)),[[di,a(c)]])]),_:1})])]),x.plotConfigModal?(y(),Z(W,{key:0,id:"plotConfiguratorModal",visible:a(w),"onUpdate:visible":f[3]||(f[3]=U=>X(w)?w.value=U:null),header:"Plot Configurator","ok-only":"","hide-backdrop":""},{default:L(()=>[v(b,{"is-visible":a(w),columns:a(I)},null,8,["is-visible","columns"])]),_:1},8,["visible"])):D("",!0)])}}}),Rn=qe(Tn,[["__scopeId","data-v-950018e6"]]);export{Rn as _,It as s};
//# sourceMappingURL=CandleChartContainer-G8jE_PHU.js.map
