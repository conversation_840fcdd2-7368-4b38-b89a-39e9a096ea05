#!/usr/bin/env python3
"""
Freqtrade 简化安装脚本
适用于Python 3.13环境
"""

import subprocess
import sys
import os

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ 成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 错误: {e.stderr}")
        return False

def install_packages():
    """安装必要的包"""
    packages = [
        "numpy>=2.0.0",
        "pandas>=2.0.0", 
        "ccxt>=4.0.0",
        "aiohttp",
        "SQLAlchemy",
        "requests",
        "cryptography",
        "cachetools",
        "humanize",
        "python-telegram-bot",
        "fastapi",
        "uvicorn",
        "jinja2",
        "tabulate",
        "colorama",
        "questionary",
        "prompt-toolkit",
        "pyarrow",
        "blosc",
        "py_find_1st"
    ]
    
    print("🚀 开始安装Freqtrade依赖包...")
    
    # 升级pip
    if not run_command("py -m pip install --upgrade pip"):
        return False
    
    # 逐个安装包
    for package in packages:
        print(f"\n📦 安装 {package}...")
        if not run_command(f"py -m pip install {package}"):
            print(f"⚠️  {package} 安装失败，继续安装其他包...")
    
    print("\n✅ 基础包安装完成！")
    return True

def test_freqtrade():
    """测试Freqtrade是否可以运行"""
    print("\n🧪 测试Freqtrade...")
    
    # 尝试导入freqtrade模块
    try:
        sys.path.insert(0, os.path.join(os.getcwd(), 'freqtrade'))
        import freqtrade
        print("✅ Freqtrade模块导入成功！")
        return True
    except ImportError as e:
        print(f"❌ Freqtrade模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🤖 Freqtrade 简化安装程序")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major != 3 or python_version.minor < 11:
        print("❌ 需要Python 3.11或更高版本")
        return False
    
    # 安装包
    if not install_packages():
        print("❌ 包安装失败")
        return False
    
    # 测试Freqtrade
    if not test_freqtrade():
        print("❌ Freqtrade测试失败")
        return False
    
    print("\n🎉 Freqtrade安装完成！")
    print("\n📋 下一步:")
    print("1. 配置OKX API密钥")
    print("2. 创建交易配置文件")
    print("3. 选择交易策略")
    print("4. 开始交易")
    
    return True

if __name__ == "__main__":
    main()
