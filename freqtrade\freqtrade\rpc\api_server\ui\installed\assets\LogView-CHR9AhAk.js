import{H as w,c as r,a,e as s,d as y,u as L,r as k,o as B,F as C,m as b,x as f,z as c,n as R,f as V,b as n,g as z,h as p,y as N,t as m}from"./index-Cwqm8wBn.js";const S={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function $(l,e){return a(),r("svg",S,e[0]||(e[0]=[s("path",{fill:"currentColor",d:"M10 4h4v9l3.5-3.5l2.42 2.42L12 19.84l-7.92-7.92L6.5 9.5L10 13z"},null,-1)]))}const E=w({name:"mdi-arrow-down-thick",render:$}),T={class:"flex h-full p-0 align-items-start"},F={class:"text-surface-600 dark:text-surface-400"},H={class:"dark:text-surface-200"},I={class:"flex flex-col gap-1 ms-1"},M=y({__name:"LogViewer",setup(l){const e=L(),t=k(null);B(async()=>{i()});async function i(){await e.activeBot.getLogs(),_()}function h(u){switch(u){case"WARNING":return"text-yellow-500";case"ERROR":return"text-red-500";default:return"text-surface-500"}}function _(){t.value&&(t.value.scrollTop=t.value.scrollHeight)}return(u,W)=>{const x=N,d=z,g=E;return a(),r("div",T,[s("div",{ref_key:"scrollContainer",ref:t,class:"border border-surface-500 p-1 text-start text-sm pb-5 w-full h-full overflow-auto"},[(a(!0),r(C,null,b(V(e).activeBot.lastLogs,(o,v)=>(a(),r("pre",{key:v,class:"m-0 overflow-visible",style:{"line-height":"unset"}},[s("span",F,[f(c(o[0])+" ",1),s("span",{class:R(h(o[3]))},c(o[3].padEnd(7," ")),3),f(" "+c(o[2])+" - ",1)]),s("span",H,c(o[4]),1)]))),128))],512),s("div",I,[n(d,{id:"refresh-logs",severity:"secondary",size:"small",title:"Reload Logs",onClick:i},{icon:p(()=>[n(x)]),_:1}),n(d,{size:"small",title:"Scroll to bottom",severity:"secondary",onClick:_},{icon:p(()=>[n(g)]),_:1})])])}}}),A=m(M,[["__scopeId","data-v-b18658d6"]]),D={},G={class:"p-1 md:p-4 md:pe-2 h-full"};function O(l,e){const t=A;return a(),r("div",G,[n(t)])}const q=m(D,[["render",O]]);export{q as default};
//# sourceMappingURL=LogView-CHR9AhAk.js.map
